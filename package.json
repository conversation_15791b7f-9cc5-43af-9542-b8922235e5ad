{"name": "erp-server", "version": "1.0.0", "private": true, "scripts": {"prettier": "prettier", "commit": "commit-wizard"}, "dependencies": {"@fontsource/montserrat": "^4.5.5", "@fontsource/roboto": "^4.5.3", "tailwindcss": "^3.0.23"}, "devDependencies": {"@prettier/plugin-php": "^0.19.6", "pre-git": "^3.17.1", "prettier": "^2.8.8"}, "prettier": {"braceStyle": "psr-2", "phpVersion": "7.3", "printWidth": 120, "trailingCommaPHP": false, "tabWidth": 4, "singleQuote": true}, "release": {"analyzeCommits": "simple-commit-message"}, "config": {"pre-git": {"enabled": true, "pre-commit": [], "pre-push": ["docker exec -t erp-server-php vendor/bin/rector --no-diffs --no-ansi --no-progress-bar --dry-run"], "post-commit": [], "post-checkout": [], "post-merge": [], "allow-untracked-files": true}}}