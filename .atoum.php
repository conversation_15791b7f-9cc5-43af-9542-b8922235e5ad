<?php

$load = require __DIR__ . '/vendor/autoload.php';

/** APP PATHS */
$cache_dir = __DIR__ . '/var/cache';
$app_dir = __DIR__ . '/app';
$stamp_path = $cache_dir . '/schema_stamp';

/** LOAD ENV FILE */
// Follows naming convention of symfony
// https://symfony.com/doc/current/configuration.html#overriding-environment-values-via-env-local
$env_file = file_exists(__DIR__ . '/.env.test.local') ? __DIR__ . '/.env.test.local' : __DIR__ . '/.env.test';

(new \Symfony\Component\Dotenv\Dotenv())->load($env_file);

// IIFE
(static function () use ($env_file): void {
    // Don't load database the entire db before running tests (does not affect fixtures)
    // Please use an "env.test.local" (ignored by git) file to not potentially break things on CI
    if (filter_var($_ENV['SKIP_DB_INIT'] ?? false, FILTER_VALIDATE_BOOLEAN)) {
        echo "\n" . sprintf(' ---> SKIPPING MYSQL DB INIT (%s) <---', $env_file) . "\n\n";

        return;
    }

    /** INIT PHCDB (legacy) database **/
    $phcdb_output = [];
    exec(sprintf('./vendor/bin/phcdb.sh -v --env-file=%s schemas', $env_file), $phcdb_output);
    exec(sprintf('./vendor/bin/phcdb.sh -v --env-file=%s alterations', $env_file), $phcdb_output);
    exec(sprintf('./vendor/bin/phcdb.sh -v --env-file=%s routines', $env_file), $phcdb_output);
    exec(sprintf('./vendor/bin/phcdb.sh -v --env-file=%s dev', $env_file), $phcdb_output);
    exec(sprintf('./vendor/bin/phcdb.sh -v --env-file=%s test', $env_file), $phcdb_output);

    // Print PHCDB output
    // Set DEBUG_PHCDB to true to output phcdb commands in the terminal
    if (filter_var($_ENV['DEBUG_PHCDB'] ?? false, FILTER_VALIDATE_BOOLEAN)) {
        echo implode("\n", $phcdb_output);
    }
})();

/** PATH DB PG  */
$schema_path = __DIR__ . '/vendor/son-video/erp-pg-schema/src/schema/schema.sql';
$template_path = __DIR__ . '/vendor/son-video/erp-pg-schema/src/schema/template.sql';

/** PATH FIXTURE PG */
$fixtures_dir = __DIR__ . '/tests/fixtures/pgsql/';
$shared_fixtures_path = __DIR__ . '/vendor/son-video/erp-pg-schema/src/fixtures.sql';

/** PATH DATA_WAREHOUSE */
$data_warehouse_schema = __DIR__ . '/vendor/son-video/data-warehouse-schema/src/sql/schema.sql';

// Check if schema has changed. If not exit
if (
    file_exists($stamp_path) &&
    file_get_contents($stamp_path) ===
        getStamp($fixtures_dir, $schema_path, $shared_fixtures_path, $data_warehouse_schema) &&
    dbExists($_ENV['DATABASE_NAME_TEMPLATE'])
) {
    echo sprintf(' ---> SKIPPING PG DB INIT (%s) <---', $stamp_path) . "\n\n";

    return true;
}

/* INIT DB PG */
// Create svd_dev in order to create template db
if (!dbExists('svd_dev')) {
    insertFileInPgDb($template_path, 'postgres', $_ENV['DATABASE_HOST'], $_ENV['DATABASE_PORT'], true);
}

// Drop and create empty template database
echo sprintf('Create database: %s', $_ENV['DATABASE_NAME_TEMPLATE']) . PHP_EOL;
$command = <<<EOF
dropdb -p :port -U :user -h :host --if-exists :name \
    && createdb -p :port -U :user -h :host -T svd_dev -O svd_dev :name
EOF;
exec(
    strtr($command, [
        ':port' => $_ENV['DATABASE_PORT'],
        ':user' => 'postgres',
        ':host' => $_ENV['DATABASE_HOST'],
        ':name' => $_ENV['DATABASE_NAME_TEMPLATE'],
    ])
);
// Insert structure in template db
echo sprintf('Insert structure in %s', $_ENV['DATABASE_NAME_TEMPLATE']) . PHP_EOL;
insertFileInPgDb($schema_path, $_ENV['DATABASE_NAME_TEMPLATE'], $_ENV['DATABASE_HOST'], $_ENV['DATABASE_PORT']);

// Insert fixtures in template db
$list_files = getFixtures($fixtures_dir);
foreach ($list_files as $name => $fs_object) {
    echo sprintf('Insert file %s', $fs_object->getRealPath()) . PHP_EOL;
    insertFileInPgDb(
        $fs_object->getRealPath(),
        $_ENV['DATABASE_NAME_TEMPLATE'],
        $_ENV['DATABASE_HOST'],
        $_ENV['DATABASE_PORT']
    );
}

// Insert shared fixtures in template db
echo sprintf('Insert file %s', $shared_fixtures_path) . PHP_EOL;
insertFileInPgDb(
    $shared_fixtures_path,
    $_ENV['DATABASE_NAME_TEMPLATE'],
    $_ENV['DATABASE_HOST'],
    $_ENV['DATABASE_PORT']
);

/* INIT DATA_WAREHOUSE DB */
// create data_warehouse_test database
echo sprintf('Insert file %s', $data_warehouse_schema) . PHP_EOL;
insertFileInPgDb(
    $data_warehouse_schema,
    $_ENV['DATABASE_NAME_TEMPLATE'],
    $_ENV['DATABASE_HOST'],
    $_ENV['DATABASE_PORT']
);

/* CREATE THE STAMP */
// Set ERP schema stamp
echo sprintf('Create stamp at %s', $stamp_path) . PHP_EOL;
file_put_contents($stamp_path, getStamp($fixtures_dir, $schema_path, $shared_fixtures_path, $data_warehouse_schema));

/**
 * @return string
 */
function insertFileInPgDb(string $file, string $db_name, string $host, int $port, bool $quiet = false)
{
    $com = sprintf(
        'psql -h %s -U %s -p %d --dbname=%s < %s %s',
        $host,
        'postgres',
        $port,
        $db_name,
        $file,
        $quiet ? '&> /dev/null' : ''
    );

    return exec($com);
}

/**
 * @return false|string
 */
function insertFileInMysqlDb(
    string $file,
    string $host,
    string $db_name,
    string $user,
    string $pass,
    bool $quiet = false
) {
    $com = sprintf(
        'mysql -u%s -p%s -h %s -D %s < %s %s',
        $user,
        $pass,
        $host,
        $db_name,
        $file,
        $quiet ? '&> /dev/null' : ''
    );

    return exec($com);
}

/**
 * @return bool
 */
function dbExists(string $db_name)
{
    $res = exec(
        sprintf(
            'psql -lqt -h %s -U postgres -p %d | cut -d \| -f 1 | grep -n %s',
            $_ENV['DATABASE_HOST'],
            $_ENV['DATABASE_PORT'],
            $db_name
        )
    );

    return '' !== trim($res);
}

/**
 * @param string $file
 * @param        $file2
 * @param        $file3
 * @param        $file4
 * @param        $fixtures_dir
 *
 * @return string
 */
function getStamp($fixtures_dir, ...$schemas)
{
    $sha512 = '';
    $list_files = getFixtures($fixtures_dir);
    foreach ($list_files as $fs_object) {
        $sha512 .= exec(sprintf('sha512sum "%s"', $fs_object->getRealPath()));
    }

    foreach ($schemas as $schema) {
        $sha512 .= exec(sprintf('sha512sum "%s"', $schema));
    }

    return $sha512;
}

/**
 * Return list of fixtures files.
 *
 * @param $fixtures_dir
 *
 * @return array|RecursiveIteratorIterator
 */
function getFixtures($fixtures_dir)
{
    $list_files = new \RecursiveIteratorIterator(
        new \RecursiveRegexIterator(
            new \RecursiveDirectoryIterator($fixtures_dir, \FilesystemIterator::SKIP_DOTS),
            '#(?<!/)\.sql$|^[^\.]*$#i'
        )
    );
    $list_files = iterator_to_array($list_files);
    usort($list_files, function ($a, $b) {
        return strcmp($a->getFilename(), $b->getFilename());
    });

    return $list_files;
}
