0000000000000000000000000000000000000000 a226a638574c8f5186301c647b1dc96a74a9ef63 RomainM <<EMAIL>> 1746196568 +0200	branch: Created from origin/kpi_customer_order_payments/release^0
a226a638574c8f5186301c647b1dc96a74a9ef63 a226a638574c8f5186301c647b1dc96a74a9ef63 RomainM <<EMAIL>> 1746196807 +0200	Branch: renamed refs/heads/kpi_security_stock to refs/heads/kpi_safety_stock
a226a638574c8f5186301c647b1dc96a74a9ef63 96e2fc1247df5b0bbcc0adeafcb2158567f8ad61 RomainM <<EMAIL>> 1746435184 +0200	commit: feat[support#2359]: new synchronizable topic
96e2fc1247df5b0bbcc0adeafcb2158567f8ad61 8f50bd0675a9f0688eb8c95004dea403ccd33f87 RomainM <<EMAIL>> 1746435189 +0200	commit: feat[support#2359]: new synchronizable topic
8f50bd0675a9f0688eb8c95004dea403ccd33f87 b4b927b2dbc89460dae0c40f72ea89a0c4d205b1 RomainM <<EMAIL>> 1746435209 +0200	commit: feat[support#2359]: pomm models for kpi safety stock
b4b927b2dbc89460dae0c40f72ea89a0c4d205b1 94eb4a268f18aaf3b2a4077b4cef9ef75a746f31 RomainM <<EMAIL>> 1746435234 +0200	commit: feat[support#2359]: add new topic to synchronization
94eb4a268f18aaf3b2a4077b4cef9ef75a746f31 9477d89f2e10a6ec9a3cebd5e758114a8b81922e RomainM <<EMAIL>> 1746435741 +0200	commit (merge): Merge remote-tracking branch 'origin/kpi_customer_order_payments/release' into kpi_safety_stock
9477d89f2e10a6ec9a3cebd5e758114a8b81922e 96c75ea672b3778628505bbe24813aa02892b9f3 RomainM <<EMAIL>> 1746523819 +0200	merge refs/remotes/origin/kpi_customer_order_payments/release: Merge made by the 'ort' strategy.
96c75ea672b3778628505bbe24813aa02892b9f3 56766d7c11d96f3319fe4faa96f53a602abad2e5 RomainM <<EMAIL>> 1746523898 +0200	commit: feat[support#2359]: synchronize topic
56766d7c11d96f3319fe4faa96f53a602abad2e5 7782e0a6b0706da9e1c3db0aa16e06cd166e03ae RomainM <<EMAIL>> 1746524838 +0200	rebase (continue) (finish): refs/heads/kpi_safety_stock onto 72acdb1dba7ea3eef2618b478bb902ddecd62260
7782e0a6b0706da9e1c3db0aa16e06cd166e03ae 6e8dccc0f233e5b7a36a63a211526a30d3c58adc RomainM <<EMAIL>> 1746524851 +0200	merge origin/kpi_security_stock: Merge made by the 'ort' strategy.
6e8dccc0f233e5b7a36a63a211526a30d3c58adc 6be5c2682412b045ac55a16e25d13752f13e020b RomainM <<EMAIL>> 1746628240 +0200	commit: feat[support#2359]: redo model entity and repository using orm
6be5c2682412b045ac55a16e25d13752f13e020b f00a29089cc35a6be61ac91134816e6aff42449c RomainM <<EMAIL>> 1746628306 +0200	commit: feat[support#2359]: composer
f00a29089cc35a6be61ac91134816e6aff42449c 6298e3fcaa4446736af6e83669bc8e571276fe48 RomainM <<EMAIL>> 1746628349 +0200	commit: feat[support#2359]: using new model instead of pomm
6298e3fcaa4446736af6e83669bc8e571276fe48 70fa4ed1f3361e9b3b599b1e5aa09e9e1fdaecc2 RomainM <<EMAIL>> 1746631243 +0200	commit (merge): Merge branch 'kpi_customer_order_payments/release' into kpi_safety_stock
70fa4ed1f3361e9b3b599b1e5aa09e9e1fdaecc2 35a386f3503bf334ca530c989b19bdb7e1ed3dc6 RomainM <<EMAIL>> 1747300127 +0200	merge refs/heads/master: Merge made by the 'ort' strategy.
35a386f3503bf334ca530c989b19bdb7e1ed3dc6 c0138403a601d5a854873d65fac4ccea9530e598 RomainM <<EMAIL>> 1747312063 +0200	commit: feat[support#2359]: add test
c0138403a601d5a854873d65fac4ccea9530e598 8772370125abc80a88ed374171cd0fe2e32e24d8 RomainM <<EMAIL>> 1747315756 +0200	merge kpi_safety_stock_release: Merge made by the 'ort' strategy.
8772370125abc80a88ed374171cd0fe2e32e24d8 a9d15d3590d669dfaf4b5a03e5a1735a2a81e4e5 RomainM <<EMAIL>> 1747318964 +0200	commit: feat[support#2359]: composer
a9d15d3590d669dfaf4b5a03e5a1735a2a81e4e5 4621bb0042fd28de3a3eb05e9e5ab31d9a008e53 RomainM <<EMAIL>> 1747660880 +0200	commit (merge): Merge branch 'master' into kpi_safety_stock
4621bb0042fd28de3a3eb05e9e5ab31d9a008e53 a6eabd3bd494c3902daa762825637f464d628797 RomainM <<EMAIL>> 1747662743 +0200	commit: feat[support#2359]: feedback review
