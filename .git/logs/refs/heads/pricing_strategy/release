ac291080ddaeeb54a7730507e7ad772f047629ba bc3ba912aace34a924cb596be159f3408a4f9da8 RomainM <romain.mabil<PERSON>@son-video.com> 1737384743 +0100	merge origin/pricing_strategy/release: Fast-forward
bc3ba912aace34a924cb596be159f3408a4f9da8 3a76ebf9e8697e1e4bdd296de913aa96bc34cb02 RomainM <<EMAIL>> 1737384863 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/release
3a76ebf9e8697e1e4bdd296de913aa96bc34cb02 b350a90320183bbbaaa806078a5d46c644cfe2f4 RomainM <<EMAIL>> 1737385829 +0100	commit: feat[support#2299]: composer
b350a90320183bbbaaa806078a5d46c644cfe2f4 ba5ebc0eaa9af0f3cd7d8c541b9ee0babb8bf401 RomainM <<EMAIL>> 1737386067 +0100	commit: feat[support#2299]: composer
ba5ebc0eaa9af0f3cd7d8c541b9ee0babb8bf401 c89b36c0e1e062716490c4e5f91e0f80626a4f0d RomainM <<EMAIL>> 1737392021 +0100	commit: feat[support#2299]: composer
c89b36c0e1e062716490c4e5f91e0f80626a4f0d 409dc4fe0c2367e70da8425238e757a113535ba2 RomainM <<EMAIL>> 1737399177 +0100	commit: feat[support#2299]: remove var_dump
409dc4fe0c2367e70da8425238e757a113535ba2 b41b18c7db9df5b6d53c9acb256b802e74f6baf1 RomainM <<EMAIL>> 1737400089 +0100	commit: feat[support#2299]: add methode to replace inTransaction in legacyPdo
b41b18c7db9df5b6d53c9acb256b802e74f6baf1 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740406665 +0100	merge origin/pricing_strategy/release: Fast-forward
e0ef4cce81045081cdb91c820e175a9b5aeaee89 0e8198b1bcfaedd9c41c5c3d17f3a40a94e03317 RomainM <<EMAIL>> 1740652901 +0100	fetch origin refs/heads/pricing_strategy/release:refs/heads/pricing_strategy/release --recurse-submodules=no --progress --prune: fast-forward
0e8198b1bcfaedd9c41c5c3d17f3a40a94e03317 08051f53041c8a68e18adeb34b06079e88f53cb2 RomainM <<EMAIL>> 1740996503 +0100	merge origin/pricing_strategy/release: Fast-forward
08051f53041c8a68e18adeb34b06079e88f53cb2 7b3c9e5983ca576d673d93fcff83675172a32613 RomainM <<EMAIL>> 1741099580 +0100	fetch origin refs/heads/pricing_strategy/release:refs/heads/pricing_strategy/release --recurse-submodules=no --progress --prune: fast-forward
7b3c9e5983ca576d673d93fcff83675172a32613 99216547a724c3832dd690b911463584bc55bf89 RomainM <<EMAIL>> 1741277375 +0100	fetch origin refs/heads/pricing_strategy/release:refs/heads/pricing_strategy/release --recurse-submodules=no --progress --prune: fast-forward
99216547a724c3832dd690b911463584bc55bf89 7025573dad66684bc9258949660e057dd2c0e2c7 RomainM <<EMAIL>> ********** +0100	merge origin/pricing_strategy/release: Fast-forward
7025573dad66684bc9258949660e057dd2c0e2c7 f861623cc1325381439e77c9422d17e3b63aab1d RomainM <<EMAIL>> ********** +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/release
f861623cc1325381439e77c9422d17e3b63aab1d 0c8c5e7b6049e38a8147679c0d147c641386c052 RomainM <<EMAIL>> ********** +0100	commit: add uuid for healthcheck
