# Read the documentation: https://github.com/1up-lab/OneupFlysystemBundle/tree/master/Resources/doc/index.md
oneup_flysystem:
    adapters:
        default_adapter:
            local:
                lazy: true
                directory: '%kernel.cache_dir%/../tmp'
        uploads_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/uploads/s3'
        cilo_ftp_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/cilo/ftp'
        cilo_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/cilo/s3'
        wiser_ftp_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/wiser/ftp'
        wiser_ftp_svd_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/wiser/ftp-svd'
        wiser_ftp_easylounge_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/wiser/ftp-ezl'
        wiser_backup_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/wiser/backup'
        gfk_ftp_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/gfk/ftp'
        sticker_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/sticker/s3'
        legacy_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/legacy/s3'
        legacy_france_express_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/tests/fixtures/files/dispatch_notes'
        svd_statics_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/svd-statics/s3'
        mock_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/tests/fixtures/files'
        mondial_relay_ftp_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/mondial_relay/ftp'
        mondial_relay_s3_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/mondial_relay/s3'
        group_digital_ftp_adapter:
            local:
                lazy: true
                directory: '%kernel.project_dir%/var/test/group_digital/ftp'
    filesystems:
        mock_filesystem:
            adapter: mock_adapter
            alias: mock
            mount: mock_filesystem
