monolog:
    handlers:
        main_debug:
            type: rotating_file
            channels: [ '!pheanstalk' ]
            max_files: 3
            path: '%kernel.logs_dir%/%kernel.environment%.debug.log'
            level: debug

        main_info:
            type: rotating_file
            channels: [ '!pheanstalk' ]
            max_files: 3
            path: '%kernel.logs_dir%/%kernel.environment%.info.log'
            level: info

        main_error:
            type: rotating_file
            max_files: 3
            path: '%kernel.logs_dir%/%kernel.environment%.error.log'
            level: error

        console:
            type: console
            process_psr_3_messages: false
            channels: [ '!event', '!doctrine', '!console', '!pheanstalk', '!php' ]
