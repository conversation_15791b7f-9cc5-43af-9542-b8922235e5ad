sentry:
    dsn: '%env(SENTRY_DSN)%'
    register_error_listener: false # Disables the ErrorListener
    register_error_handler: false
    options:
        # important! , The gzip compression make the request to fails on sentry and is slow as hell as a bonus
        enable_compression: false
        environment: '%env(SERVER_ENV)%'
        send_default_pii: true
        tags:
            machine: '%env(MACHINE_NAME)%'
    tracing:
        dbal:
            # sentryBundle's TracingStatementForV2 does not implement fetchAllAssociative() which is required by
            # LegacyPdo::fetchCollection()
            enabled: false
