monolog:
    handlers:
        # Rotating files logs are only used in dev environment therefore we don.t need to keep them for longer than 1-2 days
        main:
            type: rotating_file
            max_files: 2
            path: '%kernel.logs_dir%/%kernel.environment%.info.log'
            level: debug
        main_error:
            type: rotating_file
            max_files: 2
            path: '%kernel.logs_dir%/%kernel.environment%.error.log'
            level: error
        main_critical:
            type: rotating_file
            max_files: 2
            path: '%kernel.logs_dir%/%kernel.environment%.critical.log'
            level: critical

        # Buffer all logs until an error has been reached
        buffered:
            type: buffer
            level: error
            handler: sentry

        # Message below error levels should be registered as breadcrumb in sentry
        finger_crossed:
            type: fingers_crossed
            level: error
            handler: sentry_breadcrumb
            buffer_size: 50 # How many messages should be saved? Prevent memory leaks

        # Sentry logging handler
        sentry:
            type: service
            id: App\Sentry\MonologHandler
            channels: [ '!sentry', '!pheanstalk', '!security', '!request' ]

        sentry_breadcrumb:
            type: service
            id: App\Sentry\MonologBreadcrumbHandler
            channels: [ '!sentry', '!pheanstalk', '!security', '!request', '!php' ]

        # uncomment to get logging in your browser
        # you may have to allow bigger header sizes in your Web server configuration
        #firephp:
        #    type: firephp
        #    level: info
        #chromephp:
        #    type: chromephp
        #    level: info
        console:
            type: console
            process_psr_3_messages: false
            channels: [ '!event', '!doctrine', '!console', '!pheanstalk', '!php' ]
