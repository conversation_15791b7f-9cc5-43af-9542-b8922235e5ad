<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WMS - Emplacements par rack</title>
    <link href="style.css" rel="stylesheet" type="text/css">

    <style>
        html, body {
            font-size: 14px;
            min-height: 100vh;
        }

        .qr-code {
            width: 1.5cm;
        }

        .label {
            letter-spacing: .05rem;
            font-size: 3.6rem;
        }

        .long-text {
            font-size: 1.5rem;
        }

        .page-break-after {
            page-break-after: always;
        }

        .location-item {
            max-height: 34%;
        }

        .separator {
            border-bottom: 1px dashed #afafaf;
        }
    </style>
</head>
<body>
{% if columns|length > 0 %}
    {% for column in columns %}
        <div class="flex flex-col p-1 h-screen {% if loop.last == false %}page-break-after{% endif %}">
            {% for location in column %}
                <div class="flex mb-1 items-center justify-center w-full flex-1 pl-5 pr-5 location-item {{ cycle(['separator', ''], loop.last and loop.index == 3) }}">
                    <span class="font-semibold uppercase label{% if location.label|length > 16 %} long-text{% endif %}">{{ location.label }}</span>
                    <img src="data:image/png;base64,{{ location.qrcode }}" class="qr-code ml-auto"/>
                </div>
            {% endfor %}
        </div>
    {% endfor %}
{% endif %}
</body>
</html>
