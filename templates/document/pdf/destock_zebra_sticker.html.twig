<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>DESTOCK</title>
    <link href="style.css" rel="stylesheet" type="text/css">

    <style>
        html, body {
            font-size: 20px;
            min-height: 100vh;
        }

        .qr-code {
            width: 4cm;
        }

        .bar-code {
            width: 5cm;
            height: 3cm;
        }

        .label {
            letter-spacing: .05rem;
            font-size: 3.4rem;
        }

        .separator {
            border-bottom: 2px solid #000000;
        }

        .border {
            border: #1b1b1b 2px solid;
        }

        .justify-between {
            justify-content: space-between;
        }
    </style>
</head>
<body>
{% if result %}
     <div class="flex flex-col p-3 h-screen">
         <span class=" font-semibold uppercase" >{{ result.brand }}</span>
         <span class="font-semibold uppercase" >{{ result.name }}</span>
         <span></span>
         <div class="separator"></div>
         <div class="flex h-full items-center justify-between">
            <img src="data:image/png;base64,{{ result.qrcode }}" class="qr-code" />
            <img src="data:image/png;base64,{{ result.bar_code }}" class="bar-code" />
         </div>
         <span class="flex w-full justify-center mb-3">{{ result.code_128 }}</span>
         <span class="flex w-full justify-center mb-3">{{ result.sku }}</span>
         <div class="border label">DESTOCK</div>
     </div>
{% endif %}
</body>
</html>
