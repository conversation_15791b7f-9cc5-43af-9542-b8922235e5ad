#!/bin/bash
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
source ${DIR}/environment
SCRIPT="deployer:after-install"

cd ${DEPLOY_DIR}

if [ ! -e .env ];
then
    ln -s ${TARGET_DIR}/shared/config/.env .env;
    echo "Created symbolic link to the .env" | logger --tag="${SCRIPT}"
fi

mkdir -p ${DEPLOY_DIR}/var/tmp
mkdir -p ${DEPLOY_DIR}/var/log
mkdir -p ${DEPLOY_DIR}/var/cache
chown -R svd_admin:nginx ${DEPLOY_DIR}/var
setfacl -R -m u:nginx:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var
setfacl -dR -m u:nginx:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var
setfacl -R -m u:svd_admin:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var
setfacl -dR -m u:svd_admin:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var

chmod +x ${DEPLOY_DIR}/bin/cron.sh

php bin/console cache:clear --env=prod

[ -d "${ROLLBACK_DIR}" ] && rm -rf ${ROLLBACK_DIR};
mv ${PROJECT_DIR} ${ROLLBACK_DIR};
mv ${DEPLOY_DIR} ${PROJECT_DIR};

# vim: set ft=sh
