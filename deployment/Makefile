PACKAGE_NAME=erp-server
PACKAGE_DIR=$(PACKAGE_NAME)
ZONE_STG=eu-west-2
ZONE_VAL=eu-west-2
ZONE_PRD=eu-central-1

OK_COLOR=\033[32;01m
NO_COLOR=\033[0m

.PHONY: deploy-staging
deploy-staging: deployment-staging.id

.PHONY: deploy-validation
deploy-validation: deployment-validation.id

deployment-staging.id:
	# Prepare package
	@if [ ! -f $(PACKAGE_NAME).tar ]; \
	then \
	     make $(PACKAGE_NAME).tar \
	else \
		printf " $(OK_COLOR)-- [SKIPPING CREATING TAR]$(NO_COLOR) $(PACKAGE_NAME).tar already exists \n"; \
	fi
	git tag -l "v[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.-]' '/^v/{print $$1"."$$2"."$$3+1}' > version
	tar -uf $(PACKAGE_NAME).tar version
	gzip $(PACKAGE_NAME).tar
	# Move/Copy package to S3
	aws --output=text --region=$(ZONE_STG) s3 cp \
	    $(PACKAGE_NAME).tar.gz \
	    s3://svd-deployer-stg/$(PACKAGE_NAME)-ec2.tar.gz \
	    --metadata "GitVersion=$(shell git rev-parse HEAD)"
	# Trigger code deploy
	aws --output=text --region=$(ZONE_STG) deploy create-deployment \
	    --application-name $(PACKAGE_NAME) \
	    --deployment-group-name $(PACKAGE_NAME)-stg \
	    --s3-location="bundleType=tgz,bucket=svd-deployer-stg,key=$(PACKAGE_NAME)-ec2.tar.gz"
	# Tag release
	$(eval VERSION := $(shell git tag -l "v[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.-]' '/^v/{print $$1"."$$2"."$$3+1}')-staging)
	git tag $(VERSION)
	git push --tags --no-verify
	echo $(VERSION) > deployment-staging.id

deployment-validation.id:
	# Prepare package
	@if [ ! -f $(PACKAGE_NAME).tar ]; \
	then \
	     make $(PACKAGE_NAME).tar \
	else \
		printf " $(OK_COLOR)-- [SKIPPING CREATING TAR]$(NO_COLOR) $(PACKAGE_NAME).tar already exists \n"; \
	fi
	git tag -l "validation.[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.]' '/^validation/{print $$1"."$$2+1}' > version
	tar -uf $(PACKAGE_NAME).tar version
	gzip $(PACKAGE_NAME).tar
	# Move/Copy package to S3
	aws --output=text --region=$(ZONE_VAL) s3 cp \
	    $(PACKAGE_NAME).tar.gz \
	    s3://svd-deployer-val/$(PACKAGE_NAME).tar.gz \
	    --metadata "GitVersion=$(shell git rev-parse HEAD)"
	# Trigger code deploy
	aws --output=text --region=$(ZONE_VAL) deploy create-deployment \
	    --application-name $(PACKAGE_NAME) \
	    --deployment-group-name $(PACKAGE_NAME)-val \
	    --s3-location="bundleType=tgz,bucket=svd-deployer-val,key=$(PACKAGE_NAME).tar.gz"
	# Tag release
	$(eval VERSION := $(shell git tag -l "validation.[0-9]*" | sort --version-sort -r | head -1 | awk -F'[.]' '/^validation/{print $$1"."$$2+1}'))
	git tag $(VERSION)
	git push --tags --no-verify
	echo $(VERSION) > deployment-validation.id

.PHONY: package
package: $(PACKAGE_NAME).tar

$(PACKAGE_NAME).tar: $(PACKAGE_DIR)/vendor/autoload.php
	tar cf $(PACKAGE_NAME).tar --exclude-from=deploy-scripts/ignore.list --exclude-vcs $(PACKAGE_NAME) deploy-scripts appspec.yml

.PHONY: composer
composer: $(PACKAGE_DIR)/vendor/autoload.php

$(PACKAGE_DIR)/vendor/autoload.php: $(PACKAGE_DIR)/composer.json
	# comment the following line if you need to deploy manually
	cd $(PACKAGE_DIR) ; composer install --no-dev --no-scripts --no-interaction --optimize-autoloader
	# uncomment the following line if you need to deploy manually
	# additionally you will need to run the following command : "composer install --no-dev --no-scripts --no-interaction --optimize-autoloader"
	# in phcstack erp-server composer
	# cd $(PACKAGE_DIR);

$(PACKAGE_DIR)/composer.json:
	rm -rf $(PACKAGE_DIR)
	mkdir $(PACKAGE_DIR)
	cp -r ../bin ../config ../public ../src ../templates $(PACKAGE_DIR)/
	cp ../composer.* ../symfony.lock $(PACKAGE_DIR)/
	# uncomment the following line if you need to deploy manually
	# cp -R ../vendor $(PACKAGE_DIR)/.

.PHONY: clean
clean:
	rm -rf deployment-staging.id deployment-validation.id $(PACKAGE_NAME).tar $(PACKAGE_NAME).tar.gz version $(PACKAGE_DIR) || exit 0
