<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Eav\Repository;

use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Referential\ArticleStatus;

/**
 * Class SubcategoryEavReadRepository.
 */
class SubcategoryEavReadRepository extends AbstractLegacyRepository
{
    /**
     * Retrieve skus of articles that are active and related to the subcategory.
     *
     * @return array Array of article skus
     */
    public function findActiveAssignedArticleSkus(int $subcategory_id): array
    {
        $sql = <<<SQL
        SELECT
          p.reference AS sku
        FROM
          backOffice.produit   p
          INNER JOIN backOffice.article a ON p.id_produit = a.id_produit
        WHERE p.id_souscategorie = :subcategory_id
          AND a.status in ('oui', 'last');
        SQL;

        return array_column(
            $this->legacy_pdo->fetchObjects($sql, [
                'subcategory_id' => $subcategory_id,
                'status_oui' => ArticleStatus::OUI,
                'status_last' => ArticleStatus::LAST,
            ]),
            'sku'
        );
    }

    /** @return false|object */
    public function findIdFromArticleSku(string $sku)
    {
        $sql = <<<SQL
        SELECT
          reference AS sku,
          id_souscategorie AS subcategory_id
        FROM backOffice.produit
        WHERE reference = :sku;
        SQL;

        $data = $this->legacy_pdo->fetchOne($sql, ['sku' => $sku]);

        return $data ? (object) $data : false;
    }
}
