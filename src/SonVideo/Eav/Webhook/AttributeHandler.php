<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Eav\Webhook;

/**
 * Class AttributeHandler.
 */
class AttributeHandler extends AttributeValueHandler
{
    protected const HANDLED_EVENTS = ['eav.attribute.update'];

    /** {@inheritDoc} */
    protected function processAttributes(array $payload): array
    {
        $attribute_id = $payload['event']['data']['new']['attribute_id'];

        // retrieve linked product values
        $product_values = $this->product_value_model->findSkusByAttributeId($attribute_id);
        if ([] === $product_values) {
            return [
                'message' => 'No product use this attribute, nothing to do.',
            ];
        }

        // retrieve eavs for skus
        $skus = array_map(static fn (array $pv) => $pv['sku'], $product_values);
        $articles = $this->product_value_model->getAttributesBySkus($skus);

        // synchronize
        return $this->batchSynchronizeArticlesAttributes($articles);
    }
}
