<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Eav\Webhook;

/**
 * Class SubcategoryHandler.
 */
class SubcategoryHandler extends AbstractEavWebhookHandler
{
    protected const HANDLED_EVENTS = ['eav.subcategory.insert', 'eav.subcategory.update', 'eav.subcategory.manual'];

    /**
     * {@inheritDoc}
     *
     * @return array{facets: string[], attributes: array{message: string}}
     */
    public function process(array $payload): array
    {
        return [
            'facets' => $this->processFacets($payload),
            'attributes' => ['message' => 'Nothing to do on this event.'],
        ];
    }

    /**
     * Push the updates for eav facets.
     *
     * @return string[]
     */
    protected function processFacets(array $payload): array
    {
        $use_filter = $payload['event']['data']['new']['use_filters'];
        $subcategory_id = $payload['event']['data']['new']['subcategory_id'];
        if (!$use_filter) {
            return $this->removeSubcategoryFacets($subcategory_id);
        }

        return $this->updateSubcategoryAndRelatedFacets($subcategory_id);
    }

    /** @return array */
    protected function removeSubcategoryFacets(int $subcategory_id)
    {
        $response = $this->rpc_client->call('bo-cms', static::RPC_DEACTIVATE_METHOD, [$subcategory_id]);

        return $response['result'];
    }
}
