<?php

namespace SonVideo\Erp\Payment\Manager;

use App\Adapter\Serializer\SerializerInterface;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderPaymentCreationContextDto;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\Payment\Client\PaymentV2ClientInterface;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationPayload;
use SonVideo\Erp\Referential\PaymentApiV2;
use SonVideo\Erp\Referential\PaymentWorkflow;

class PaymentV2Creator implements PaymentV2CreatorInterface
{
    private PaymentV2ClientInterface $client;

    private SerializerInterface $serializer;

    public function __construct(PaymentV2ClientInterface $client, SerializerInterface $serializer)
    {
        $this->client = $client;
        $this->serializer = $serializer;
    }

    /** @param CustomerOrderPaymentCreationContextDto[] $payments */
    private function buildPayload(array $payments): ?PaymentV2CreationPayload
    {
        return array_map(
            static fn (
                CustomerOrderPaymentCreationContextDto $payment
            ): ?PaymentV2CreationPayload => $payment->getPaymentV2VerifiedPayment(),
            array_filter(
                $payments,
                static fn (CustomerOrderPaymentCreationContextDto $payment): bool => PaymentWorkflow::V2 ===
                    $payment->workflow
            )
        )[0] ?? null;
    }

    private function call(int $customer_order_id, array $payments): ?array
    {
        $creation_payload = $this->buildPayload($payments);

        return $this->client->post(
            PaymentApiV2::CREATE_URL,
            array_merge(
                ['customer_order_id' => $customer_order_id],
                $creation_payload instanceof PaymentV2CreationPayload
                    ? $this->serializer->normalize($creation_payload)
                    : []
            )
        );
    }

    public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
    {
        return $this->call($customer_order_id, $order_context->payments);
    }

    public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
    {
        return $this->call($request_context->customer_order_id, $request_context->payments);
    }
}
