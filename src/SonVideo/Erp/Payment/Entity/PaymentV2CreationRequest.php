<?php

namespace SonVideo\Erp\Payment\Entity;

use OpenApi\Annotations as OA;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderPaymentCreationContextDto;

class PaymentV2CreationRequest
{
    public string $code;

    public float $amount;

    /** @OA\Property(type="object") */
    public array $extra_data = [];

    public ?string $external_reference = null;

    /** Payment V2 expect some keys mapped differently */
    public static function fromCreationContextDto(CustomerOrderPaymentCreationContextDto $dto): self
    {
        $mapped_instance = new self();
        $mapped_instance->code = $dto->payment_mean;
        $mapped_instance->amount = $dto->amount;
        $mapped_instance->extra_data = $dto->extra_data;
        $mapped_instance->external_reference = $dto->external_reference;

        return $mapped_instance;
    }
}
