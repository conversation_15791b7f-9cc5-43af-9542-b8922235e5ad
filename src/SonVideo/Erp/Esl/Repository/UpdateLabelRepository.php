<?php

namespace SonVideo\Erp\Esl\Repository;

use App\Sql\LegacyReadonlyPdo;

class UpdateLabelRepository
{
    private LegacyReadonlyPdo $legacy_pdo;

    public function __construct(LegacyReadonlyPdo $legacy_pdo)
    {
        $this->legacy_pdo = $legacy_pdo;
    }

    public function findProducts(int $warehouse_id): array
    {
        $sql = <<<SQL
        SELECT
            p.reference AS id,
            a.prix_vente AS price,
            CONCAT(m.marque, " ", a.modele) AS full_name,
            sc.souscategorie AS sub_cat,
            a.prix_ecotaxe AS deee,
            a.garantie_constructeur AS warranty,
            CASE
                WHEN c.highlight = "sale" OR product.is_on_sales THEN "SOLDES"
                WHEN c.highlight = "exclusive" THEN "EXCLU"
                WHEN c.highlight = "promotion" THEN "PROMO"
                ELSE ""
            END AS highlight,
            PDT_code128C(a.id_produit) AS code_128,
            COALESCE(PDT_ART_px_grt5(a.id_produit), '') AS gld_premium,
            CONCAT(a.V_qte_dispo_resa, ':', UPPER(LEFT(a.status, 1)), '-', DATE_FORMAT(NOW(), '%y%m%d-%H:%i')) AS timestamp,
            p.reference AS sku,
            -- original_price for destock without sales is parent price
            COALESCE(IF(product.is_on_sales, NULL, ap.prix_vente), scp.reference_price) AS original_price,
            PDT_ART_ean(a.id_produit) AS ean_13,
            a.status as status
            FROM (
                -- PRODUCT AND DESTOCK
                SELECT a.id_produit AS id, sp.actif = 1 AS is_on_sales
                FROM backOffice.article a
                INNER JOIN backOffice.BO_STK_produit_depot bspd ON a.id_produit = bspd.id_produit
                LEFT JOIN backOffice.solde_produit sp ON a.id_produit = sp.id_produit AND backOffice.SLD_actif(sp.id_solde)
                WHERE bspd.quantite_stock > 0 AND bspd.id_depot = :warehouse_id
                -- PRODUCT FROM DESTOCK
                UNION
                SELECT a.stock_a_id_produit AS id, FALSE AS is_on_sales
                FROM backOffice.article a
                INNER JOIN backOffice.BO_STK_produit_depot bspd ON a.id_produit = bspd.id_produit
                WHERE bspd.quantite_stock > 0 AND bspd.id_depot = :warehouse_id
            ) product
            INNER JOIN backOffice.article a ON a.id_produit = product.id
            INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
            INNER JOIN backOffice.CTG_TXN_souscategorie sc ON p.id_souscategorie = sc.id
            LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
            LEFT JOIN backOffice.batch_catalog c ON COALESCE(a.stock_a_id_produit, a.id_produit) = c.article_id
            LEFT JOIN backOffice.sales_channel_product scp ON scp.product_id = a.id_produit AND scp.is_active AND scp.sales_channel_id = 1
            LEFT JOIN backOffice.article ap ON ap.id_produit = a.stock_a_id_produit
            GROUP BY p.reference
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['warehouse_id' => $warehouse_id]);
    }
}
