<?php

namespace SonVideo\Erp\Metabase\Manager;

use Firebase\JWT\JWT;

/**
 * Class MetabaseUrlGenerator.
 */
class MetabaseUrlGenerator
{
    protected string $site_origin;
    protected string $secret_key;

    public function __construct(string $site_origin, string $secret_key)
    {
        $this->site_origin = $site_origin;
        $this->secret_key = $secret_key;
    }

    public function createForEmbedDashboard(int $dashboard, array $params): string
    {
        $payload = [
            'resource' => [
                'dashboard' => $dashboard,
            ],
            'params' => (object) $params, // cast to object is required to avoid metabase issue when array is empty
            'exp' => (new \DateTime())->getTimestamp() + 10 * 60, // 10 minutes expiration
        ];

        $jwt = JWT::encode($payload, $this->secret_key, 'HS256');

        return sprintf('%s/embed/dashboard/%s', $this->site_origin, $jwt);
    }
}
