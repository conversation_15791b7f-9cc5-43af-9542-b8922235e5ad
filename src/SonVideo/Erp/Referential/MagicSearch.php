<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Referential;

class MagicSearch
{
    public const ALL_INDEX = 'all';
    public const ARTICLE_INDEX = 'articles';
    public const CUSTOMER_INDEX = 'customers';

    public const SYSTEM_VARIABLE_START_AT = 'magic_search.incremental_update.start_at';
    public const SYSTEM_VARIABLE_STOP_AT = 'magic_search.incremental_update.stop_at';
}
