<?php

namespace SonVideo\Erp\Referential;

use Symfony\Component\Validator\Constraints as Assert;

class SystemEventRelationship
{
    public static function validationRules(): array
    {
        $key = ['supplier', 'article', 'quote', 'warehouse', 'customer', 'invoice', 'delivery_note', 'customer_order'];
        $rules = [];

        foreach ($key as $value) {
            $rules[$value] = new Assert\Optional([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Type('integer'),
            ]);
        }

        return $rules;
    }
}
