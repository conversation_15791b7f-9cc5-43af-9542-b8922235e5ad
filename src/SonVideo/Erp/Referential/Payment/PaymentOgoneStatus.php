<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Referential\Payment;

/**
 * Class PaymentOgoneStatus.
 */
class PaymentOgoneStatus
{
    public const ACCEPTED = 'accepte';
    public const REMITTED = 'remise';
    public const REFUSED = 'refuse';
    public const CANCELED = 'refuse';
    public const AWAITING = 'attente';

    public const STATUSES = [
        '5' => self::ACCEPTED,
        '91' => self::ACCEPTED,
        '9' => self::REMITTED,
        '0' => self::REFUSED,
        '2' => self::REFUSED,
        '52' => self::REFUSED,
        '93' => self::REFUSED,
        '92' => self::REFUSED,
        '1' => self::CANCELED,
        '6' => self::CANCELED,
        '7' => self::CANCELED,
        '64' => self::CANCELED,
        '74' => self::CANCELED,
        '75' => self::CANCELED,
        '41' => self::AWAITING,
        '51' => self::AWAITING,
        '61' => self::AWAITING,
        '71' => self::AWAITING,
        '81' => self::AWAITING,
    ];
}
