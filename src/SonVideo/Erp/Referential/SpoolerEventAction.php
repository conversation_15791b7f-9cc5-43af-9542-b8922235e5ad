<?php

namespace SonVideo\Erp\Referential;

final class SpoolerEventAction
{
    public const CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT = 'customer_order.update.last_modified_at';
    public const CUSTOMER_ORDER_UPDATE_ESTIMATED_DELIVERY_TIME = 'customer_order.update.estimated_delivery_time';
    public const CUSTOMER_ORDER_PRODUCT_UPDATE_ESTIMATED_DELIVERY_TIME = 'customer_order_product.update.estimated_delivery_time';

    /** BEWARE: The order of the events below is important */
    public const DEQUEUEABLE_ORDERED_EVENTS = [
        self::CUSTOMER_ORDER_PRODUCT_UPDATE_ESTIMATED_DELIVERY_TIME,
        self::CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT,
    ];
}
