<?php

namespace SonVideo\Erp\Accounting\Payment\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

class PaymentRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function findForTxtExport(array $transaction_ids): array
    {
        $sql = <<<SQL
            SELECT
                IF(
                    pa.moyen = 'CTPE', pc.acceptation_date, coalesce(pp.creation_date, pc.creation_date)
                ) AS created_at,
                pc.type AS type,
                CASE
                    WHEN (pa.moyen = 'CRTAT' OR pa.moyen = 'CHKF') THEN ('CHK')
                    WHEN (pa.moyen = 'PPAL' OR pa.moyen = 'PPAL MAN') THEN ('PPAL')
                    ELSE (pa.moyen)
                    END
                AS payment_mean,
                CASE
                    WHEN (pa.compte_bancaire = '512000' AND pa.moyen = 'CHK') THEN ('512300')
                    ELSE (pa.compte_bancaire)
                    END
                AS bank_account,
                CASE
                    WHEN (pa.journal = 'BQ' AND (pa.moyen = 'CHK' OR pa.moyen = 'DARTY')) THEN ('CE')
                    ELSE (pa.journal)
                    END
                AS journal,
                pc.remise_montant AS remit_amount,
                RIGHT(
                    CONCAT('0000000', c.id_prospect), 7
                ) AS client_nb,
                UPPER(
                    FORMAT_string_whithout_accent(
                        IF(
                            CASE WHEN (pr.cnt_societe = '') THEN (CONCAT(pr.cnt_nom, ' ', pr.cnt_prenom))
                            ELSE (pr.cnt_societe)
                            END != '',
                            CASE WHEN (pr.cnt_societe = '') THEN (CONCAT(pr.cnt_nom, ' ', pr.cnt_prenom))
                            ELSE (pr.cnt_societe)
                            END,
                            CASE WHEN (c.cnt_fct_societe = '') THEN (CONCAT(c.cnt_fct_nom, ' ', c.cnt_fct_prenom))
                            ELSE (c.cnt_fct_societe)
                            END
                        )
                    )
                ) AS client,
                CASE
                    WHEN (pr.type = 'entreprise') THEN ('411100')
                    ELSE ('411000')
                    END
                AS account,
                IF(
                    pa.moyen = 'CTPE', pc.acceptation_date, pc.remise_date
                ) AS remit_date,
                pc.remise_taux AS rate,
                c.id_commande AS order_id,
                IF(
                    pa.moyen = 'CTPE', d.nom_depot, null
                ) AS warehouse,
                (SELECT
                    MIN(id_facture)
                    FROM backOffice.facture
                    WHERE id_commande = pc.id_commande
                    GROUP BY id_commande
                ) AS invoice_id,
                ABS(COALESCE(pcwp.commission_amount, 0)) as commission_amount
            FROM backOffice.commande c
                INNER JOIN backOffice.prospect pr ON c.id_prospect = pr.id_prospect
                INNER JOIN backOffice.paiement_commande pc ON c.id_commande = pc.id_commande
                INNER JOIN backOffice.paiement pa ON pc.id_paiement = pa.id_paiement
                LEFT JOIN backOffice.paiement_commande_tpe_report pctr ON pc.id = pctr.paiement_commande_id
                LEFT JOIN backOffice.BO_STK_depot d ON pc.warehouse_id = d.id
                LEFT JOIN paiements.paiement pp ON pp.id_trans_client = pc.creation_justificatif COLLATE latin1_general_ci
                LEFT JOIN backOffice.paiement_commande_worldline_payout pcwp ON
                    pcwp.external_ref = pc.remise_justificatif
                    AND (pc.creation_justificatif = pcwp.payment_creation_proof
                             OR pc.creation_justificatif = pcwp.formatted_external_id)
            WHERE 1
                AND (c.creation_origine != 'ecranlounge.com' OR c.date_creation < '2022-01-21')
                AND pa.avoir = 'N'
                AND pc.id IN(:transaction_ids)
            ORDER BY payment_mean, type, warehouse ASC, remit_date ASC
        SQL;

        return $this->legacy_pdo->fetchAll($sql, [
            'transaction_ids' => $transaction_ids,
        ]);
    }

    public function findForCsvExport(array $transaction_ids): array
    {
        $sql = <<<SQL
            SELECT
                c.date_creation                             AS customer_order_created_at,
                pc.id_commande                              AS customer_order_id,
                c.id_prospect                               AS customer_id,
                CONCAT(
                    UPPER(
                        TRIM(c.cnt_fct_nom)
                    ), ' ', TRIM(c.cnt_fct_prenom)
                )                                           AS customer_name,
                FORMAT(pc.acceptation_montant, 2, 'fr_FR')  AS accepted_amount,
                pc.acceptation_date                         AS accepted_at,
                p.moyen                                     AS payment_mean,
                pc.remise_justificatif                      AS accepted_proof,
                d.nom_depot                                 AS warehouse_name
               FROM backOffice.paiement_commande pc
                 INNER JOIN backOffice.paiement p ON pc.id_paiement = p.id_paiement
                 INNER JOIN backOffice.commande c ON pc.id_commande = c.id_commande
                 LEFT JOIN backOffice.BO_STK_depot d ON pc.warehouse_id = d.id
            WHERE 1
                AND pc.id IN(:transaction_ids)
            ORDER BY date_creation ASC
        SQL;

        return $this->legacy_pdo->fetchAll($sql, [
            'transaction_ids' => $transaction_ids,
        ]);
    }
}
