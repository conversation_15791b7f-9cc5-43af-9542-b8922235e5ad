<?php

namespace SonVideo\Erp\Accounting\Payment\Helper;

use App\Exception\InternalErrorException;
use League\Csv\Exception;
use League\Csv\InvalidArgument;
use League\Csv\Reader;
use League\Csv\Statement;
use SonVideo\Erp\Referential\InternalError;

class CSVDataExtractor
{
    public const EXTRACTION_LIMIT = 500;
    public const FETCH_LIMIT = 2000; // extraction limit * 4

    private const COLUMN_MAPPING = [
        'order_id' => 'CMD',
        'amount_to_remit' => 'MONTANT',
        'remit_proof' => 'JUSTIF',
    ];

    /** @var Reader */
    private $reader;

    /**
     * @throws InvalidArgument
     * @throws Exception
     */
    public function setReader(string $file_contents): void
    {
        $this->reader = Reader::createFromString($file_contents);

        $this->reader->setHeaderOffset(0);
        $this->reader->setDelimiter(';');
    }

    /** @throws InternalErrorException|Exception */
    public function extract(bool $remit_payments): array
    {
        $this->preflight($remit_payments);

        $orders = [];
        $stmt = Statement::create()
            ->offset(0)
            ->limit(self::EXTRACTION_LIMIT);

        $records = $stmt->process($this->reader);
        foreach ($records as $record) {
            $order_id = trim($record[self::COLUMN_MAPPING['order_id']]);
            $orders[$order_id][] = [
                'amount_to_remit' => empty($record[self::COLUMN_MAPPING['amount_to_remit']])
                    ? false
                    : number_format(
                        str_replace([',', ' '], ['.', ''], trim($record[self::COLUMN_MAPPING['amount_to_remit']])),
                        2,
                        '.',
                        ''
                    ),
                'remit_proof' => empty($record[self::COLUMN_MAPPING['remit_proof']])
                    ? ''
                    : trim($record[self::COLUMN_MAPPING['remit_proof']]),
            ];
        }

        return $orders;
    }

    /** @throws InternalErrorException */
    private function preflight(bool $remit_payments): void
    {
        $header = $this->reader->getHeader();
        if (
            !in_array(self::COLUMN_MAPPING['order_id'], $header) ||
            ($remit_payments && !in_array(self::COLUMN_MAPPING['amount_to_remit'], $header))
        ) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('CSVDataExtractor: The file header is invalid.'), ['error' => 'incomplete_header']);
        }

        if ($this->reader->count() > self::EXTRACTION_LIMIT) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException(sprintf('CSVDataExtractor: The file cannot contain more than %d records.', self::EXTRACTION_LIMIT)), ['error' => 'number_of_records_exceeded']);
        }
    }
}
