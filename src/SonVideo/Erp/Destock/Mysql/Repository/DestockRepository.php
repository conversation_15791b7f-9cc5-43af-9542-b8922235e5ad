<?php

namespace SonVideo\Erp\Destock\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Article\Contract\ArticleUpdateRelatedRepositoryInterface;

class DestockRepository extends AbstractLegacyRepository implements ArticleUpdateRelatedRepositoryInterface, LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'article_id' => 'da.article_id',
        'destock_assignment' => 'da.assignment',
        'destock_defects' => 'da.defects',
        'destock_origin' => 'da.origin',
        'destock_state' => 'da.state',
        'internal_comment' => 'da.internal_comment',
        'public_description' => 'da.public_description',
    ];

    /** @throws NotFoundException */
    public function getOneById(int $article_id, ?array $columns = null): array
    {
        $columns ??= array_keys(self::COLUMNS_MAPPING);

        $sql = <<<SQL
            SELECT {projection}
            FROM backOffice.article_destock da
            WHERE da.article_id = :article_id
        SQL;

        $projection_lines = array_map(static function ($column): string {
            if (!isset(self::COLUMNS_MAPPING[$column])) {
                throw new \Exception(sprintf('Column %s not exists', $column));
            }

            return self::COLUMNS_MAPPING[$column] . ' AS ' . $column;
        }, $columns);

        $sql = strtr($sql, [
            '{projection}' => "\n" . implode(", \n", $projection_lines) . "\n",
        ]);

        $result = $this->legacy_pdo->fetchOne($sql, ['article_id' => $article_id]);

        if (false === $result) {
            throw new NotFoundException(sprintf('No destock found with id %d.', $article_id));
        }

        $result['destock_defects'] = array_filter(explode(',', $result['destock_defects']));

        return $result;
    }

    public function update(int $article_id, array $data): int
    {
        if (isset($data['destock_defects']) && is_array($data['destock_defects'])) {
            $data['destock_defects'] = implode(',', $data['destock_defects']);
        }

        $statement = array_map(function ($value): string {
            if (!isset(self::COLUMNS_MAPPING[$value])) {
                throw new \Exception(sprintf('Column %s not exists', $value));
            }

            return sprintf('%s = :%s', self::COLUMNS_MAPPING[$value], $value);
        }, array_keys($data));

        $sql = strtr('UPDATE backOffice.article_destock da SET {statement} WHERE da.article_id = :article_id', [
            '{statement}' => "\n" . implode(", \n", $statement) . "\n",
        ]);

        return $this->legacy_pdo->fetchAffected($sql, array_merge($data, ['article_id' => $article_id]));
    }
}
