<?php

namespace SonVideo\Erp\DataWarehouse\Repository\Mysql;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Database\ConnectionProvider\MysqlErpConnectionProvider;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrderInitialLine;

class CustomerOrderInitialForDataWarehouseRepository implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    private MysqlErpConnectionProvider $connection_provider;

    public function __construct(MysqlErpConnectionProvider $connection_provider)
    {
        $this->connection_provider = $connection_provider;
    }

    public function findByIdForDataWarehouse(int $customer_order_id): array
    {
        $sql = <<<'MYSQL'
        SELECT
          copi.customer_order_product_initial_id,
          c.date_creation AS customer_order_created_at,
          p.reference,
          CONVERT(p.type USING utf8) AS product_type,
          CONVERT(a.modele USING utf8) AS model,
          CONVERT(m.marque USING utf8) AS brand,
          CONVERT(f.fournisseur USING utf8) AS supplier,
          copi.quantity,
          copi.purchase_price * copi.quantity AS total_purchase_cost,
          copi.selling_price_tax_included * copi.quantity / (1 + copi.vat) AS total_gross_excl_tax,
          copi.discount_amount * copi.quantity / (1 + copi.vat) AS total_discount_excl_tax,
          (copi.selling_price_tax_included * copi.quantity + copi.discount_amount) / (1 + copi.vat) AS total_net_excl_tax,
          copi.warranties_price * copi.quantity / (1 + copi.vat) AS total_guarantee_excl_tax,
          (copi.selling_price_tax_included * copi.quantity + copi.discount_amount) / (1 + copi.vat) -
          copi.purchase_price * copi.quantity AS total_margin,
          cts.id AS subcategory_id,
          CONVERT(cts.souscategorie USING utf8) AS subcategory,
          ctc.id_categorie AS category_id,
          CONVERT(ctc.categorie USING utf8) AS category,
          ctd.id AS domain_id,
          CONVERT(ctd.domaine USING utf8) AS domain,
          copi.customer_order_id,
          copi.vat AS vat_rate,
          copi.promo_code,
          copi.available_quantity AS available_quantity,
          copi.estimated_supplier_order_delivery_date AS estimated_supplier_order_delivery_date
          FROM
            data_warehouse.customer_order_product_initial copi
              INNER JOIN backOffice.article a ON copi.product_id = a.id_produit
              INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
              LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
              LEFT JOIN backOffice.fournisseur f ON a.id_fournisseur = f.id_fournisseur
              INNER JOIN backOffice.commande c ON copi.customer_order_id = c.id_commande
              INNER JOIN backOffice.CTG_TXN_souscategorie cts ON p.id_souscategorie = cts.id
              INNER JOIN backOffice.CTG_TXN_categorie ctc ON cts.dft_categorie_id = ctc.id_categorie
              INNER JOIN backOffice.CTG_TXN_domaine ctd ON ctc.dft_domaine_id = ctd.id
        WHERE copi.customer_order_id = :customer_order_id
        MYSQL;

        return $this->serializer->denormalize(
            $this->connection_provider
                ->getConnection()
                ->fetchAllAssociative($sql, ['customer_order_id' => $customer_order_id]),
            CustomerOrderInitialLine::class . '[]'
        );
    }
}
