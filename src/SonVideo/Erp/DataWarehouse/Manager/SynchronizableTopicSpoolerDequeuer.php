<?php

namespace SonVideo\Erp\DataWarehouse\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Database\Orm\MysqlErp\Repository\Entity\SynchronizableTopic;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicReadRepository;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicWriteRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\DataWarehouse\Collection\TopicSynchronizerCollection;
use SonVideo\Erp\DataWarehouse\Contract\SynchronizableTopicFeedbackHandlerInterface;
use SonVideo\Erp\DataWarehouse\Contract\TopicSynchronizerInterface;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicDequeueParameters;

class SynchronizableTopicSpoolerDequeuer implements SerializerAwareInterface, LoggerAwareInterface
{
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    private SynchronizableTopicWriteRepository $synchronizable_topic_write_repository;
    private SynchronizableTopicReadRepository $synchronizable_topic_repository;
    private TopicSynchronizerCollection $topic_synchronizer_collection;

    public function __construct(
        SynchronizableTopicWriteRepository $synchronizable_topic_write_repository,
        SynchronizableTopicReadRepository $synchronizable_topic_repository,
        TopicSynchronizerCollection $topic_synchronizer_collection
    ) {
        $this->synchronizable_topic_write_repository = $synchronizable_topic_write_repository;
        $this->topic_synchronizer_collection = $topic_synchronizer_collection;
        $this->synchronizable_topic_repository = $synchronizable_topic_repository;
    }

    public function dequeue(
        SynchronizableTopicFeedbackHandlerInterface $synchronizable_topic_feedback_handler,
        SynchronizableTopicDequeueParameters $parameters = null
    ): void {
        // Use default parameters if none provided
        if (!$parameters instanceof SynchronizableTopicDequeueParameters) {
            $parameters = new SynchronizableTopicDequeueParameters();
        }

        // Lock columns
        $this->synchronizable_topic_write_repository->lockForSynchronization(
            $this->synchronizable_topic_repository->findHowManyRowsAreLocked(),
            $parameters
        );

        // Prepare feedback
        $synchronizable_topic_feedback_handler->start(
            $this->synchronizable_topic_repository->findAllTopicsTopicForFeedback()
        );
        $synchronizable_topic_feedback_handler->progress(null);

        // Dispatch topics to their respective handlers
        /** @var SynchronizableTopic $synchronizable_topic */
        foreach ($this->synchronizable_topic_repository->findAllLockedToSynchronize() as $synchronizable_topic) {
            /** @var AbstractSynchronizableTopicContent $consolidated_synchronizable_topic */
            $consolidated_synchronizable_topic = $this->serializer->denormalize(
                $synchronizable_topic->consolidated(),
                AbstractSynchronizableTopicContent::class
            );
            $feedback = $synchronizable_topic_feedback_handler->getByTopic($consolidated_synchronizable_topic->topic);

            try {
                /** @var TopicSynchronizerInterface $handler */
                $handler = $this->topic_synchronizer_collection->getHandler($consolidated_synchronizable_topic->topic);

                if (!$handler instanceof TopicSynchronizerInterface) {
                    throw new \RuntimeException(sprintf('No handler found for topic "%s"', $consolidated_synchronizable_topic->topic));
                }

                // synchronize
                $handler->synchronize($consolidated_synchronizable_topic);

                // cleanup
                $this->synchronizable_topic_write_repository->removeSynchronizedTopic(
                    $consolidated_synchronizable_topic->synchronizable_topic_id
                );

                $synchronizable_topic_feedback_handler->progress($feedback->ok());
            } catch (\Exception $exception) {
                $this->logger->warning($exception->getMessage());
                $this->synchronizable_topic_write_repository->markAsAttempted(
                    $consolidated_synchronizable_topic->synchronizable_topic_id
                );

                $synchronizable_topic_feedback_handler->progress($feedback->failure());
            }
        }

        $synchronizable_topic_feedback_handler->end();
    }
}
