<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\DataWarehouse\Contract\TopicSynchronizerInterface;

abstract class AbstractTopicSynchronizer implements TopicSynchronizerInterface, SerializerAwareInterface, LoggerAwareInterface
{
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    protected const TOPIC = self::class;

    public function canHandle(string $key): bool
    {
        if (self::class === static::TOPIC) {
            throw new \RuntimeException(sprintf('You must define a TOPIC constant in the child class "%s"', static::class));
        }

        return static::TOPIC === $key;
    }
}
