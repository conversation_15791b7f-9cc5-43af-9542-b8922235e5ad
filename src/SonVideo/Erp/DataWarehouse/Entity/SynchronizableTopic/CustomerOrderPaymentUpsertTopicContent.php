<?php

namespace SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;

use Ramsey\Uuid\UuidInterface;

class CustomerOrderPaymentUpsertTopicContent extends AbstractSynchronizableTopicContent
{
    public int $customer_order_payment_id;
    public int $customer_order_id;
    public string $code;
    public string $label;
    public string $type;
    public string $amount;
    public ?string $payment_country_code = null;
    public ?string $invoice_country_code = null;
    public ?UuidInterface $operation_id = null;
}
