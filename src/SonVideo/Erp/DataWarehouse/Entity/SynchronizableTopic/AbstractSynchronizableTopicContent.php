<?php

namespace SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;

use Symfony\Component\Serializer\Annotation\DiscriminatorMap;

/**
 * Note: do not shorten the FQCN as rector wil unfortunately remove the imported namespace.
 *
 * @DiscriminatorMap(typeProperty="topic", mapping={
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT=CustomerOrderInitialUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT=CustomerOrderPaymentStatusUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_UPSERT=CustomerOrderPaymentUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_UPSERT=CustomerOrderUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::SAFETY_STOCK_UPSERT=SafetyStockUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT=SupplierContractUpsertTopicContent::class,
 * })
 */
abstract class AbstractSynchronizableTopicContent
{
    public int $synchronizable_topic_id;
    public string $topic;
}
