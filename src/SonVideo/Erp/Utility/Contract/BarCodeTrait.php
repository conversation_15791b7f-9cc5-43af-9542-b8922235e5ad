<?php

namespace SonVideo\Erp\Utility\Contract;

use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeGenerator;
use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeGeneratorPNG;
use <PERSON><PERSON><PERSON><PERSON>\Barcode\Exceptions\BarcodeException;

trait BarCodeTrait
{
    /** @throws BarcodeException */
    protected function getBarCode(string $from): string
    {
        $generator = new BarcodeGeneratorPNG();
        $bar_code = $generator->getBarcode($from, BarcodeGenerator::TYPE_CODE_128_B);

        return base64_encode($bar_code);
    }
}
