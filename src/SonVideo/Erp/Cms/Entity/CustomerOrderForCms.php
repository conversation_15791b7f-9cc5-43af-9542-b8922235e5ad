<?php

namespace SonVideo\Erp\Cms\Entity;

use App\DataLoader\Type\CommaSeparatedListType;
use App\DataLoader\Type\JsonType;

final class CustomerOrderForCms
{
    public int $customer_order_id;
    public int $customer_id;
    public \DateTimeInterface $created_at;
    public \DateTimeInterface $modified_at;
    public string $flux;
    /** @var CommaSeparatedListType|null */
    public ?array $in_progress_flags = null;
    public string $origin;
    public string $status;
    public float $total_price;
    public float $total_price_vat_excluded;
    public float $ecotax_price;
    public float $shipping_price;
    public ?int $shipment_method_id = null;
    public ?string $relay_id = null;
    public ?int $store_pick_up_id = null;
    public ?int $quote_id = null;
    public ?\DateTimeInterface $preparation_email_sent_at = null;
    /** @var array|JsonType */
    public array $shipping_address = [];
    /** @var array|JsonType */
    public array $billing_address;
    /** @var array|JsonType */
    public array $products;
    /** @var array|JsonType */
    public array $payments;
    /** @var array|JsonType */
    public array $tags = [];
}
