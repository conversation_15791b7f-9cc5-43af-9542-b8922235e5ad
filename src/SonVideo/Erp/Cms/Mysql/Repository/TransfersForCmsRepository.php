<?php

namespace SonVideo\Erp\Cms\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Cms\Entity\TransferForCms;
use SonVideo\Erp\Referential\Product;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class TransfersForCmsRepository implements LegacyPdoAwareInterface, SerializerAwareInterface, LoggerAwareInterface
{
    use LegacyPdoAwareTrait;
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    /**
     * @return TransferForCms[]
     *
     * @throws ExceptionInterface
     */
    public function fetchForCmsCustomerOrder(int $customer_order_id): array
    {
        $sql = <<<'MYSQL'
        SELECT
          t.id AS transfer_id,
          t.statut AS status,
          t.date_creation AS created_at,
          t.date_cloture AS closed_at,
          MAX(bl.date_validation) AS shipped_at,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'product_id', pt.id_produit,
              'quantity', pt.quantite,
              'delivered_quantity', pt.quantite_livree
            )
          ) AS products
        FROM
          backOffice.BO_STK_transfert t
            INNER JOIN backOffice.BO_STK_produit_transfert pt ON pt.id_transfert = t.id
            INNER JOIN backOffice.commande c ON t.id_commande = c.id_commande
            LEFT JOIN backOffice.bon_livraison bl ON t.id = bl.id_transfert
        WHERE t.id_commande = :customer_order_id
          AND t.id_depot_arrivee = c.depot_emport
          AND pt.id_produit NOT IN (:delivery_product_id, :catalog_product_id, :magazine_product_id)
        GROUP BY t.id
        ;
        MYSQL;

        return $this->serializer->denormalize(
            $this->legacy_pdo->fetchAll($sql, [
                'customer_order_id' => $customer_order_id,
                'delivery_product_id' => Product::SHIPMENT_PRODUCT_ID,
                'catalog_product_id' => Product::CATALOG_PRODUCT_ID,
                'magazine_product_id' => Product::MAGAZINE_PRODUCT_ID,
            ]),
            TransferForCms::class . '[]'
        );
    }
}
