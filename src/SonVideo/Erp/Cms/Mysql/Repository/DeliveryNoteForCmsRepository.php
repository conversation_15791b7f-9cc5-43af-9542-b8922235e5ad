<?php

namespace SonVideo\Erp\Cms\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Cms\Entity\DeliveryNoteForCms;
use SonVideo\Erp\Referential\Product;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class DeliveryNoteForCmsRepository implements LegacyPdoAwareInterface, SerializerAwareInterface, LoggerAwareInterface
{
    use LegacyPdoAwareTrait;
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    /**
     * @return DeliveryNoteForCms[]
     *
     * @throws ExceptionInterface
     */
    public function fetchForCmsCustomerOrder(int $customer_order_id): array
    {
        $sql = <<<'MYSQL'
        SELECT
          bl.id_bon_livraison AS delivery_note_id,
          IF(t.zone = 'SVD MAG', 5, bl.id_transporteur) AS carrier_id,
          CAST(CONVERT_TZ(bl.date_creation, 'SYSTEM', '+00:00') AS CHAR) AS created_at,
          CAST(CONVERT_TZ(bl.date_validation, 'SYSTEM', '+00:00') AS CHAR) AS shipped_at,
          CAST(CONVERT_TZ(delivery.delivered_at, 'SYSTEM', '+00:00') AS CHAR) AS delivered_at,
          bl.status,
          JSON_ARRAYAGG(
            JSON_OBJECT(
              'product_id', pbl.id_produit,
              'quantity', pbl.quantite
            )
          ) AS products,
          COALESCE(parcel.parcels, JSON_ARRAY()) AS parcels
        FROM
          backOffice.bon_livraison bl
            INNER JOIN backOffice.transporteur t ON bl.id_transporteur = t.id_transporteur
            INNER JOIN backOffice.produit_bon_livraison pbl ON bl.id_bon_livraison = pbl.id_bon_livraison
            LEFT JOIN (
              SELECT
                c.id_bon_livraison AS delivery_note_id,
                JSON_ARRAYAGG(
                  JSON_OBJECT(
                    'parcel_id', c.id_colis,
                    'tracking_number', c.no_colis,
                    'tracking_url', backOffice.get_tracking_url(c.id_colis),
                    'tracks', COALESCE(tracking.tracks, JSON_ARRAY())
                  )
                ) AS parcels
                FROM
                  backOffice.bon_livraison bl
                    INNER JOIN backOffice.colis c ON bl.id_bon_livraison = c.id_bon_livraison
                    INNER JOIN  backOffice.commande co ON co.id_commande = bl.id_commande
                    INNER JOIN backOffice.BO_TPT_PDT_liste pt ON backOffice.shipment_method_havre_to_champigny(bl.id_pdt_transporteur) = pt.id
                    LEFT JOIN (
                      SELECT
                        cl.id_colis,
                        JSON_ARRAYAGG(
                          JSON_OBJECT(
                            'status', CASE cs.statut
                                WHEN 'en cours'
                                  THEN 'ON_GOING'
                                WHEN 'livre_retard'
                                  THEN 'LATE_DELIVERY'
                                WHEN 'livre'
                                  THEN 'DELIVERED'
                                WHEN 'perdu'
                                  THEN 'LOST'
                                WHEN 'casse'
                                  THEN 'BROKEN'
                                WHEN 'incident'
                                  THEN 'ISSUE'
                                END,
                            'status_date', COALESCE(cs.created_at, cs.statut_date),
                            'description', CONVERT(cs.description USING utf8)
                          )
                        ) AS tracks
                      FROM backOffice.commande c
                        INNER JOIN backOffice.bon_livraison bl ON c.id_commande = bl.id_commande
                        INNER JOIN backOffice.colis cl ON bl.id_bon_livraison = cl.id_bon_livraison
                        INNER JOIN backOffice.colis_statut cs ON cl.id_colis = cs.id_colis
                      WHERE c.id_commande = :customer_order_id
                        AND cs.type != 'INTERNAL'
                        AND cs.statut IN ('en cours', 'livre')
                      GROUP BY cl.id_colis
                    ) tracking ON c.id_colis = tracking.id_colis
                WHERE co.id_commande = :customer_order_id
                GROUP BY c.id_bon_livraison
            ) parcel ON parcel.delivery_note_id = bl.id_bon_livraison
            LEFT JOIN (
              SELECT
                c.id_bon_livraison AS delivery_note_id,
                MAX(COALESCE(cs.created_at, cs.statut_date)) AS delivered_at
                FROM
                  backOffice.bon_livraison bl
                    INNER JOIN backOffice.colis c ON bl.id_bon_livraison = c.id_bon_livraison
                    INNER JOIN backOffice.colis_statut cs ON c.id_colis = cs.id_colis
                    INNER JOIN backOffice.commande co ON co.id_commande = bl.id_commande
                WHERE co.id_commande = :customer_order_id
                    AND cs.statut = 'livre'
                GROUP BY c.id_bon_livraison
            ) delivery ON delivery.delivery_note_id = bl.id_bon_livraison
        WHERE bl.id_commande = :customer_order_id
          AND pbl.id_produit NOT IN (:delivery_product_id, :catalog_product_id, :magazine_product_id)
        GROUP BY bl.id_bon_livraison
        ;
        MYSQL;

        return $this->serializer->denormalize(
            $this->legacy_pdo->fetchAll($sql, [
                'customer_order_id' => $customer_order_id,
                'delivery_product_id' => Product::SHIPMENT_PRODUCT_ID,
                'catalog_product_id' => Product::CATALOG_PRODUCT_ID,
                'magazine_product_id' => Product::MAGAZINE_PRODUCT_ID,
            ]),
            DeliveryNoteForCms::class . '[]'
        );
    }
}
