<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Filesystem\Manager;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use SonVideo\Erp\Filesystem\Contract\CreateOrOverwriteTrait;

class MondialRelayS3
{
    use CreateOrOverwriteTrait;

    public const FILESYSTEM = 'mondial_relay_s3_filesystem';
    public const TRACKING_DIR = '/tracking';
    public const TRACKING_SUCCESS = '/success';
    public const TRACKING_FAILURE = '/failure';

    /** @var FilesystemInterface */
    protected $filesystem;

    /** MondialRelayS3 constructor. */
    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(self::FILESYSTEM);
    }

    public function getFilesystem(): FilesystemInterface
    {
        return $this->filesystem;
    }

    /**
     * @param resource $stream
     *
     * @return bool True on success, false on failure
     *
     * @throws FileExistsException
     */
    public function backupTrackingFile(string $filename, $stream): bool
    {
        return $this->filesystem->writeStream($filename, $stream);
    }

    /**
     * Move the order file $filename from inbox to succes directory.
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function moveTrackingToSuccessDir(string $filename): bool
    {
        return $this->filesystem->rename($filename, sprintf('%s/%s', self::TRACKING_SUCCESS, $filename));
    }

    /**
     * Move the order file $filename from inbox to failure directory.
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function moveTrackingToFailureDir(string $filename): bool
    {
        return $this->filesystem->rename($filename, sprintf('%s/%s', self::TRACKING_FAILURE, $filename));
    }

    /** @throws FileNotFoundException */
    public function readTrackingToProcess(string $filename): string
    {
        return $this->filesystem->read($filename);
    }
}
