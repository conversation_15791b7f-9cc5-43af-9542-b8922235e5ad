<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Manager\Payment;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Client\OgoneApiClient;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository;
use SonVideo\Erp\Referential\Payment\PaymentOgoneStatus;
use SonVideo\Erp\Repository\PaymentCorrectiveReadRepository;
use SonVideo\Erp\Repository\PaymentCorrectiveWriteRepository;
use SonVideo\Erp\Repository\PaymentWriteRepository;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;

class OgoneStatusManager implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const OGONE_SECOND_ACCOUNT = ['CBS-OT', 'CBS-O2', 'CBS-EMPT', 'CBS-COT', 'AMX-OT', 'CGA-OT', 'AUR-OT'];

    private OgoneApiClient $ogone_api_client;
    private CustomerOrderPaymentReadRepository $order_repository;
    private PaymentCorrectiveWriteRepository $payment_corrective_write_repository;
    private PaymentCorrectiveReadRepository $payment_corrective_read_repository;
    private CustomerOrderPaymentWriteRepository $order_write_repository;
    private PaymentWriteRepository $payment_write_repository;

    /** OgoneStatusManager constructor. */
    public function __construct(
        OgoneApiClient $ogone_api_client,
        CustomerOrderPaymentReadRepository $order_read_repository,
        TaskRepository $task_repository,
        PaymentCorrectiveWriteRepository $payment_corrective_write_repository,
        PaymentCorrectiveReadRepository $payment_corrective_read_repository,
        CustomerOrderPaymentWriteRepository $order_write_repository,
        PaymentWriteRepository $payment_write_repository
    ) {
        $this->ogone_api_client = $ogone_api_client;
        $this->order_repository = $order_read_repository;
        $this->payment_corrective_write_repository = $payment_corrective_write_repository;
        $this->payment_corrective_read_repository = $payment_corrective_read_repository;
        $this->order_write_repository = $order_write_repository;
        $this->payment_write_repository = $payment_write_repository;
    }

    /** check */
    public function check(): void
    {
        // fetch paiement_commande without autoreponse
        $payments_without_autoresponse = $this->order_repository->fetchOgonePaymentsWithoutAutoresponse();

        // fetch status info from Ogone API
        foreach ($payments_without_autoresponse as $payment) {
            try {
                $account = in_array($payment->payment_mean, self::OGONE_SECOND_ACCOUNT)
                    ? OgoneApiClient::ACCOUNT2
                    : OgoneApiClient::ACCOUNT1;

                $result = $this->ogone_api_client->call($payment->creation_proof, $account);
                $xml = \simplexml_load_string($result);
                $ncerror = (int) $xml->xpath('./@NCERROR')[0];

                // If no response from Ogone, send task to user in charge of payments)
                if (8 === strlen($ncerror)) {
                    continue;
                }

                // Payment already has a corrective
                if ($this->payment_corrective_read_repository->exists($payment->creation_proof)) {
                    continue;
                }

                // Payment does not exists in "paiements" database
                if (null === $payment->payment_id) {
                    $this->payment_write_repository->upsert($payment->creation_proof);

                    // Reload payment with all required data for corrective
                    $payment = $this->order_repository->fetchOgonePaymentFromCreationProof($payment->creation_proof);

                    // This should never happen - we still provide a fallback if it ever happen anyway
                    if (null === $payment->payment_id) {
                        continue;
                    }
                }

                // update payment (with paiements.correctif) + refresh customer order
                $data = $this->formatForCorrection($payment, $this->formatXml($xml));
                $this->payment_corrective_write_repository->create($data);
                $this->order_write_repository->refresh($payment->creation_proof);
            } catch (\Exception $exception) {
                $this->logger->error($exception->getMessage(), ['exception' => $exception]);
            }
        }
    }

    /**
     * formatXml.
     *
     * @return array{order_id: string, payid: string, payidsub: string, ncstatus: string, ncerrorplus: string, acceptance: string, status: string, ipcty: string, cccty: string, eci: string, cvccheck: string, aavcheck: string, vc: string, amount: string, currency: string, pm: string, brand: string, cardno: string, ip: string}
     */
    protected function formatXml(\SimpleXMLElement $xml): array
    {
        return [
            'order_id' => (string) $xml->xpath('./@orderID')[0],
            'payid' => (string) $xml->xpath('./@PAYID')[0],
            'payidsub' => (string) $xml->xpath('./@PAYIDSUB')[0],
            'ncstatus' => (string) $xml->xpath('./@NCSTATUS')[0],
            'ncerrorplus' => (string) $xml->xpath('./@NCERRORPLUS')[0],
            'acceptance' => (string) $xml->xpath('./@ACCEPTANCE')[0],
            'status' => (string) $xml->xpath('./@STATUS')[0],
            'ipcty' => (string) $xml->xpath('./@IPCTY')[0],
            'cccty' => (string) $xml->xpath('./@CCCTY')[0],
            'eci' => (string) $xml->xpath('./@ECI')[0],
            'cvccheck' => (string) $xml->xpath('./@CVCCheck')[0],
            'aavcheck' => (string) $xml->xpath('./@AAVCheck')[0],
            'vc' => (string) $xml->xpath('./@VC')[0],
            'amount' => (string) $xml->xpath('./@amount')[0],
            'currency' => (string) $xml->xpath('./@currency')[0],
            'pm' => (string) $xml->xpath('./@PM')[0],
            'brand' => (string) $xml->xpath('./@BRAND')[0],
            'cardno' => (string) $xml->xpath('./@CARDNO')[0],
            'ip' => (string) $xml->xpath('./@IP')[0],
        ];
    }

    /**
     * formatForCorrection.
     *
     * @param $payment
     */
    protected function formatForCorrection($payment, array $xml_formatted): array
    {
        $data = [
            'payment_id' => $payment->payment_id,
            'id_trans_banque' => sprintf('OGN%s', $xml_formatted['payid']),
            'amount' => $xml_formatted['amount'],
            'payment_means' => $payment->payment_mean,
            'status' => PaymentOgoneStatus::STATUSES[$xml_formatted['status']],
            'status_detail' => sprintf('Import manuel (STATUS : %s)', $xml_formatted['status']),
            'card_number' => $xml_formatted['cardno'],
            'bank' => 'ogone',
            'currency' => 'euro',
            'correction_date' => date('Y-m-d', strtotime('now')),
            'creation_date' => date('Y-m-d', strtotime('now')),
        ];

        if (PaymentOgoneStatus::ACCEPTED === $data['status']) {
            $data['accepted_date'] = date('Y-m-d', strtotime('now'));
        }
        if (PaymentOgoneStatus::CANCELED === $data['status'] || PaymentOgoneStatus::REFUSED === $data['status']) {
            $data['cancel_date'] = date('Y-m-d', strtotime('now'));
        }
        if (PaymentOgoneStatus::REMITTED === $data['status']) {
            $data['remit_date'] = date('Y-m-d', strtotime('now'));
        }

        return $data;
    }
}
