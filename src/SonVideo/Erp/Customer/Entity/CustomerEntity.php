<?php

namespace SonVideo\Erp\Customer\Entity;

use App\Entity\AbstractEntity;

/**
 * Class CustomerEntity.
 */
class CustomerEntity extends AbstractEntity
{
    public int $customer_id;

    public string $email_address;

    public string $type;

    public string $civility;

    public string $firstname;

    public string $lastname;

    public ?string $company_name = null;

    public string $address;

    public string $zip_code;

    public int $country_id;

    public string $phone;

    public string $mobile_phone;

    public string $customer_type;

    public bool $is_blacklisted;

    public bool $accept_marketing_emails;

    public \DateTimeInterface $created_at;

    public \DateTimeInterface $modified_at;

    public ?\DateTimeInterface $birthdate = null;

    public string $computed_name;

    public string $country_name;

    public string $country_code;

    public bool $has_ongoing_premium_warranty;

    public ?CustomerOrdersAggregatesEntity $customer_orders_aggregates = null;

    public ?CustomerCreditNotesAggregatesEntity $credit_notes_aggregates = null;

    public ?CustomerNewsletterSummaryEntity $newsletter = null;

    public float $encours_interne;

    public float $encours_sfac;

    public ?string $tva_number = null;

    public ?string $classification = null;

    public bool $balance_acceptance;

    public ?string $atradius = 'Pas soumis';

    public ?string $incoterm = null;

    public bool $npai;
}
