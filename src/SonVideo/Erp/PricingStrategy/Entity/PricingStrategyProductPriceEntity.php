<?php

namespace SonVideo\Erp\PricingStrategy\Entity;

use SonVideo\Erp\Article\Dto\UpdateContext\TriggeredPriceUpdateContextDto;

class PricingStrategyProductPriceEntity
{
    public array $sales_channel;

    public float $selling_price_tax_excluded;

    public float $margin;

    public float $margin_rate;

    public float $current_margin_rate;
    /** the margin rate if we matched the selling price of our cheapest competitor */
    public float $margin_rate_if_cheapest;

    public float $current_selling_price_tax_included;

    public float $selling_price_tax_included;
    public float $selling_price_if_cheapest;

    public ?TriggeredPriceUpdateContextDto $price_update_context = null;
}
