<?php

namespace SonVideo\Erp\PricingStrategy\Manager;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Params\FiltersParams;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyProductEntity;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyProductRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyRepository;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;

class PricingStrategyProductManager
{
    private PricingStrategyProductRepository $pricing_strategy_product_repository;
    private PricingStrategyRepository $pricing_strategy_repository;
    private ArticleReadRepository $article_read_repository;
    private QueryBuilder $query_builder;

    public function __construct(
        PricingStrategyProductRepository $pricing_strategy_product_repository,
        PricingStrategyRepository $pricing_strategy_repository,
        ArticleReadRepository $article_read_repository,
        QueryBuilder $query_builder
    ) {
        $this->pricing_strategy_product_repository = $pricing_strategy_product_repository;
        $this->pricing_strategy_repository = $pricing_strategy_repository;
        $this->article_read_repository = $article_read_repository;
        $this->query_builder = $query_builder;
    }

    public function getFilteredCollection(FiltersParams $params): Pager
    {
        $query_builder = $this->query_builder
            ->setWhere($params->getFilters() ?? [])
            ->setOrderBy($params->getOrderBy(), $params->getOrderDirection())
            ->setPage($params->getPage(), $params->getLimit());

        return $this->pricing_strategy_product_repository->findAllPaginated($query_builder);
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function deletePricingStrategyProduct(int $pricing_strategy_id, int $product_id): int
    {
        $this->pricing_strategy_repository->fetchById($pricing_strategy_id);

        if (!$this->article_read_repository->exists($product_id)) {
            throw new NotFoundException(sprintf('Product not found %s', $product_id));
        }

        return $this->pricing_strategy_product_repository->removeProductFromStrategy($pricing_strategy_id, $product_id);
    }

    /** @return PricingStrategyProductEntity[] */
    public function getPricingStrategyProducts(int $pricing_strategy_id): array
    {
        return $this->pricing_strategy_product_repository->findAllByStrategyId($pricing_strategy_id);
    }
}
