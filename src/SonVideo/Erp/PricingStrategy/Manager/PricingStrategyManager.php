<?php

namespace SonVideo\Erp\PricingStrategy\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Params\FiltersParams;
use App\Sql\Helper\Pager;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\PricingStrategy\Dto\CreationContext\PricingStrategyCreationContextDto;
use SonVideo\Erp\PricingStrategy\Dto\UpdateContext\PricingStrategyUpdateContextDto;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyEntity;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyArchiveRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyCompetitorRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategySalesChannelRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Referential\PricingStrategyStatus;
use SonVideo\Erp\User\Entity\UserEntity;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PricingStrategyManager
{
    private PricingStrategyRepository $pricing_strategy_repository;
    private PricingStrategySalesChannelRepository $pricing_strategy_sales_channel_repository;
    private PricingStrategyCompetitorRepository $pricing_strategy_competitor_repository;
    private ValidatorInterface $validator;
    private QueryBuilder $query_builder;
    private LegacyPdo $legacy_pdo;

    private SerializerInterface $serializer;

    private PricingStrategyArchiveRepository $pricing_strategy_archive_repository;

    public function __construct(
        PricingStrategyRepository $pricing_strategy_repository,
        PricingStrategySalesChannelRepository $pricing_strategy_sales_channel_repository,
        PricingStrategyCompetitorRepository $pricing_strategy_competitor_repository,
        ValidatorInterface $validator,
        QueryBuilder $query_builder,
        LegacyPdo $legacy_pdo,
        SerializerInterface $serializer,
        PricingStrategyArchiveRepository $pricing_strategy_archive_repository
    ) {
        $this->pricing_strategy_repository = $pricing_strategy_repository;
        $this->pricing_strategy_sales_channel_repository = $pricing_strategy_sales_channel_repository;
        $this->pricing_strategy_competitor_repository = $pricing_strategy_competitor_repository;
        $this->validator = $validator;
        $this->query_builder = $query_builder;
        $this->legacy_pdo = $legacy_pdo;
        $this->serializer = $serializer;
        $this->pricing_strategy_archive_repository = $pricing_strategy_archive_repository;
    }

    /** @throws \Exception */
    public function create(PricingStrategyCreationContextDto $dto): int
    {
        $this->validateDto($dto);

        $pricing_strategy_id = $this->pricing_strategy_repository->create($dto);

        $this->attachSalesChannelsToStrategy($pricing_strategy_id, $dto->sales_channels);
        $this->attachCompetitorsToStrategy($pricing_strategy_id, $dto->competitors);

        return $pricing_strategy_id;
    }

    /**
     * @throws NotFoundException
     * @throws InternalErrorException
     */
    public function update(PricingStrategyUpdateContextDto $dto): void
    {
        $this->validateDto($dto);

        try {
            $this->legacy_pdo->beginTransaction();
            if (0 === $this->pricing_strategy_repository->update($dto)) {
                throw new NotFoundException('Pricing strategy not found');
            }

            $this->pricing_strategy_sales_channel_repository->removeSalesChannelsFromStrategy(
                $dto->pricing_strategy_id
            );
            $this->attachSalesChannelsToStrategy($dto->pricing_strategy_id, $dto->sales_channels);
            $this->pricing_strategy_competitor_repository->removeCompetitorsFromStrategy($dto->pricing_strategy_id);
            $this->attachCompetitorsToStrategy($dto->pricing_strategy_id, $dto->competitors);

            $this->legacy_pdo->commit();
        } catch (\Exception $e) {
            $this->legacy_pdo->rollBack();
            throw $e;
        }
    }

    public function getFilteredCollection(FiltersParams $params): Pager
    {
        $query_builder = $this->query_builder
            ->setWhere($params->getFilters() ?? [])
            ->setOrderBy($params->getOrderBy(), $params->getOrderDirection())
            ->setPage($params->getPage(), $params->getLimit());

        return $this->pricing_strategy_repository->findAllPaginated($query_builder);
    }

    /** @throws NotFoundException */
    public function getPricingStrategyById(int $pricing_strategy_id): PricingStrategyEntity
    {
        return $this->pricing_strategy_repository->fetchById($pricing_strategy_id);
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws InternalErrorException
     * @throws \Exception
     */
    public function activatePricingStrategy(int $pricing_strategy_id, UserEntity $user): void
    {
        $pricing_strategy = $this->pricing_strategy_repository->fetchById($pricing_strategy_id);

        if (PricingStrategyStatus::ACTIVATED === $pricing_strategy->activation_status) {
            throw new \Exception('Pricing strategy already activated');
        }

        $this->pricing_strategy_archive_repository->createPricingStrategyArchive(
            $pricing_strategy,
            PricingStrategyStatus::ACTIVATED
        );
        $this->pricing_strategy_repository->updateActivationStatus(
            $pricing_strategy_id,
            PricingStrategyStatus::ACTIVATED
        );
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws \Exception
     */
    public function closePricingStrategy(
        int $pricing_strategy_id,
        int $user_id,
        string $status,
        ?PricingStrategyEntity $pricing_strategy = null
    ): void {
        if (!$pricing_strategy instanceof PricingStrategyEntity) {
            $pricing_strategy = $this->pricing_strategy_repository->fetchById($pricing_strategy_id);
        }

        if (
            PricingStrategyStatus::DEACTIVATED === $pricing_strategy->activation_status &&
            PricingStrategyStatus::DEACTIVATED === $status
        ) {
            throw new \Exception('Pricing strategy already deactivated');
        }

        $this->pricing_strategy_archive_repository->createPricingStrategyArchive($pricing_strategy, $status);
        $this->pricing_strategy_repository->updateActivationStatus($pricing_strategy_id, $status);
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     */
    public function pricingStrategyExpired(): int
    {
        $this->query_builder->setWhere(
            [
                'ends_at' => ['_lte' => date('Y-m-d H:i:s')],
                'activation_status' => [
                    '_in' => [PricingStrategyStatus::DEACTIVATED, PricingStrategyStatus::ACTIVATED],
                ],
            ],
            PricingStrategyRepository::COLUMNS_MAPPING
        );

        $pricing_strategies = $this->pricing_strategy_repository->findAllPaginated($this->query_builder)->getResults();

        foreach ($pricing_strategies as $pricing_strategy) {
            /** @var PricingStrategyEntity $pricing_strategy */
            $pricing_strategy = $this->serializer->denormalize($pricing_strategy, PricingStrategyEntity::class);

            $this->closePricingStrategy(
                $pricing_strategy->pricing_strategy_id,
                UserEntity::SYSTEM_ID,
                PricingStrategyStatus::EXPIRED,
                $pricing_strategy
            );
        }

        return count($pricing_strategies);
    }

    /** @throws InternalErrorException */
    public function validateDto($dto): void
    {
        $errors = $this->validator->validate($dto);
        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }
    }

    private function attachSalesChannelsToStrategy(int $pricing_strategy_id, array $sales_channels): void
    {
        if ([] !== $sales_channels) {
            $this->pricing_strategy_sales_channel_repository->attachSalesChannelsToStrategy(
                $pricing_strategy_id,
                $sales_channels
            );
        }
    }

    private function attachCompetitorsToStrategy(int $pricing_strategy_id, array $competitors): void
    {
        if ([] !== $competitors) {
            $this->pricing_strategy_competitor_repository->attachCompetitorsToStrategy(
                $pricing_strategy_id,
                $competitors
            );
        }
    }
}
