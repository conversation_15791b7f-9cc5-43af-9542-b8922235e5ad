<?php

namespace SonVideo\Erp\CustomerOrderProduct\Contract;

use SonVideo\Erp\Quote\Entity\ProductInfoSelectedWarrantyEntity;

trait CustomerOrderProductWarrantiesTrait
{
    public function extractWarranties(array $warranties): array
    {
        $customer_order_warranties = [];

        foreach ($warranties as $warranty) {
            if (ProductInfoSelectedWarrantyEntity::TYPE_EXTENSION === $warranty['type']) {
                $customer_order_warranties['warranty_duration_ext'] = $warranty['duration'];
                $customer_order_warranties['warranty_unit_selling_price_ext'] =
                    $warranty['unit_selling_price_tax_included'];
            }
            if (ProductInfoSelectedWarrantyEntity::TYPE_THEFT_BREAKDOWN === $warranty['type']) {
                $customer_order_warranties['warranty_duration_tb'] = $warranty['duration'];
                $customer_order_warranties['warranty_unit_selling_price_tb'] =
                    $warranty['unit_selling_price_tax_included'];
            }
        }

        return $customer_order_warranties;
    }
}
