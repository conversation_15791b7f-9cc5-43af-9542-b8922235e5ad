<?php

namespace SonVideo\Erp\Marketplace\EasyLounge\Mysql\Repository;

use App\DataLoader\MapToEntityTrait;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Marketplace\EasyLounge\Entity\ProductToExportEntity;

class ExportedProductWriteRepository extends AbstractLegacyRepository
{
    use MapToEntityTrait;

    public function upsertProductChecksum(ProductToExportEntity $product): self
    {
        $sql = <<<SQL
        INSERT INTO backOffice.EZL_exported_product_checksum (sku, checksum)
        VALUES (:sku, :checksum)
        ON DUPLICATE KEY UPDATE checksum = :checksum
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'sku' => $product->sku,
            'checksum' => $product->checksum,
        ]);

        return $this;
    }
}
