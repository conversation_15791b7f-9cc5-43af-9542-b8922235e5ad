<?php

namespace SonVideo\Erp\Inventory\Mysql\Repository;

use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;

/**
 * Class InventoryCollectWriteRepository.
 */
class InventoryCollectWriteRepository extends AbstractLegacyRepository
{
    /**
     * activate.
     *
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    public function activate(int $inventory_collect_id): self
    {
        $this->legacy_pdo->fetchValue('CALL backOffice.WMS_INV_collecte_active(:inventory_collect_id)', [
            'inventory_collect_id' => $inventory_collect_id,
        ]);

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    public function count(
        int $product_id,
        int $inventory_id,
        int $location_id,
        int $quantity,
        int $user_id,
        bool $use_main_collect
    ): self {
        $this->legacy_pdo->fetchValue(
            'CALL backOffice.WMS_INV_add_count_for_article(:product_id, :inventory_id, :location_id, :quantity, :user_id, :use_main_collect)',
            [
                'product_id' => $product_id,
                'inventory_id' => $inventory_id,
                'location_id' => $location_id,
                'quantity' => $quantity,
                'user_id' => $user_id,
                'use_main_collect' => $use_main_collect,
            ]
        );

        return $this;
    }
}
