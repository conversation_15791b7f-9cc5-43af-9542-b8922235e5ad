<?php

namespace SonVideo\Erp\Inventory\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\SqlErrorMessageException;

class InventoryLocationReadRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public function findRemainingLocationsForActiveCollecte(int $inventory_id): array
    {
        $sql = <<<SQL
            SELECT
                wl.location_id AS location_id,
                wl.label AS label
            FROM backOffice.WMS_location wl
                LEFT JOIN backOffice.BO_INV_inventory_location biil ON biil.location_id = wl.location_id
                LEFT JOIN backOffice.BO_INV_inventaire bii ON biil.inventory_id = bii.id
            WHERE bii.id = :inventory_id
                AND biil.scanned_empty_at IS NULL
                AND wl.location_id NOT IN (
                    SELECT bica.id_emplacement
                    FROM backOffice.BO_INV_collecte_article bica
                    WHERE bica.BO_INV_collecte_id = bii.collecte_active_id
            )
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['inventory_id' => $inventory_id]);
    }

    /** @throws SqlErrorMessageException */
    public function countLocationsForInventory(int $inventory_id): int
    {
        $sql = <<<SQL
            SELECT
                COUNT(biil.location_id) AS count
            FROM backOffice.BO_INV_inventory_location biil
            WHERE biil.inventory_id = :inventory_id;
        SQL;

        return (int) $this->legacy_readonly_pdo->fetchValue($sql, ['inventory_id' => $inventory_id]);
    }
}
