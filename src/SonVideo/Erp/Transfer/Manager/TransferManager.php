<?php

namespace SonVideo\Erp\Transfer\Manager;

use App\DataLoader\MapToEntityTrait;
use App\Params\FiltersParams;
use App\Sql\Helper\Pager;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Product\Mysql\Repository\ProductRepository;
use SonVideo\Erp\Transfer\Entity\TransferEntity;
use SonVideo\Erp\Transfer\Mysql\Repository\TransferRepository;

class TransferManager
{
    use MapToEntityTrait;

    private TransferRepository $repo;

    private ProductRepository $product_repo;

    private LegacyPdo $legacy_pdo;

    private QueryBuilder $query_builder;

    public function __construct(
        TransferRepository $repo,
        ProductRepository $product_repo,
        LegacyPdo $legacy_pdo,
        QueryBuilder $query_builder
    ) {
        $this->repo = $repo;
        $this->product_repo = $product_repo;
        $this->legacy_pdo = $legacy_pdo;
        $this->query_builder = $query_builder;
    }

    /** Get a paginated filtered collection of Transfer */
    public function getFilteredCollection(FiltersParams $params): Pager
    {
        $query_builder = $this->query_builder
            ->setWhere($params->getFilters() ?? [])
            ->setOrderBy($params->getOrderBy(), $params->getOrderDirection())
            ->setPage($params->getPage(), $params->getLimit());

        return $this->repo->findAllPaginated($query_builder);
    }

    /** Get one transfer */
    public function getOneBy(FiltersParams $params): ?TransferEntity
    {
        $query_builder = $this->query_builder->setWhere($params->getFilters());

        return $this->repo->findOne($query_builder);
    }

    /**
     * Create a new transfer.
     *
     * @throws \Exception
     */
    public function createTransfert(
        string $username,
        int $warehouse_from,
        int $warehouse_to,
        array $products_quantities
    ): int {
        try {
            $this->legacy_pdo->beginTransaction();

            $transfer_id = $this->repo->createTransfer($username, $warehouse_from, $warehouse_to);
            $this->addProducts($transfer_id, $products_quantities);

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $transfer_id;
    }

    /**
     * add Products in a transfer.
     *
     * @param array<int,array<string,int>> $products_quantities
     */
    public function addProducts(int $transfer_id, array $products_quantities): self
    {
        $final_product_quantities = [];

        // map the final product quantity array
        $count_final_product = function (int $id_produit, int $quantity, bool $add_product) use (
            &$final_product_quantities
        ): void {
            if (!isset($final_product_quantities[$id_produit])) {
                $final_product_quantities[$id_produit] = [
                    'product_id' => $id_produit,
                    'quantity' => 0,
                    'add_product' => $add_product,
                ];
            }

            $final_product_quantities[$id_produit]['quantity'] += $quantity;
        };

        // retrieve final products quantities (handle composition)
        foreach ($products_quantities as $product_quantity) {
            $product_key = $product_quantity['product_key'] ?? $product_quantity['product_id'];
            $product = $this->product_repo->getByKey($product_key);

            if ($add_product = isset($product_quantity['add_quantity'])) {
                $quantity = $product_quantity['add_quantity'];
            } else {
                $quantity = $product_quantity['quantity'];
            }

            // case where the product is composed of others
            if ($product->is_package) {
                foreach ($product->packaged_articles as $packaged_article) {
                    $count_final_product(
                        $packaged_article->id_produit,
                        $quantity * $packaged_article->quantite,
                        $add_product
                    );
                }
            } else {
                $count_final_product($product->id_produit, $quantity, $add_product);
            }
        }

        // insert final products
        foreach ($final_product_quantities as $final_product_quantity) {
            $this->repo->addTransferedProduct(
                $transfer_id,
                $final_product_quantity['product_id'],
                $final_product_quantity['quantity'],
                $final_product_quantity['add_product']
            );
        }

        return $this;
    }
}
