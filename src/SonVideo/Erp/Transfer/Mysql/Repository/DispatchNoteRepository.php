<?php

namespace SonVideo\Erp\Transfer\Mysql\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Transfer\Entity\DispatchNoteEntity;

class DispatchNoteRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use LegacyPdoAwareTrait;
    use DataLoaderAwareTrait;

    public const COLUMNS_MAPPING = [
        'id' => 'dn.id',
        'creation_date' => 'dn.created_at',
        'is_sent' => 'dn.sent',
    ];

    /** Find all transfers */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<'SQL'
        SELECT SQL_CALC_FOUND_ROWS *
        FROM (
            {base_sql}
        ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => strtr($this->getSelectBaseSql(), [
                '{conditions}' => $query_builder->setColumnsMapping(static::COLUMNS_MAPPING)->getWhere(),
            ]),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            DispatchNoteEntity::class
        );
    }

    private function getSelectBaseSql(): string
    {
        return <<<'SQL'
        SELECT
            dn.id           id,
            dn.created_at   creation_date,
            dn.sent         is_sent
        FROM
            BO_EXP_franceexpress_dispatch_note dn
        WHERE {conditions}
        GROUP BY dn.id
        SQL;
    }

    /** @throws NotFoundException */
    public function findOneById(int $dispatch_note_id): DispatchNoteEntity
    {
        $sql = strtr($this->getSelectBaseSql(), [
            '{conditions}' => 'dn.id = :dispatch_note_id',
        ]);

        $dispatch_note = $this->legacy_readonly_pdo->fetchOneEntity(
            $sql,
            ['dispatch_note_id' => $dispatch_note_id],
            DispatchNoteEntity::class
        );

        if (!$dispatch_note instanceof DispatchNoteEntity) {
            throw new NotFoundException(sprintf('Dispatch note not found with id %s', $dispatch_note_id));
        }

        return $dispatch_note;
    }
}
