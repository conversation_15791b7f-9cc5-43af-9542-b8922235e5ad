<?php

namespace SonVideo\Erp\Transfer\Mysql\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Transfer\Entity\TransferEntity;

class TransferRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use LegacyPdoAwareTrait;
    use DataLoaderAwareTrait;

    private const COLUMNS_MAPPING = [
        'transfer_id' => 't.id',
        'warehouse_from.id' => 't.id_depot_depart',
        'warehouse_to.id' => 't.id_depot_arrivee',
        'customer_order_id' => 't.id_commande',
        'created_at' => 't.date_creation',
        'closed' => 't.date_cloture',
        'created_by' => 't.utilisateur_creation',
        'type' => 't.type_transfert',
        'status' => 't.statut',
        'comment' => 't.commentaire',
        'shipement_method_id' => 't.id_pdt_transporteur',
        'transfered_products.product.product_id' => 'p.id_produit',
        'transfered_products.product.sku' => 'p.reference',
    ];

    /** Find all transfers */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<'SQL'
        SELECT SQL_CALC_FOUND_ROWS *
        FROM (
            {base_sql}
        ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getSelectBaseSql($query_builder),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            TransferEntity::class
        );
    }

    /** Find One transfer */
    public function findOne(QueryBuilder $query_builder): TransferEntity
    {
        $sql = $this->getSelectBaseSql($query_builder);

        return $this->legacy_pdo->fetchOneEntity($sql, $query_builder->getWhereParameters(), TransferEntity::class);
    }

    private function getSelectBaseSql(QueryBuilder $query_builder): string
    {
        $sql = <<<'SQL'
        SELECT
            t.id AS transfer_id,
            t.date_creation AS created_at,
            t.date_cloture AS closed_at,
            t.utilisateur_creation AS created_by,
            t.type_transfert AS type,
            t.statut AS status,
            t.commentaire AS comment,
            t.id_pdt_transporteur AS shipment_method_id,
            t.id_commande AS customer_order_id,
            JSON_OBJECT(
                'warehouse_id', wf.id,
                'code', wf.code,
                'name', wf.nom_depot
            ) AS warehouse_from,
            JSON_OBJECT(
                'warehouse_id', wt.id,
                'code', wt.code,
                'name', wt.nom_depot
            ) AS warehouse_to,
            IF(
                COUNT(pt.id) = 0,
                JSON_ARRAY(),
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'delivery_note_id', pt.id_bon_livraison,
                        'transfer_product_id', pt.id,
                        'product', JSON_OBJECT(
                            'product_id', p.id_produit,
                            'sku', p.reference,
                            'description', CONCAT(m.marque, ' ', a.modele)
                        ),
                        'quantity', pt.quantite
                    )
                )
            ) AS transfered_products
        FROM backOffice.BO_STK_transfert t
        LEFT JOIN backOffice.BO_STK_depot wf ON t.id_depot_depart = wf.id
        LEFT JOIN backOffice.BO_STK_depot wt ON t.id_depot_arrivee = wt.id
        LEFT JOIN backOffice.BO_STK_produit_transfert pt ON t.id = pt.id_transfert
        LEFT JOIN backOffice.produit p ON pt.id_produit = p.id_produit
        LEFT JOIN backOffice.article a ON p.id_produit = a.id_produit
        LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
        WHERE {conditions}
        GROUP BY t.id
        SQL;

        return strtr($sql, ['{conditions}' => $query_builder->setColumnsMapping(self::COLUMNS_MAPPING)->getWhere()]);
    }

    /** Create a new Transfer */
    public function createTransfer(string $username, int $warehouse_from, int $warehouse_to): int
    {
        // insert transfer
        $sql = <<<'SQL'
        INSERT INTO backOffice.BO_STK_transfert (id_depot_depart, id_depot_arrivee, date_creation, id_pdt_transporteur, utilisateur_creation)
        VALUES (:warehouse_from, :warehouse_to, NOW(), backOffice.TRF_get_default_carrier_product(:warehouse_from, :warehouse_to), :username);
        SQL;
        $new_row = $this->legacy_pdo->fetchAffected($sql, [
            'warehouse_from' => $warehouse_from,
            'warehouse_to' => $warehouse_to,
            'username' => $username,
        ]);
        if (0 === $new_row) {
            throw new \UnexpectedValueException('Transfer creation failed.');
        }

        // retrieve transfer_id
        return $this->legacy_pdo->lastInsertId();
    }

    /** Add product to a transfer. This product must be final (= not a package) */
    public function addTransferedProduct(int $transfer_id, int $product_id, int $quantity, bool $add_product): void
    {
        $sql = <<<'SQL'
        INSERT INTO backOffice.BO_STK_produit_transfert (id_transfert, id_produit, quantite)
        VALUES (:transfer_id, :product_id, :quantity)
        ON DUPLICATE KEY UPDATE quantite = {on_update_quantity}
        SQL;

        $sql = strtr($sql, ['{on_update_quantity}' => $add_product ? 'quantite + :quantity' : ':quantity']);

        $this->legacy_pdo->fetchAffected($sql, [
            'transfer_id' => $transfer_id,
            'product_id' => $product_id,
            'quantity' => $quantity,
        ]);
    }
}
