<?php

namespace SonVideo\Erp\Transfer\Mysql\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use SonVideo\Erp\Transfer\Entity\TransferCollectionEntity;

class TransferCollectionRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use DataLoaderAwareTrait;
    use LegacyPdoAwareTrait;

    /** Get stalled transfers from france express */
    public function getStalledTransfers(): array
    {
        $sql = <<<SQL
        SELECT
            {base_sql}
        FROM backOffice.BO_STK_transfer_collection c
            INNER JOIN backOffice.BO_STK_transfert t ON c.transfer_id = t.id
            INNER JOIN backOffice.bon_livraison bl ON t.id = bl.id_transfert
            INNER JOIN backOffice.BO_TPT_PDT_liste tl ON bl.id_pdt_transporteur = tl.id
            LEFT JOIN backOffice.BO_STK_depot sd ON t.id_depot_depart = sd.id
            LEFT JOIN backOffice.BO_STK_depot td ON t.id_depot_arrivee = td.id
        WHERE
            t.statut = 'expedie'
            AND c.collected = 0
            AND c.sticker_id = 0
            AND tl.transporteur_id = 30
            AND t.id_depot_depart = 21
        GROUP BY t.id_pdt_transporteur, t.id_depot_depart, t.id_depot_arrivee;
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        return $this->legacy_pdo->fetchAllEntities($sql, [], TransferCollectionEntity::class);
    }

    /** @return TransferCollectionEntity[] */
    public function getTransferReports(): array
    {
        $sql = <<<SQL
        SELECT
            {base_sql}
        FROM backOffice.BO_STK_transfer_collection c
        INNER JOIN BO_STK_transfer_collection_sticker s ON c.sticker_id = s.id
        INNER JOIN BO_STK_transfert t ON c.transfer_id = t.id
        INNER JOIN bon_livraison bl ON t.id = bl.id_transfert
        INNER JOIN BO_TPT_PDT_liste tl ON bl.id_pdt_transporteur = tl.id
        LEFT JOIN BO_STK_depot sd ON t.id_depot_depart = sd.id
        LEFT JOIN BO_STK_depot td ON t.id_depot_arrivee = td.id
        WHERE
            t.statut = 'expedie'
            AND c.collected = 0
            AND c.sticker_id <> 0
            AND tl.transporteur_id = 30
        GROUP BY t.id_pdt_transporteur, c.sticker_id, t.id_depot_depart, t.id_depot_arrivee;
        SQL;

        $sql = strtr($sql, ['{base_sql}' => $this->getTransferReportsSql()]);

        return $this->legacy_pdo->fetchAllEntities($sql, [], TransferCollectionEntity::class);
    }

    private function getBaseSql(): string
    {
        return <<<'SQL'
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'transfer_id', c.transfer_id,
                    'weight', CAST(FORMAT_poids(
                        bl.id_transporteur,
                        bl.id_bon_livraison
                    ) AS DECIMAL(8, 2))
                )
            )                           transfers,
            bl.id_bon_livraison         delivery_note_id,
            t.id_pdt_transporteur       carrier_product_id,
            tl.libelle_produit          carrier_product_label,
            t.id_depot_depart           sent_from_id,
            sd.nom_depot                sent_from_name,
            t.id_depot_arrivee          target_id,
            td.nom_depot                target_name
        SQL;
    }

    private function getTransferReportsSql(): string
    {
        return implode(',', [$this->getBaseSql(), 'c.parcel_number tracking_number, s.amount parcels']);
    }
}
