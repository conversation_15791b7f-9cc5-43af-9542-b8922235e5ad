<?php

namespace SonVideo\Erp\Partner\Manager;

use League\Csv\AbstractCsv;
use League\Csv\CannotInsertRecord;
use League\Csv\Reader;
use League\Csv\Writer;
use League\Flysystem\FileNotFoundException;
use SonVideo\Erp\Filesystem\Manager\TemporaryFile;
use SonVideo\Erp\Partner\Mysql\Repository\CustomerOrderRepository;

/**
 * Class AffiliationConsolidation.
 */
class AffiliationConsolidation
{
    private TemporaryFile $temporary_file;
    private CustomerOrderRepository $customer_order_repository;

    public function __construct(TemporaryFile $temporary_file, CustomerOrderRepository $customer_order_repository)
    {
        $this->temporary_file = $temporary_file;
        $this->customer_order_repository = $customer_order_repository;
    }

    /**
     * @throws CannotInsertRecord
     * @throws \InvalidArgumentException
     * @throws FileNotFoundException
     */
    public function getCsvString(string $file_path): AbstractCsv
    {
        $file_system = $this->temporary_file->getFilesystem();

        if (!$file_system->has($file_path)) {
            throw new \InvalidArgumentException('File does not exist.');
        }

        if (!in_array($file_system->getMimetype($file_path), ['text/csv', 'text/plain'], true)) {
            throw new \InvalidArgumentException('File must be a CSV.');
        }

        $input_csv = Reader::createFromString($file_system->read($file_path));
        $customer_order_ids = array_column(iterator_to_array($input_csv->getRecords()), '0');
        $output_data = $this->customer_order_repository->getConsolidated($customer_order_ids);

        $output_csv = Writer::createFromString();
        $output_csv->insertOne(['Commande ID', 'Statut', 'Détail statut', 'Origine', 'Clef']);
        $output_csv->insertAll($output_data);

        $file_system->delete($file_path);

        return $output_csv;
    }
}
