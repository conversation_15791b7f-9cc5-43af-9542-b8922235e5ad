<?php

namespace SonVideo\Erp\Brand\Mysql\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\DataLoader\MapToEntityTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Aura\Sql\Exception\CannotBindValue;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Brand\Entity\BrandEntity;

final class BrandRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use LegacyPdoAwareTrait;
    use MapToEntityTrait;
    use DataLoaderAwareTrait;

    public const COLUMNS_MAPPING = [
        'brand_id' => 'm.id_marque',
        'name' => 'm.marque',
        'status' => 'm.status',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT DISTINCT
                  m.id_marque AS brand_id,
                  m.marque AS name,
                  m.status AS status
              FROM backOffice.marque m
              WHERE {conditions}
              ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function findAllBySupplierId(int $supplier_id): array
    {
        $sql = <<<'SQL'
            SELECT DISTINCT
                m.id_marque AS brand_id,
                m.marque AS name,
                m.status AS status
            FROM backOffice.marque m
                LEFT JOIN backOffice.BO_FRN_marque fm on fm.marque = m.id_marque
            WHERE fm.frn = :supplier_id
            ORDER BY m.marque
        SQL;

        $brands = $this->legacy_readonly_pdo->fetchObjects($sql, ['supplier_id' => $supplier_id]);

        return $this->mapToEntities($brands, BrandEntity::class);
    }

    /**
     * Get the ids of brand of the group.
     *
     * @return array<{brand_id: int}>
     *
     * @throws CannotBindValue
     * @throws Exception
     */
    public function getGroupBrands(): array
    {
        $sql = <<<SQL
        SELECT
            m.id_marque as brand_id,
            m.marque as brand_name,
            m.type_marque_maison as group_brand
        FROM
            backOffice.marque m
        WHERE
            m.type_marque_maison IS NOT NULL
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql);
    }
}
