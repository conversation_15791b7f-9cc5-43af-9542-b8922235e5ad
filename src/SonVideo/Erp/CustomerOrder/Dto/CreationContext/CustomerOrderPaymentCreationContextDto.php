<?php

/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Dto\CreationContext;

use OpenApi\Annotations as OA;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationPayload;
use SonVideo\Erp\Referential\PaymentWorkflow;
use Symfony\Component\Validator\Constraints as Assert;

final class CustomerOrderPaymentCreationContextDto
{
    /**
     * @OA\Property(example="ECLNG")
     *
     * @Assert\NotBlank(allowNull=true)
     */
    public ?string $payment_mean = null;

    /**
     * @OA\Property(example="2023-03-07 12:00:00")
     *
     * @Assert\NotBlank(allowNull=true)
     */
    public ?string $created_at = null;

    /**
     * @OA\Property(example=149.99)
     *
     * @Assert\NotBlank()
     */
    public float $amount;

    /** @OA\Property(type="object") */
    public array $extra_data = [];

    /** @OA\Property(type="string") */
    public ?string $external_reference = null;

    /**
     * @OA\Property(example="legacy")
     *
     * @Assert\NotBlank(allowNull=true)
     */
    public ?string $workflow = PaymentWorkflow::LEGACY;

    private ?PaymentV2CreationPayload $verified_payment = null;

    public function setPaymentV2VerifiedPayment(PaymentV2CreationPayload $verified_payment): void
    {
        $this->verified_payment = $verified_payment;
    }

    public function getPaymentV2VerifiedPayment(): ?PaymentV2CreationPayload
    {
        return $this->verified_payment;
    }

    /** Payment V2 needs this to update the payment in its system */
    public function setCreatedProof(string $created_proof): void
    {
        $this->verified_payment->created_proof = $created_proof;
    }
}
