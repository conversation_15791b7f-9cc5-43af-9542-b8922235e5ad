<?php

/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Dto\CreationContext;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class CustomerOrderProductCreationContextDto
{
    /**
     * @OA\Property(example="ARCAMRBLINKNR")
     *
     * @Assert\NotBlank()
     */
    public string $sku;

    /**
     * @OA\Property(example="Récepteur Audio Bluetooth APTX Arcam rBlink")
     *
     * @Assert\NotBlank()
     */
    public string $description;

    /** @OA\Property(example=0.4) */
    public float $ecotax_price;

    /** @OA\Property(example=2.5) */
    public float $sorecop_price;

    /**
     * @OA\Property(example=1)
     *
     * @Assert\NotBlank()
     * @Assert\Positive()
     */
    public int $quantity;

    /** @OA\Property(example=145.99) */
    public ?float $selling_price_tax_included = null;

    /**
     * @OA\Property(example=-10)
     *
     * @Assert\NotBlank()
     * @Assert\NegativeOrZero()
     */
    public float $unit_discount_amount = 0.0;

    /**
     * @Assert\Valid
     *
     * @var CustomerOrderSelectedWarrantyCreationContextDto[]|null
     */
    public ?array $selected_warranties = null;

    public function computeTotalPrice(): float
    {
        return ($this->computeUnitPrice() + $this->computeUnitWarrantyPrice()) * $this->quantity;
    }

    public function computeUnitPrice(): float
    {
        return $this->selling_price_tax_included + $this->unit_discount_amount;
    }

    public function computeUnitWarrantyPrice(): float
    {
        return array_reduce(
            $this->selected_warranties ?? [],
            static fn (
                $carry,
                CustomerOrderSelectedWarrantyCreationContextDto $item
            ): float => $item->unit_selling_price_tax_included + $carry,
            0.0
        );
    }
}
