<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Dto\CreationContext;

use App\Validator\Constraint as CustomAssert;
use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class CustomerOrderAddressCreationContextDto
{
    /** @var string|null */
    public $company_name;

    /**
     * @OA\Property(example="M.")
     *
     * @Assert\NotBlank()
     * @Assert\Choice({"M.", "Mme"})
     */
    public string $civility;

    /**
     * @OA\Property(example="John")
     *
     * @Assert\NotBlank()
     */
    public string $firstname;

    /**
     * @OA\Property(example="Doe")
     *
     * @Assert\NotBlank()
     */
    public string $lastname;

    /**
     * @OA\Property(example="38 rue de la Ville en Bois")
     *
     * @Assert\NotBlank()
     * @Assert\Length(max="500")
     */
    public string $address;

    /**
     * @OA\Property(example="44100")
     *
     * @Assert\NotBlank()
     */
    public string $postal_code;

    /**
     * @OA\Property(example="Nantes")
     *
     * @Assert\NotBlank()
     */
    public string $city;

    /**
     * @OA\Property(example="FR", description="ISO 3166-1 alpha-2 codes are two-letter country codes defined in ISO 3166-1")
     *
     * @Assert\NotBlank()
     * @CustomAssert\CountryCode()
     */
    public string $country_code;

    /** @OA\Property(example="0102030405") */
    public ?string $phone = null;

    /** @OA\Property(example="0102030405") */
    public string $cellphone;

    /**
     * @OA\Property(example="<EMAIL>")
     *
     * @Assert\NotBlank()
     * @Assert\Email()
     */
    public string $email;

    /** @OA\Property(example="FR29432317980") */
    public ?string $vat_number = null;
}
