<?php

namespace SonVideo\Erp\CustomerOrder\Manager;

use App\Exception\NotFoundException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Country\Manager\CountryManager;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderAddressCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderProductCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderSelectedWarrantyCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderShipmentMethodCreationContextDto;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrderProduct\Mysql\Repository\CustomerOrderProductRepository;
use SonVideo\Erp\Entity\OrderEntity;
use SonVideo\Erp\Referential\CustomerOrderIpAddress;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class CustomerOrderCloner
{
    private CustomerOrderRepository $customer_order_repository;
    private CountryManager $country_manager;
    private CustomerOrderProductRepository $customer_order_product_repository;
    private QueryBuilder $query_builder;

    private const SHIPMENT_METHOD_ID_EXCLUDED = [68];

    private const DEFAULT_SHIPMENT_METHOD_ID = 1;

    public function __construct(
        CustomerOrderRepository $customer_order_repository,
        CustomerOrderProductRepository $customer_order_order_product_repository,
        CountryManager $country_manager,
        QueryBuilder $query_builder
    ) {
        $this->customer_order_repository = $customer_order_repository;
        $this->country_manager = $country_manager;
        $this->customer_order_product_repository = $customer_order_order_product_repository;
        $this->query_builder = $query_builder;
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws \Exception
     */
    public function getCreationContextFromCustomerOrderId(int $customer_order_id): CustomerOrderCreationContextDto
    {
        $customer_order_to_clone = $this->customer_order_repository->findOneById($customer_order_id);
        $customer_order_creation_context_dto = new CustomerOrderCreationContextDto();

        $customer_order_creation_context_dto->clone_customer_order_id = $customer_order_id;
        $customer_order_creation_context_dto->origin = CustomerOrderOrigin::BACKOFFICE;
        $customer_order_creation_context_dto->created_at = (new \DateTime())->format('Y-m-d H:i:s');
        $customer_order_creation_context_dto->customer_id = $customer_order_to_clone->id_prospect;
        $customer_order_creation_context_dto->ip_address = CustomerOrderIpAddress::BACKOFFICE;
        $customer_order_creation_context_dto->total_price = $customer_order_to_clone->V_montant_ttc;
        $customer_order_creation_context_dto->quote_id = $customer_order_to_clone->quote_id;
        $customer_order_creation_context_dto->is_excluding_tax = $customer_order_to_clone->is_duty_free;
        $customer_order_creation_context_dto->is_amazon_business = $customer_order_to_clone->is_amazon_business;
        $customer_order_creation_context_dto->promotion_id = $customer_order_to_clone->promotion_id;

        $customer_order_creation_context_dto->promotion_linked_products = [];

        $customer_order_creation_context_dto->billing_address = $this->cloneBillingAddress($customer_order_to_clone);
        $customer_order_creation_context_dto->shipping_address = $this->cloneShippingAddress($customer_order_to_clone);
        $customer_order_creation_context_dto->shipment_method = $this->cloneShipmentMethod($customer_order_to_clone);
        $customer_order_creation_context_dto->payments = [];
        $customer_order_creation_context_dto->products = $this->cloneCustomerOrderProducts($customer_order_id);

        return $customer_order_creation_context_dto;
    }

    private function cloneShipmentMethod(
        OrderEntity $customer_order_to_clone
    ): CustomerOrderShipmentMethodCreationContextDto {
        $shipment_method = new CustomerOrderShipmentMethodCreationContextDto();
        $shipment = $this->customer_order_product_repository->fetchShippingProductFromOrder(
            $customer_order_to_clone->id_commande
        );

        $shipment_method->shipment_method_id = in_array(
            $customer_order_to_clone->id_pdt_transporteur,
            self::SHIPMENT_METHOD_ID_EXCLUDED
        )
            ? self::DEFAULT_SHIPMENT_METHOD_ID
            : $customer_order_to_clone->id_pdt_transporteur;
        $shipment_method->relay_id = $customer_order_to_clone->relay_id;
        $shipment_method->cost = $shipment['selling_price'];

        return $shipment_method;
    }

    /** @throws \Exception */
    private function cloneShippingAddress(OrderEntity $customer_order_to_clone): CustomerOrderAddressCreationContextDto
    {
        $shipping_address = new CustomerOrderAddressCreationContextDto();
        $shipping_address->company_name = $customer_order_to_clone->cnt_lvr_societe;
        $shipping_address->civility = $customer_order_to_clone->cnt_lvr_civilite;
        $shipping_address->firstname = $customer_order_to_clone->cnt_lvr_prenom;
        $shipping_address->lastname = $customer_order_to_clone->cnt_lvr_nom;
        $shipping_address->address = $customer_order_to_clone->cnt_lvr_adresse;
        $shipping_address->postal_code = $customer_order_to_clone->cnt_lvr_code_postal;
        $shipping_address->city = $customer_order_to_clone->cnt_lvr_ville;
        $shipping_address->country_code = $this->country_manager->getCountryCodeFromId(
            $customer_order_to_clone->cnt_fct_id_pays
        );
        $shipping_address->phone = $customer_order_to_clone->cnt_lvr_telephone;
        $shipping_address->cellphone = $customer_order_to_clone->cnt_lvr_mobile;
        $shipping_address->email = $customer_order_to_clone->cnt_lvr_email;
        $shipping_address->vat_number = $customer_order_to_clone->cnt_lvr_numero_tva;

        return $shipping_address;
    }

    /** @throws \Exception */
    private function cloneBillingAddress(OrderEntity $customer_order_to_clone): CustomerOrderAddressCreationContextDto
    {
        $billing_address = new CustomerOrderAddressCreationContextDto();
        $billing_address->company_name = $customer_order_to_clone->cnt_fct_societe;
        $billing_address->civility = $customer_order_to_clone->cnt_fct_civilite;
        $billing_address->firstname = $customer_order_to_clone->cnt_fct_prenom;
        $billing_address->lastname = $customer_order_to_clone->cnt_fct_nom;
        $billing_address->address = $customer_order_to_clone->cnt_fct_adresse;
        $billing_address->postal_code = $customer_order_to_clone->cnt_fct_code_postal;
        $billing_address->city = $customer_order_to_clone->cnt_fct_ville;
        $billing_address->country_code = $this->country_manager->getCountryCodeFromId(
            $customer_order_to_clone->cnt_fct_id_pays
        );
        $billing_address->phone = $customer_order_to_clone->cnt_fct_telephone;
        $billing_address->cellphone = $customer_order_to_clone->cnt_fct_mobile;
        $billing_address->email = $customer_order_to_clone->cnt_fct_email;
        $billing_address->vat_number = $customer_order_to_clone->cnt_fct_numero_tva;

        return $billing_address;
    }

    /** @return CustomerOrderProductCreationContextDto[] */
    private function cloneCustomerOrderProducts(int $customer_order_id): array
    {
        $this->query_builder->setWhere(
            ['customer_order_id' => ['_eq' => $customer_order_id]],
            CustomerOrderProductRepository::COLUMNS_MAPPING
        );
        $products = $this->customer_order_product_repository->findAllPaginated($this->query_builder)->getResults();

        $customer_order_products_creation_context_dto = [];
        foreach ($products as $product) {
            if ($product['quantity'] <= 0) {
                continue;
            }
            $customer_order_product = new CustomerOrderProductCreationContextDto();
            $customer_order_product->sku = $product['sku'];
            $customer_order_product->description = $product['description'];
            $customer_order_product->ecotax_price = is_null($product['ecotax_price']) ? 0 : $product['ecotax_price'];
            $customer_order_product->sorecop_price = is_null($product['sorecop_price']) ? 0 : $product['sorecop_price'];
            $customer_order_product->quantity = $product['quantity'];
            $customer_order_product->selling_price_tax_included = $product['selling_price'];
            $customer_order_product->unit_discount_amount = is_null($product['discount_amount'])
                ? 0
                : $product['discount_amount'];

            if (!is_null($product['damage_and_theft_warranty_duration'])) {
                $warranty = new CustomerOrderSelectedWarrantyCreationContextDto();
                $warranty->type = 'theft_breakdown';
                $warranty->duration = $product['damage_and_theft_warranty_duration'];
                $warranty->unit_selling_price_tax_included = $product['damage_and_theft_warranty_price'];
                $customer_order_product->selected_warranties[] = $warranty;
            }

            if (!is_null($product['extension_warranty_duration'])) {
                $warranty = new CustomerOrderSelectedWarrantyCreationContextDto();
                $warranty->type = 'extension';
                $warranty->duration = $product['extension_warranty_duration'];
                $warranty->unit_selling_price_tax_included = $product['extension_warranty_price'];
                $customer_order_product->selected_warranties[] = $warranty;
            }

            $customer_order_products_creation_context_dto[] = $customer_order_product;
        }

        return $customer_order_products_creation_context_dto;
    }
}
