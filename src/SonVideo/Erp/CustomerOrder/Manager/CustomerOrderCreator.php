<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderInterface;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicReadRepository;
use App\Exception\SqlErrorMessageException as SqlErrorMessageExceptionAlias;
use App\Sql\LegacyPdo;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Customer\Manager\CustomerManager;
use SonVideo\Erp\CustomerOrder\Collection\CustomerOrderCreationDataMapperCollection;
use SonVideo\Erp\CustomerOrder\Contract\CreatedCustomerOrderResponsePayloadInterface;
use SonVideo\Erp\CustomerOrder\Contract\CustomerOrderCreationDataMapperInterface;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderProductCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Entity\Result\CreatedCustomerOrder;
use SonVideo\Erp\CustomerOrder\Entity\Result\CreatedCustomerOrderWithPaymentAction;
use SonVideo\Erp\CustomerOrder\Exception\CustomerOrderAlreadyExistsException;
use SonVideo\Erp\CustomerOrder\Exception\CustomerOrderRequestPayloadException;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository;
use SonVideo\Erp\CustomerOrderProduct\Mysql\Repository\CustomerOrderProductRepository;
use SonVideo\Erp\Payment\Manager\PaymentV2CreatorInterface;
use SonVideo\Erp\Payment\Manager\PaymentV2StateInterface;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use SonVideo\Erp\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\PaymentWorkflow;
use SonVideo\Erp\Referential\Product;
use SonVideo\Erp\SvdGiftCard\Manager\SvdGiftCardManager;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;
use SonVideo\Erp\User\Entity\UserEntity;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CustomerOrderCreator implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private LegacyPdo $legacy_pdo;

    /** @var Validation */
    private ValidatorInterface $validator;

    private DataLoaderInterface $data_loader;

    private CustomerOrderCreationDataMapperCollection $customer_order_creation_data_mapper_collection;

    private CustomerOrderRepository $customer_order_repository;

    private CustomerOrderProductRepository $customer_order_product_repository;

    private CustomerOrderPaymentWriteRepository $customer_order_payment;

    private TaskRepository $task_repository;

    private SerializerInterface $serializer;

    private CustomerManager $customer_manager;

    private SvdGiftCardManager $svd_gift_card_manager;

    private PaymentV2StateInterface $payment_v2_state;

    private PaymentV2CreatorInterface $payment_v2_creator;

    private QuoteRepository $quote_repository;
    private CurrentUser $current_user;
    private SynchronizableTopicReadRepository $synchronizable_topic_repository;

    public function __construct(
        LegacyPdo $legacy_pdo,
        ValidatorInterface $validator,
        DataLoaderInterface $data_loader,
        CustomerOrderCreationDataMapperCollection $customer_order_creation_data_mapper_collection,
        CustomerOrderRepository $customer_order_repository,
        CustomerOrderProductRepository $customer_order_product_repository,
        CustomerOrderPaymentWriteRepository $customer_order_payment,
        TaskRepository $task_repository,
        SerializerInterface $serializer,
        CustomerManager $customer_manager,
        SvdGiftCardManager $svd_gift_card_manager,
        PaymentV2StateInterface $payment_v2_state,
        PaymentV2CreatorInterface $payment_v2_creator,
        QuoteRepository $quote_repository,
        CurrentUser $current_user,
        SynchronizableTopicReadRepository $synchronizable_topic_repository
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->validator = $validator;
        $this->data_loader = $data_loader;
        $this->customer_order_creation_data_mapper_collection = $customer_order_creation_data_mapper_collection;
        $this->customer_order_repository = $customer_order_repository;
        $this->customer_order_product_repository = $customer_order_product_repository;
        $this->customer_order_payment = $customer_order_payment;
        $this->task_repository = $task_repository;
        $this->serializer = $serializer;
        $this->customer_manager = $customer_manager;
        $this->svd_gift_card_manager = $svd_gift_card_manager;
        $this->payment_v2_state = $payment_v2_state;
        $this->payment_v2_creator = $payment_v2_creator;
        $this->quote_repository = $quote_repository;
        $this->current_user = $current_user;
        $this->synchronizable_topic_repository = $synchronizable_topic_repository;
    }

    /**
     * @throws CustomerOrderRequestPayloadException
     * @throws SqlErrorMessageExceptionAlias
     * @throws \throwable
     */
    public function create(
        CustomerOrderCreationContextDto $customer_order_context_from_request
    ): CreatedCustomerOrderResponsePayloadInterface {
        /** @var ConstraintViolationListInterface $errors */
        $errors = $this->validator->validate($customer_order_context_from_request);

        if (count($errors) > 0) {
            throw new CustomerOrderRequestPayloadException(sprintf('Invalid request parameters : %s', ConstraintMessageFormatter::prettify($errors)));
        }

        // Validate payments on payment v2 (replaces the agreement from payment_v1)
        // Internally ignores origin that do not support Payment V2
        // Payment v2 flags the payments that it should handle after the customer order creation
        $customer_order_context_from_request = $this->payment_v2_state->isValidWithProvidedOrderContext(
            $customer_order_context_from_request
        );

        $data = $this->serializer->normalize($customer_order_context_from_request);

        try {
            $this->legacy_pdo->beginTransaction();

            $this->checkIfExistWithExternalReference($customer_order_context_from_request);

            // Prepare data before insertion
            /** @var CustomerOrderCreationDataMapperInterface $creation_data_mapper */
            $creation_data_mapper = $this->customer_order_creation_data_mapper_collection->getHandler($data['origin']);
            if (null === $creation_data_mapper) {
                throw new \RuntimeException(sprintf('No origin mapper found for key "%s"', $data['origin']));
            }

            $data['customer_order_id'] = $this->customer_order_repository->getOrderNumber(
                $data['origin'],
                !array_key_exists('original_customer_order_id', $data) ||
                '' === trim($data['original_customer_order_id'])
                    ? null
                    : trim($data['original_customer_order_id'])
            );

            $customer_order_creation_context = $creation_data_mapper->map($data);

            // create customer
            if ($customer_order_creation_context->new_customer) {
                $customer_order_creation_context->customer_id = $this->customer_manager->createAccountByEmail(
                    $customer_order_creation_context->billing_address_email,
                    $customer_order_creation_context->origin
                );
            }

            if (null === $customer_order_creation_context->customer_id) {
                throw new \RuntimeException('customer_id can not be null');
            }

            // sort of syncing, required if customer_id does not exist yet in backoffice.
            $this->customer_manager->createIfNotExist(
                $customer_order_creation_context->customer_id,
                $customer_order_creation_context->billing_address_email,
                $customer_order_creation_context->origin
            );

            // create customer order
            $this->customer_order_repository->createFromContext($customer_order_creation_context);

            if ($customer_order_creation_context->is_b2b) {
                $this->customer_order_repository->tagCommandeB2B(
                    $data['customer_order_id'],
                    $customer_order_creation_context->quote_creator_username
                );
            }

            // create customer order products
            foreach ($customer_order_creation_context->products as $product) {
                $this->addProductToOrder($product);
            }

            // Register for data-warehouse synchronization
            foreach (
                [SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT, SynchronizableTopicName::CUSTOMER_ORDER_UPSERT]
                as $topic
            ) {
                $this->synchronizable_topic_repository->upsert([
                    'target' => $product->customer_order_id,
                    'topic' => $topic,
                    'content' => ['customer_order_id' => $product->customer_order_id],
                ]);
            }

            // create customer order payments
            if ([] !== (array) $customer_order_creation_context->payments) {
                foreach ($customer_order_creation_context->payments as $i => $payment) {
                    $payment->setDataLoader($this->data_loader);
                    $result = $this->customer_order_payment->create($payment);

                    // It's an INSERT ... SELECT
                    // So if the payment method does not exist, the payment is not inserted
                    // we need to rollback if that happen
                    if (0 === $result) {
                        throw new \UnexpectedValueException(sprintf('Could not create payment with payment method "%s". Does it exist ?', $payment->payment_mean));
                    }

                    $payment->extra_data['payment_line_nb'] = $i + 1;

                    if (
                        PaymentWorkflow::V2 === $payment->workflow &&
                        $customer_order_context_from_request->payments[$i]->payment_mean === $payment->payment_mean
                    ) {
                        $customer_order_context_from_request->payments[$i]->setCreatedProof($payment->created_proof);
                    }

                    if (
                        in_array(
                            $payment->payment_mean,
                            [PaymentMean::PRESTO, PaymentMean::CREDIT_CARD_OGONE_TELEPHONE],
                            true
                        )
                    ) {
                        $this->customer_order_repository->activateCustomerReminder($payment->customer_order_id);
                    }

                    if (PaymentMean::SVD_GIFT_CARD === $payment->payment_mean) {
                        $gift_card = $this->svd_gift_card_manager->handlePayment($payment);
                        $this->svd_gift_card_manager->burn($gift_card);
                    }
                }

                if (!$customer_order_creation_context->hasTag(CustomerOrderTag::STATUS_IMPORT)) {
                    $this->checkAmount($customer_order_creation_context);
                }
            }

            // create tags
            foreach ($customer_order_creation_context->tags as $tag) {
                $tag->setDataLoader($this->data_loader);
                $this->customer_order_repository->tag($tag);
            }

            if (
                !is_null($customer_order_creation_context->clone_customer_order_id) &&
                CustomerOrderOrigin::BACKOFFICE === $customer_order_creation_context->origin
            ) {
                $this->customer_order_repository->addComment(
                    $data['customer_order_id'],
                    sprintf(
                        'Cette commande est le clone de la commande %s.',
                        $customer_order_creation_context->clone_customer_order_id
                    ),
                    $this->current_user->entity()->utilisateur
                );
            }

            if (!empty($customer_order_creation_context->quote_id)) {
                $this->quote_repository->update($customer_order_creation_context->quote_id, ['is_ordered' => true]);
            }

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        // MySQL Transaction is finished, create the payment in Payment V2
        if (
            in_array(
                PaymentWorkflow::V2,
                array_column($customer_order_context_from_request->payments, 'workflow'),
                true
            )
        ) {
            try {
                $response = $this->payment_v2_creator->create(
                    $customer_order_context_from_request,
                    $customer_order_creation_context->customer_order_id
                );

                $this->logger->debug('[Payment V2] Creation Response', $response);

                return new CreatedCustomerOrderWithPaymentAction(
                    $customer_order_creation_context->customer_order_id,
                    $response['action']
                );
            } catch (\Exception $exception) {
                // Do not prevent returning to the caller
                // Log it for manual intervention and continue with normal process
                $this->logger->error('An error occurred', [
                    'exception' => new \Exception(
                        '[Payment V2] Failed to create the payment',
                        $exception->getCode(),
                        $exception
                    ),
                ]);

                return new CreatedCustomerOrderWithPaymentAction($customer_order_creation_context->customer_order_id, [
                    'cancel' => true,
                ]);
            }
        }

        return new CreatedCustomerOrder($customer_order_creation_context->customer_order_id);
    }

    private function addProductToOrder(CustomerOrderProductCreationContextEntity $product): void
    {
        $product->setDataLoader($this->data_loader);
        try {
            $this->customer_order_product_repository->create($product);
            $this->customer_order_product_repository->createStatInitial($product);
        } catch (\Throwable $e) {
            // ignore failure on catalog and magazine product
            if (!in_array($product->product_id, [Product::CATALOG_PRODUCT_ID, Product::MAGAZINE_PRODUCT_ID], true)) {
                throw $e;
            }
        }
    }

    /**
     * Customer order payments should be equal to sum of products amounts.
     * If not, add comment on customer order + send a task to concerned user.
     */
    private function checkAmount(CustomerOrderCreationContextEntity $customer_order_creation_context): void
    {
        $comment = 'La somme des paiements est différente de la somme des produits.';

        $payments_amount = 0.0;
        foreach ($customer_order_creation_context->payments as $payment) {
            $payments_amount += $payment->amount;
        }
        $products_amount = 0.0;
        foreach ($customer_order_creation_context->products as $product) {
            $selling_price =
                'non' === $customer_order_creation_context->is_excluding_tax
                    ? $product->selling_price_tax_included
                    : $product->selling_price_tax_included / (1 + $product->vat);

            $products_amount +=
                ($selling_price +
                    $product->warranty_unit_selling_price_ext +
                    $product->warranty_unit_selling_price_tb) *
                    $product->quantity +
                $product->discount_amount;
        }

        if (round($payments_amount, 2) !== round($products_amount, 2)) {
            $this->customer_order_repository->addComment(
                $customer_order_creation_context->customer_order_id,
                $comment,
                UserEntity::SYSTEM_USERNAME
            );

            $this->task_repository->create(
                UserEntity::CATHY_ID,
                $comment,
                null,
                null,
                $customer_order_creation_context->customer_order_id
            );
        }
    }

    /** @throws CustomerOrderAlreadyExistsException */
    private function checkIfExistWithExternalReference(CustomerOrderCreationContextDto $customer_order): void
    {
        if (!$customer_order->original_customer_order_id) {
            return;
        }

        // Check if order already exists for marketplace kind of orders
        $existing_customer_order_id = $this->customer_order_repository->fetchOrderIdByOriginalOrderIdIfExists(
            $customer_order->original_customer_order_id,
            $customer_order->origin
        );

        if ($existing_customer_order_id) {
            throw new CustomerOrderAlreadyExistsException($existing_customer_order_id, $customer_order->original_customer_order_id);
        }
    }
}
