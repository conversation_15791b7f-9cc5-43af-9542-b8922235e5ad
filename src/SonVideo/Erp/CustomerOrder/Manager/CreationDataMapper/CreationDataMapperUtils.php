<?php

namespace SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper;

use SonVideo\Erp\Product\Entity\ProductV2Entity;
use SonVideo\Erp\Referential\Product;

final class CreationDataMapperUtils
{
    /** @return array{customer_order_id: int, product_id: mixed, type: mixed, quantity: int, selling_price_tax_included: float, vat: float, description: mixed, ecotax_price: float, sorecop_price: float, discount_type: null, discount_description: null, discount_amount: float, group_type: null, group_description: null} */
    public static function getShipmentMethodLine(int $customer_order_id, float $cost, float $vat): array
    {
        return [
            'customer_order_id' => $customer_order_id,
            'product_id' => Product::SHIPMENT_PRODUCT_ID,
            'type' => Product::TYPE_GENERIQUE,
            'quantity' => 1,
            'selling_price_tax_included' => $cost,
            'vat' => $vat,
            'description' => ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            'ecotax_price' => 0.0,
            'sorecop_price' => 0.0,
            'discount_type' => null,
            'discount_description' => null,
            'discount_amount' => 0.0,
            'group_type' => null,
            'group_description' => null,
        ];
    }

    public static function getPaymentsForMarketplace(array $data, string $origin, string $payment_mean): array
    {
        return [
            [
                'origin' => $origin,
                'amount' => array_sum(array_column($data['payments'], 'amount')),
                'created_at' => $data['created_at'] ?? date('Y-m-d') . ' 00:00:00',
                'unique_id' => '1',
                'payment_mean' => $payment_mean,
                'created_proof' => $data['original_customer_order_id'] . '-1',
                'customer_order_id' => $data['customer_order_id'],
            ],
        ];
    }
}
