<?php

namespace SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper;

use App\Contract\DataLoaderAwareInterface;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use App\Formatter\String\StringFormatter;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use SonVideo\Erp\Country\Mysql\Repository\CountryRepository;
use SonVideo\Erp\CustomerOrder\Contract\CustomerOrderCreationDataMapperInterface;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\Product\Mysql\Repository\ProductV2Repository;
use SonVideo\Erp\Referential\CustomerOrderInternalStatus;
use SonVideo\Erp\Referential\CustomerOrderIpAddress;

abstract class AbstractMarketplaceCreationDataMapper implements CustomerOrderCreationDataMapperInterface, DataLoaderAwareInterface
{
    use MapToEntityTrait;

    protected CountryRepository $country_repository;

    protected ShipmentMethodReadRepository $shipment_method_read_repository;

    private ProductV2Repository $product_repository;

    protected const CUSTOMER_ORDER_ORIGIN = null;
    protected const PAYMENT_MEAN = null;

    /** @throws \Exception */
    public function __construct(
        CountryRepository $country_repository,
        ShipmentMethodReadRepository $shipment_method_read_repository,
        ProductV2Repository $product_repository
    ) {
        $this->country_repository = $country_repository;
        $this->shipment_method_read_repository = $shipment_method_read_repository;
        $this->product_repository = $product_repository;
        if (null === static::CUSTOMER_ORDER_ORIGIN || null === static::PAYMENT_MEAN) {
            throw new \Exception('error in class construction');
        }
    }

    /** {@inheritDoc} */
    abstract public function canHandle(string $key): bool;

    abstract public function getTagsLines(int $customer_order_id, array $data): array;

    public function map(array $data): CustomerOrderCreationContextEntity
    {
        $shipping_address_country_id = $this->country_repository->getFrom2LettersCode(
            $data['shipping_address']['country_code']
        )->country_id;

        $vat = $this->country_repository->getVat($shipping_address_country_id);

        // products
        $customer_order_products = $this->getProductsLines($data, $vat);

        // shipment method product
        $customer_order_products[] = CreationDataMapperUtils::getShipmentMethodLine(
            $data['customer_order_id'],
            $data['shipment_method']['cost'],
            $vat
        );

        // payments
        $customer_order_payments = CreationDataMapperUtils::getPaymentsForMarketplace(
            $data,
            static::CUSTOMER_ORDER_ORIGIN,
            static::PAYMENT_MEAN
        );

        // tags
        $customer_order_tags = $this->getTagsLines($data['customer_order_id'], $data);

        $data = $this->overloadData($data);

        $customer_order_data = array_merge($data, [
            'new_customer' => true,
            'internal_status' => CustomerOrderInternalStatus::ON_GOING,
            'invoice_comment' => '',
            'billing_address_company_name' => $data['billing_address']['company_name'] ?? '',
            'billing_address_civility' => $data['billing_address']['civility'],
            'billing_address_firstname' => $data['billing_address']['firstname'],
            'billing_address_lastname' => $data['billing_address']['lastname'],
            'billing_address_address' => $data['billing_address']['address'],
            'billing_address_postal_code' => $data['billing_address']['postal_code'],
            'billing_address_city' => $data['billing_address']['city'],
            'billing_address_country_id' => $this->country_repository->getFrom2LettersCode(
                $data['billing_address']['country_code']
            )->country_id,
            'billing_address_phone' => $data['billing_address']['phone'],
            'billing_address_cellphone' => $data['billing_address']['cellphone'],
            'billing_address_email' => $data['billing_address']['email'],
            'shipping_address_company_name' => $data['shipping_address']['company_name'] ?? '',
            'shipping_address_civility' => $data['shipping_address']['civility'],
            'shipping_address_firstname' => $data['shipping_address']['firstname'],
            'shipping_address_lastname' => $data['shipping_address']['lastname'],
            'shipping_address_address' => $data['shipping_address']['address'],
            'shipping_address_postal_code' => $data['shipping_address']['postal_code'],
            'shipping_address_city' => $data['shipping_address']['city'],
            'shipping_address_country_id' => $shipping_address_country_id,
            'shipping_address_phone' => $data['shipping_address']['phone'],
            'shipping_address_cellphone' => $data['shipping_address']['cellphone'],
            'shipping_address_email' => $data['shipping_address']['email'],
            'carrier_id' => null,
            'shipment_method_id' => null,
            'store_pickup_id' => null,
            'payments' => $customer_order_payments,
            'products' => $customer_order_products,
            'relay_id' => null,
            'chrono_precise_appointment' => null,
            'svd_header' => false,
            'ip_address' => CustomerOrderIpAddress::DEFAULT,
            'customer_id' => null,
            'tags' => $customer_order_tags,
            'is_excluding_tax' => true === $data['is_excluding_tax'] ? 'oui' : 'non',
        ]);

        /** @var $customer_order CustomerOrderCreationContextEntity */
        $customer_order = $this->hydrateEntity($customer_order_data, CustomerOrderCreationContextEntity::class);

        return $customer_order;
    }

    public function overloadData($data): array
    {
        return $data;
    }

    /** @throws NotFoundException */
    private function getProductsLines(array $data, float $vat): array
    {
        $customer_order_products = [];
        foreach ($data['products'] as $product) {
            $product_info = $this->product_repository->getOneByIdOrSku($product['sku']);
            $customer_order_products[] = array_merge($product, [
                'customer_order_id' => $data['customer_order_id'],
                'product_id' => $product_info->product_id,
                'vat' => $vat,
                'discount_amount' => $product['unit_discount_amount'] * $product['quantity'],
                'description' => StringFormatter::transliterate($product['description']),
                'type' => $product_info->product_type,
                'ecotax_price' => $product['ecotax_price'] ?? $product_info->ecotax,
                'sorecop_price' => $product['sorecop_price'] ?? $product_info->sorecop,
            ]);
        }

        return $customer_order_products;
    }
}
