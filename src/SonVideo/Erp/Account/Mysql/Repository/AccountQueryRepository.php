<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Account\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Sql\Helper\MysqlEnumValuesExtractorTrait;
use App\Sql\Helper\Pager;
use App\Sql\Helper\Where;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Account\Entity\SellerCommissionInfos;
use SonVideo\Erp\User\Entity\UserEntity;

class AccountQueryRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface, SerializerAwareInterface
{
    use LegacyPdoAwareTrait;
    use DataLoaderAwareTrait;
    use MysqlEnumValuesExtractorTrait;
    use SerializerAwareTrait;

    public const COLUMNS_MAPPING = [
        'user_id' => 'u.id_utilisateur',
        'username' => 'u.utilisateur',
        'is_active' => 'u.status',
        'user_groups' => 'u.groupe',
        'company' => 'u.societe',
        'site' => 'u.site',
        'job' => 'u.titre',
        'civility' => 'u.civilite',
        'last_name' => 'u.nom',
        'first_name' => 'u.prenom',
        'email' => 'u.email',
        'internal_phone_number' => 'u.poste',
        'mobile_phone_number' => 'u.mobile',
        'is_employee' => 'u.employe',
        'warehouse_id' => 'sgup.warehouse_id',
        'seller_commission_role' => 'sgup.seller_commission_role',
        'seller_commission_role_level' => 'sgup.seller_commission_role_level',
        'signature' => 'sgup.signature',
    ];

    public const ENUM_MAPPINGS = [
        'seller_commission_role' => 'roles',
        'seller_commission_role_level' => 'role_levels',
    ];

    /** @throws NotFoundException */
    public function getUser(string $username): UserEntity
    {
        $where = (new Where())->addCondition('utilisateur', 'utilisateur', $username);
        $sql = <<<MYSQL
        SELECT
          id_utilisateur AS id_utilisateur,
          utilisateur    AS utilisateur,
          status         AS status,
          groupe         AS groupes,
          societe        AS societe,
          site           AS site,
          titre          AS titre,
          civilite       AS civilite,
          nom            AS nom,
          prenom         AS prenom,
          email          AS email,
          employe        AS employe,
          signature      AS signature,
          warehouse_id   AS warehouse_id,
          seller_commission_role AS seller_commission_role
          FROM backOffice.utilisateur {where}
        MYSQL;
        $sql = strtr($sql, ['{where}' => $where]);

        $user = $this->legacy_readonly_pdo->fetchOne($sql, $where->getParams());

        if (false === $user) {
            throw new NotFoundException(sprintf('User with username "%s" not found.', $username));
        }

        return $this->data_loader->hydrate($user, UserEntity::class);
    }

    /** @throws NotFoundException */
    public function findOneById(int $user_id, ?string $status = 'active'): UserEntity
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.utilisateur
        WHERE id_utilisateur = :user_id
        MYSQL;

        if (!is_null($status)) {
            $sql .= ' AND status = :status;';
        }

        $user = $this->legacy_readonly_pdo->fetchOne($sql, ['user_id' => $user_id, 'status' => $status]);

        if (!$user) {
            throw new NotFoundException(sprintf('User with id "%s" not found.', $user_id));
        }

        return $this->data_loader->hydrate($user, UserEntity::class);
    }

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<MYSQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                u.id_utilisateur                                AS user_id,
                u.utilisateur                                   AS username,
                u.status = 'active'                             AS is_active,
                u.groupe                                        AS user_groups,
                u.societe                                       AS company,
                u.site                                          AS site,
                u.titre                                         AS job,
                u.civilite                                      AS civility,
                u.nom                                           AS last_name,
                u.prenom                                        AS first_name,
                u.email                                         AS email,
                u.poste                                         AS internal_phone_number,
                u.mobile                                        AS mobile_phone_number,
                u.employe = 'oui'                               AS is_employee,
                backOffice.GET_COMPUTED_USER_NAME_BY_ID(sgu.id) AS full_name,
                backOffice.USR_codebarre(sgu.id, sgu.password)  AS barcode,
                sgup.warehouse_id                               AS warehouse_id,
                sgup.seller_commission_role                     AS seller_commission_role,
                sgup.seller_commission_role_level               AS seller_commission_role_level,
                sgup.signature                                  AS signature
                FROM backOffice.utilisateur u
                  INNER JOIN backOffice.sf_guard_user         sgu ON u.id_utilisateur = sgu.id
                  INNER JOIN backOffice.sf_guard_user_profile sgup ON u.id_utilisateur = sgup.id
                WHERE {where}
              ) tmp
        {order_by}
        MYSQL;

        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function fetchSuppliers(array $usernames): array
    {
        $sql = <<<MYSQL
        SELECT
            sgu.username,
            f.id_fournisseur AS supplier_id,
            f.fournisseur AS name
        FROM backOffice.sf_guard_user sgu
        INNER JOIN backOffice.Bo_frn_user fu ON sgu.id = fu.id_user
        INNER JOIN backOffice.fournisseur f ON fu.id_fournisseur = f.id_fournisseur
        WHERE sgu.username IN (:usernames)
        MYSQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['usernames' => $usernames]);
    }

    public function fetchSellerCommissionInfos(): SellerCommissionInfos
    {
        $sql = <<<MYSQL
        SHOW COLUMNS FROM backOffice.sf_guard_user_profile LIKE 'seller_commission_role%';
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchAll($sql);
        $extracted = [];

        foreach ($result as $item) {
            $extracted[static::ENUM_MAPPINGS[$item['Field']]] = $this->extractEnumValuesFrom($item['Type']);
        }

        return $this->data_loader->hydrate($extracted, SellerCommissionInfos::class);
    }
}
