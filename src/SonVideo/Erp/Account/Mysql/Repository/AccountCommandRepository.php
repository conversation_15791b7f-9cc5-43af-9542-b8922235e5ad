<?php

namespace SonVideo\Erp\Account\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use SonVideo\Erp\Account\Dto\AccountProfileUpdateRequestDto;

class AccountCommandRepository implements LegacyPdoAwareInterface, SerializerAwareInterface
{
    use LegacyPdoAwareTrait;
    use SerializerAwareTrait;

    public function updateProfileWith(AccountProfileUpdateRequestDto $account_profile_update_request_dto): int
    {
        $sql = <<<MYSQL
        UPDATE backOffice.sf_guard_user_profile
          SET
        warehouse_id = :warehouse_id,
        seller_commission_role = :seller_commission_role,
        seller_commission_role_level = :seller_commission_role_level,
        nom = :last_name,
        prenom = :first_name,
        signature = :signature
          WHERE id = :user_id
        MYSQL;

        $this->legacy_pdo->fetchAffected($sql, $this->serializer->normalize($account_profile_update_request_dto));

        return $account_profile_update_request_dto->user_id;
    }

    public function clone(string $username): void
    {
        // TODO
    }
}
