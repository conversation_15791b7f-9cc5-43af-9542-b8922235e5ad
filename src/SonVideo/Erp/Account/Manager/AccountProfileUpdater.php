<?php

namespace SonVideo\Erp\Account\Manager;

use SonVideo\Erp\Account\Dto\AccountProfileUpdateRequestDto;
use SonVideo\Erp\Account\Mysql\Repository\AccountCommandRepository;

class AccountProfileUpdater
{
    private AccountCommandRepository $account_command_repository;

    public function __construct(AccountCommandRepository $account_command_repository)
    {
        $this->account_command_repository = $account_command_repository;
    }

    /** @return array{user_id: int} */
    public function update(AccountProfileUpdateRequestDto $dto): array
    {
        return ['user_id' => $this->account_command_repository->updateProfileWith($dto)];
    }
}
