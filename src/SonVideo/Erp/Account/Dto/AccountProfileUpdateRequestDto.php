<?php

namespace SonVideo\Erp\Account\Dto;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class AccountProfileUpdateRequestDto
{
    /**
     * @OA\Property(example=1346)
     *
     * @var int
     */
    public $user_id;

    /**
     * @OA\Property(example=21)
     *
     * @var int|null
     */
    public $warehouse_id;

    /**
     * @OA\Property(example="CALL_CENTER_SELLER")
     *
     * @var string|null
     */
    public $seller_commission_role;

    /**
     * @OA\Property(example="0")
     *
     * @var string|null
     */
    public $seller_commission_role_level;

    /**
     * @OA\Property(example="Toto")
     *
     * @var string|null
     */
    public $first_name;

    /**
     * @OA\Property(example="Alaplage")
     *
     * @var string|null
     */
    public $last_name;

    /**
     * @OA\Property(example="Ma signature")
     * @Assert\Type("string")
     *
     * @var string
     */
    public $signature;
}
