<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\DeliveryNote\Mysql\Repository;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\DeliveryNote\Entity\DeliveryNoteEntity;
use SonVideo\Erp\Entity\DeliveryTicketActivityLog;
use SonVideo\Erp\Entity\DeliveryTicketTrackingLog;
use SonVideo\Erp\Referential\DeliveryTicketLogStatus;
use SonVideo\Erp\Referential\DeliveryTicketWorkflowStatus;

/**
 * Class DeliveryNoteRepository.
 */
class DeliveryNoteRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'delivery_note_id' => 'bl.id_bon_livraison',
        'status' => 'if(cs.statut IS NOT NULL, \'livre\', bl.status)',
        'warehouse_id' => 'bl.id_depot',
        'customer_order_id' => 'bl.id_commande',
        'transfer_id' => 'bl.id_transfert',
        'invoice_id' => 'bl.id_facture',
        'creation_date' => 'bl.date_creation',
        'carrier_id' => 'bl.id_transporteur',
        'carrier_product_id' => 'bl.id_pdt_transporteur',
        'transfer_status' => 't.statut',
        'transfer_destination_warehouse_id' => 't.id_depot_arrivee',
        'prepared_at' => 'bl.date_export_transporteur',
        'store_pickup_started_at' => 'bl.store_pickup_started_at',
        'picked_by' => 'bl.picked_by',
        'workflow_status' => 'bl.workflow_status',
        'is_pickup' => 'IF(d.id_transporteur_emport IS NOT NULL, 1, 0)',
        'tracking_id' => 'COALESCE(cft.tracking_id, c.no_colis)',
        'shipment_method_id' => 'bl.id_pdt_transporteur',
        'address' => 'bl.cnt_adresse',
        'city' => 'bl.cnt_ville',
        'civility' => 'bl.cnt_civilite',
        'cellphone' => 'bl.cnt_mobile',
        'country_id' => 'bl.cnt_id_pays',
        'email' => 'bl.cnt_email',
        'firstname' => 'bl.cnt_prenom',
        'lastname' => 'bl.cnt_nom',
        'phone' => 'bl.cnt_telephone',
        'postal_code' => 'bl.cnt_code_postal',
        'company' => 'bl.cnt_societe',
    ];

    protected DeliveryNoteProductRepository $delivery_note_product_repo;

    /** LocationRepository constructor. */
    public function __construct(LegacyPdo $legacy_pdo, DeliveryNoteProductRepository $delivery_note_product_repo)
    {
        parent::__construct($legacy_pdo);

        $this->delivery_note_product_repo = $delivery_note_product_repo;
    }

    /** @throws NotFoundException */
    public function findOneById(int $delivery_note_id): DeliveryNoteEntity
    {
        $sql = <<<SQL
        {base_sql}
        WHERE bl.id_bon_livraison = :delivery_note_id
        SQL;

        $sql = strtr($sql, ['{base_sql}' => $this->getBaseSql()]);

        $delivery_note = $this->legacy_pdo->fetchOne($sql, ['delivery_note_id' => $delivery_note_id]);

        if (false === $delivery_note) {
            throw new NotFoundException('Delivery note not found.');
        }

        $delivery_note = $this->data_loader->hydrate($delivery_note, DeliveryNoteEntity::class);

        // structure data as expected
        $delivery_note_products = $this->delivery_note_product_repo->findAllForOneId($delivery_note_id);
        $delivery_note->fromArray(['delivery_note_products' => $delivery_note_products]);

        return $delivery_note;
    }

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              {base_sql}
              WHERE {conditions}
            ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    private function getBaseSql(): string
    {
        return <<<SQL
        SELECT
        DISTINCT
          bl.id_bon_livraison                                           AS delivery_note_id,
          IF(cs.statut IS NOT NULL, 'livre', bl.status)                 AS status,
          bl.id_depot                                                   AS warehouse_id,
          bl.id_commande                                                AS customer_order_id,
          bl.id_transfert                                               AS transfer_id,
          bl.id_facture                                                 AS invoice_id,
          bl.date_creation                                              AS creation_date,
          bl.id_transporteur                                            AS carrier_id,
          bl.id_pdt_transporteur                                        AS carrier_product_id,
          t.statut                                                      AS transfer_status,
          t.id_depot_arrivee                                            AS transfer_destination_warehouse_id,
          bl.date_export_transporteur                                   AS prepared_at,
          bl.store_pickup_started_at                                    AS store_pickup_started_at,
          bl.picked_by                                                  AS picked_by,
          backOffice.GET_COMPUTED_USER_NAME_BY_USERNAME(bl.picked_by)   AS picked_by_name,
          bl.workflow_status                                            AS workflow_status,
          IF(d.id_transporteur_emport IS NOT NULL, 1, 0)                AS is_pickup,
          CASE
              WHEN beeb.bon_livraison_id IS NOT NULL THEN beeb.statut = 4
              ELSE FALSE
          END                                                           AS is_prepared_in_expedition,
          COALESCE(cft.tracking_id, c.no_colis)                         AS tracking_id
          FROM
            backOffice.bon_livraison                                bl
              LEFT JOIN backOffice.BO_STK_transfert      t ON bl.id_transfert = t.id
              LEFT JOIN backOffice.BO_EXP_expeditions_bl beeb ON bl.id_bon_livraison = beeb.bon_livraison_id
              LEFT JOIN backOffice.BO_STK_depot d ON bl.id_transporteur = d.id_transporteur_emport
              LEFT JOIN backOffice.colis c ON bl.id_bon_livraison = c.id_bon_livraison
              LEFT JOIN backOffice.colis_franceexpress_tracking cft ON c.id_colis = cft.id_colis
              LEFT JOIN backOffice.colis_statut cs ON c.id_colis = cs.id_colis AND cs.statut = 'livre'
        SQL;
    }

    /** @throws SqlErrorMessageException */
    public function autoPickVirtualProducts(int $delivery_note_id): ?string
    {
        $sql = 'CALL backOffice.WMS_auto_pick_virtual_product(:delivery_note_id)';

        return $this->legacy_pdo->fetchValue($sql, ['delivery_note_id' => $delivery_note_id]);
    }

    /** @throws SqlErrorMessageException */
    public function autoArrangeVirtualProducts(int $delivery_note_id): ?string
    {
        $sql = 'CALL backOffice.WMS_auto_arrange_virtual_product_after_picking_abortion(:delivery_note_id)';

        return $this->legacy_pdo->fetchValue($sql, ['delivery_note_id' => $delivery_note_id]);
    }

    /** @throws NotFoundException */
    public function findAllActivityById(int $delivery_note_id): array
    {
        $sql = <<<SQL
        SELECT
          dtal.delivery_ticket_activity_log_id                     AS delivery_ticket_activity_log_id,
          dtal.delivery_ticket_id                                  AS delivery_ticket_id,
          dtal.delivery_ticket_id_without_fk                       AS delivery_ticket_id_without_fk,
          dtal.action                                              AS action,
          dtal.description                                         AS description,
          dtal.created_at                                          AS created_at,
          dtal.created_by                                          AS created_by,
          backOffice.GET_COMPUTED_USER_NAME_BY_ID(dtal.created_by) AS created_by_name,
          dtal.foreign_id                                          AS foreign_id
          FROM backOffice.delivery_ticket_activity_log dtal
          WHERE dtal.delivery_ticket_id_without_fk = :delivery_note_id
        ORDER BY created_at
        SQL;

        $lines = $this->legacy_pdo->fetchAssoc($sql, ['delivery_note_id' => $delivery_note_id]);
        if ([] === $lines) {
            throw new NotFoundException(sprintf('Found no activity for delivery ticket with id %d', $delivery_note_id));
        }

        $results = [];
        foreach ($lines as $activity) {
            $results[] = $this->data_loader->hydrate($activity, DeliveryTicketActivityLog::class)->toArray();
        }

        return $results;
    }

    /** @throws NotFoundException */
    public function findAllTrackingById(int $delivery_note_id): array
    {
        $sql = <<<SQL
        SELECT
          cs.id_statut                                             AS parcel_statut_id,
          c.id_bon_livraison                                       AS delivery_ticket_id,
          IF(cs.statut = 'livre', 'DELIVERED', 'DELIVERING')       AS action,
          cs.description                                           AS description,
          COALESCE(cs.statut_date, cs.created_at)                  AS created_at,
          1000                                                     AS created_by,
          backOffice.GET_COMPUTED_USER_NAME_BY_ID(1000)            AS created_by_name
          FROM
            colis                     c
              INNER JOIN colis_statut cs ON c.id_colis = cs.id_colis
          WHERE c.id_bon_livraison = :delivery_note_id
        ORDER BY created_at
        SQL;

        $lines = $this->legacy_pdo->fetchAssoc($sql, ['delivery_note_id' => $delivery_note_id]);

        $results = [];
        foreach ($lines as $tracking) {
            $results[] = $this->data_loader->hydrate($tracking, DeliveryTicketTrackingLog::class)->toArray();
        }

        return $results;
    }

    /** Mark the delivery note as started to pickup */
    public function startStorePickup(int $delivery_note_id): DeliveryNoteRepository
    {
        $sql = <<<SQL
        UPDATE backOffice.bon_livraison
        SET store_pickup_started_at = now()
        WHERE id_bon_livraison = :delivery_note_id
          AND store_pickup_started_at is null
        SQL;

        $result = $this->legacy_pdo->fetchAffected($sql, ['delivery_note_id' => $delivery_note_id]);

        if (0 === $result) {
            throw new \UnexpectedValueException('Failed to mark the store pickup as started');
        }

        return $this;
    }

    /** Retrieve data about upcoming store pickups */
    public function findStorePickupStarted(int $warehouse_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM (
            SELECT
              bl.id_bon_livraison AS delivery_note_id,
              c.id_commande AS customer_order_id,
              bl.store_pickup_started_at AS store_pickup_started_at,
              cnt_lvr_nom AS last_name,
              cnt_lvr_prenom AS first_name,
              workflow_status AS workflow_status,
              bl.picked_by,
              GET_COMPUTED_USER_NAME_BY_USERNAME(bl.picked_by) AS picked_by_name
            FROM
              backOffice.bon_livraison bl
              INNER JOIN backOffice.commande c ON bl.id_commande = c.id_commande
              INNER JOIN backOffice.BO_STK_depot d ON bl.id_transporteur = d.id_transporteur_emport AND bl.id_depot = d.id
            WHERE store_pickup_started_at IS NOT NULL
              AND id_depot = :warehouse_id
              AND workflow_status != :validated
          ) tmp
        ORDER BY store_pickup_started_at
        SQL;

        return $this->legacy_pdo->paginateObjects(1, 100, $sql, [
            'warehouse_id' => $warehouse_id,
            'validated' => DeliveryTicketWorkflowStatus::VALIDATED,
        ]);
    }

    public function generateDeliveryNote(
        int $customer_order_id,
        int $carrier_id,
        string $user,
        int $shipment_method_id
    ): array {
        $sql = <<<SQL
        CALL backOffice.CMD_fait_bls(:customer_order_id, :carrier_id, :user, :shipment_method_id);
        SQL;

        return $this->legacy_pdo->fetchCollection($sql, [
            'customer_order_id' => $customer_order_id,
            'carrier_id' => $carrier_id,
            'user' => $user,
            'shipment_method_id' => $shipment_method_id,
        ]);
    }

    public function findDeliveryNotesBlockingPartialInventory(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                DISTINCT
                bl.id_bon_livraison                              AS delivery_note_id,
                bl.id_commande                                   AS customer_order_id,
                bl.id_transfert                                  AS transfer_id,
                bl.date_creation                                 AS creation_date
              FROM backOffice.bon_livraison bl
                INNER JOIN backOffice.produit_bon_livraison pbl ON bl.id_bon_livraison = pbl.id_bon_livraison
                INNER JOIN backOffice.WMS_product_location wpl
                    ON wpl.quantity > 0
                    AND (pbl.id_produit = wpl.product_id
                        OR wpl.delivery_ticket_id = bl.id_bon_livraison)
                INNER JOIN backOffice.BO_INV_zone_location bizl ON bizl.location_id = wpl.location_id
                INNER JOIN backOffice.BO_INV_zone_inventory bizi ON bizi.zone_id = bizl.zone_id
                INNER JOIN backOffice.BO_INV_inventaire bii
                    ON bizi.inventory_id = bii.id
                    AND bii.id_depot = bl.id_depot
                WHERE bl.status IN ('au depart', 'en transfert')
                    AND bl.date_export_transporteur IS NULL
                        AND bl.id_bon_livraison NOT IN (
                            SELECT _wpl.delivery_ticket_id
                            FROM backOffice.WMS_product_location _wpl
                                INNER JOIN backOffice.WMS_location _wl ON _wpl.location_id = _wl.location_id
                                INNER JOIN backOffice.WMS_area _wa ON _wl.area_id = _wa.area_id
                            WHERE _wpl.quantity > 0
                                AND _wa.area_type_id = 9 -- Emport
                        )
                    AND bizi.inventory_id = :inventory_id
              ORDER BY creation_date DESC
            ) tmp
        SQL;

        return $this->legacy_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }

    public function findDeliveryNotesBlockingProductInventory(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                DISTINCT
                bl.id_bon_livraison                              AS delivery_note_id,
                bl.id_commande                                   AS customer_order_id,
                bl.id_transfert                                  AS transfer_id,
                bl.date_creation                                 AS creation_date
              FROM backOffice.bon_livraison bl
                INNER JOIN backOffice.produit_bon_livraison pbl ON bl.id_bon_livraison = pbl.id_bon_livraison
                INNER JOIN backOffice.WMS_product_location wpl
                    ON wpl.quantity > 0
                    AND (pbl.id_produit = wpl.product_id
                        OR wpl.delivery_ticket_id = bl.id_bon_livraison)
                INNER JOIN backOffice.BO_INV_zone_location bizl ON bizl.location_id = wpl.location_id
                INNER JOIN backOffice.BO_INV_zone_inventory bizi ON bizi.zone_id = bizl.zone_id
                INNER JOIN backOffice.BO_INV_inventaire bii
                    ON bizi.inventory_id = bii.id
                    AND bii.id_depot = bl.id_depot
                 -- No LATERAL in Mysql5 so we have to use subqueries for 'id_depot'
                INNER JOIN (
                    -- find location containing products that have not been counted this year
                    SELECT bizl.location_id
                    FROM backOffice.BO_INV_zone_inventory bizi
                      INNER JOIN backOffice.BO_INV_zone_location bizl ON bizi.zone_id = bizl.zone_id
                      INNER JOIN backOffice.WMS_location wl ON bizl.location_id = wl.location_id
                      LEFT JOIN backOffice.WMS_product_location wpl ON wl.location_id = wpl.location_id
                      LEFT JOIN backOffice.article a ON wpl.product_id = a.id_produit
                      LEFT JOIN backOffice.BO_INV_differential bid ON a.id_produit = bid.product_id
                      LEFT JOIN backOffice.BO_INV_inventaire _bii ON
                        bid.inventory_id = _bii.id
                        AND _bii.id_depot = (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                    WHERE
                      backOffice.PDT_ART_qte_stock_depot(
                        a.id_produit,
                        (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                      ) > 0
                      AND a.compose = 0
                      AND a.is_auto_picked = 0
                      AND wpl.quantity > 0
                      AND bizi.inventory_id = :inventory_id
                      AND wl.is_active = 1
                    GROUP BY wl.location_id
                    HAVING MAX(_bii.inv_date_closed) < YEAR(NOW()) OR MAX(_bii.id) IS NULL
                    ) inv_location ON wpl.location_id = inv_location.location_id
                WHERE bl.status IN ('au depart', 'en transfert')
                    AND bl.date_export_transporteur IS NULL
                        AND bl.id_bon_livraison NOT IN (
                            SELECT _wpl.delivery_ticket_id
                            FROM backOffice.WMS_product_location _wpl
                                INNER JOIN backOffice.WMS_location _wl ON _wpl.location_id = _wl.location_id
                                INNER JOIN backOffice.WMS_area _wa ON _wl.area_id = _wa.area_id
                            WHERE _wpl.quantity > 0
                                AND _wa.area_type_id = 9 -- Emport
                        )
                    AND bizi.inventory_id = :inventory_id
              ORDER BY creation_date DESC
            ) tmp
        SQL;

        return $this->legacy_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }

    /**
     * Update a delivery note.
     *
     * @param array<string,mixed> $data
     */
    public function updateDeliveryNote(int $delivery_note_id, array $data): void
    {
        // Parse postal address to match the database format
        if (isset($data['address']) && is_array($data['address'])) {
            $data['address'] = implode(PHP_EOL, $data['address']);
        }

        $sql = <<<'SQL'
            UPDATE backOffice.bon_livraison bl SET {set} WHERE bl.id_bon_livraison = :delivery_note_id
        SQL;

        $set = array_map(fn ($key): string => static::COLUMNS_MAPPING[$key] . ' = :' . $key, array_keys($data));

        $this->legacy_pdo->fetchAffected(
            strtr($sql, ['{set}' => implode(', ', $set)]),
            ['delivery_note_id' => $delivery_note_id] + $data
        );
    }

    /**
     * Set delivery slip entries as validated if there's a log entry marking them as validated.
     *
     * @throws \DateMalformedStringException
     */
    public function validatePickupStartedNDaysAgo(int $days, ?int $warehouse_id = null): bool
    {
        $n_days_ago = (new \DateTime())->modify("-{$days} days")->format('Y-m-d');

        $sql = <<<SQL
            UPDATE backOffice.bon_livraison bl
                INNER JOIN backOffice.delivery_ticket_activity_log dtal
                    ON dtal.delivery_ticket_id = bl.id_bon_livraison
                    AND dtal.action = :validated
            SET bl.workflow_status = :validated
            WHERE {warehouse_clause}
                AND bl.workflow_status != :validated
                AND bl.store_pickup_started_at IS NOT NULL
                AND bl.store_pickup_started_at > :pickup_started_at
        SQL;

        return $this->legacy_pdo->fetchAffected(
            strtr($sql, ['{warehouse_clause}' => null !== $warehouse_id ? 'bl.id_depot = :warehouse_id' : '1']),
            [
                'validated' => DeliveryTicketLogStatus::VALIDATED,
                'pickup_started_at' => $n_days_ago,
                'warehouse_id' => $warehouse_id,
            ]
        );
    }
}
