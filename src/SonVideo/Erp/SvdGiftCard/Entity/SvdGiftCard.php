<?php

namespace SonVideo\Erp\SvdGiftCard\Entity;

use App\Entity\AbstractEntity;

class SvdGiftCard extends AbstractEntity
{
    public int $id;

    public string $type;

    public string $card_number;

    public string $reference;

    public float $amount;

    public \DateTimeInterface $created_at;

    public int $creation_order_id;

    public ?int $creation_order_unique_id = null;

    public \DateTimeInterface $expire_at;

    public ?\DateTimeInterface $printed_at = null;

    public ?\DateTimeInterface $used_at = null;

    public ?int $use_order_id = null;
}
