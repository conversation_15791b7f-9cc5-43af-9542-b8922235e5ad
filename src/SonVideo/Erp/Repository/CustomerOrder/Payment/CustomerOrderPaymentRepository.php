<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\CustomerOrder\Payment;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

/**
 * Class CustomerOrderPaymentRepository.
 */
class CustomerOrderPaymentRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /**
     * findAllPaginated.
     *
     * @deprecated use src/SonVideo/Erp/CustomerOrderPayment/Mysql/Repository/CustomerOrderPaymentReadRepository.php instead
     */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        SELECT SQL_CALC_FOUND_ROWS
          pc.id,
          pc.id_commande,
          pc.id_unique,
          pc.id_paiement,
          pc.type,
          pc.creation_date,
          pc.creation_usr,
          pc.creation_montant,
          pc.creation_justificatif,
          pc.creation_origine,
          pc.acceptation_date,
          pc.acceptation_usr,
          pc.acceptation_montant,
          pc.acceptation_justificatif,
          pc.annulation_date,
          pc.annulation_usr,
          pc.annulation_montant,
          pc.demande_remise_date,
          pc.demande_remise_usr,
          pc.remise_date,
          pc.remise_usr,
          pc.remise_montant,
          pc.remise_justificatif,
          pc.remise_bon,
          pc.remise_taux,
          pc.impaye_date,
          pc.impaye_usr,
          pc.impaye_montant,
          pc.auto_statut,
          pc.auto_statut_detail,
          pc.auto_garantie,
          pc.auto_garantie_detail,
          pc.pays_ip,
          pc.pays_origine,
          p.description
          FROM backOffice.paiement_commande pc
            INNER JOIN backOffice.paiement  p ON pc.id_paiement = p.id_paiement
        WHERE {conditions}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
