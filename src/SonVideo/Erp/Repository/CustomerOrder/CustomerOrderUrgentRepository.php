<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\CustomerOrder;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

/**
 * Class CustomerOrderUrgentRepository.
 */
class CustomerOrderUrgentRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    /** getSql */
    private function getSql(): string
    {
        return <<<SQL
        SELECT SQL_CALC_FOUND_ROWS
          id_commande                                     AS order_id,
          date_creation                                   AS creation_date,
          trim(concat(cnt_lvr_prenom, ' ', cnt_lvr_nom))  AS customer,
          t.transporteur                                  AS carrier,
          t.id_transporteur                               AS carrier_id,
          btpl.libelle_produit                            AS shipment_method,
          btpl.id                                         AS shipment_method_id,
          backOffice.CMD_est_V_livrable(id_commande, 0)              AS is_deliverable,
          order_type                                      AS order_type
          FROM
            (
              SELECT
                c.id_commande,
                c.date_creation,
                c.cnt_lvr_prenom,
                c.cnt_lvr_nom,
                c.id_transporteur,
                c.id_pdt_transporteur,
                'Amazon' AS order_type
                FROM backOffice.commande c
                WHERE c.creation_origine IN ('sellercentral-europe', 'amazon.fr', 'amazon.es', 'amazon.de', 'amazon.it')
                  AND c.flux = 'traitement'
                  AND backOffice.CMD_est_en_attente(c.id_commande) = 1
              UNION
              SELECT
                c.id_commande,
                c.date_creation,
                c.cnt_lvr_prenom,
                c.cnt_lvr_nom,
                c.id_transporteur,
                c.id_pdt_transporteur,
                'Chronopost'
                FROM
                  backOffice.commande                          c
                    LEFT JOIN backOffice.bon_livraison         bl ON c.id_commande = bl.id_commande
                    LEFT JOIN backOffice.BO_CMD_chrono_precise bccp ON c.id_commande = bccp.id_commande
                WHERE bl.id_commande IS NULL
                  AND c.flux = 'traitement'
                  AND c.en_attente_de_livraison = 1
                  AND c.id_transporteur = 7
                  AND c.id_pdt_transporteur != 69
                  AND (c.id_pdt_transporteur != 68 OR backOffice.is_next_working_day(now()) >= date(bccp.time_slot_start_date))
              UNION
              SELECT
                c.id_commande,
                c.date_creation,
                c.cnt_lvr_prenom,
                c.cnt_lvr_nom,
                c.id_transporteur,
                c.id_pdt_transporteur,
                'DHL'
                FROM
                  backOffice.commande                  c
                    LEFT JOIN backOffice.bon_livraison bl ON c.id_commande = bl.id_commande
                WHERE bl.id_commande IS NULL
                  AND c.flux = 'traitement'
                  AND c.id_transporteur = 20
                  AND c.tpt_option_code = 'O3'
              )                                     urgent_order
              LEFT JOIN backOffice.transporteur     t ON t.id_transporteur = urgent_order.id_transporteur
              LEFT JOIN backOffice.BO_TPT_PDT_liste btpl ON btpl.id = urgent_order.id_pdt_transporteur
          WHERE {where}
          {order_by}
        SQL;
    }

    /** findAll. */
    public function findAll(): array
    {
        $sql = $this->getSql();

        $sql = strtr($sql, [
            '{where}' => 'true',
            '{order_by}' => 'ORDER BY creation_date ASC',
        ]);

        return $this->legacy_pdo->fetchAll($sql);
    }

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = $this->getSql();

        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
