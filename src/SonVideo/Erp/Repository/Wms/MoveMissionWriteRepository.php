<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Wms;

use App\Sql\AbstractLegacyRepository;

/**
 * Class MoveMissionWriteRepository.
 */
class MoveMissionWriteRepository extends AbstractLegacyRepository
{
    /** @return $this */
    public function assign(int $move_mission_id, int $assignee_id): self
    {
        $sql = <<<SQL
        UPDATE backOffice.WMS_move_mission
        SET assigned_to = :assignee_id,
            assigned_at = NOW()
          WHERE move_mission_id = :move_mission_id
        ;
        SQL;

        $result = $this->legacy_pdo->fetchAffected($sql, [
            'move_mission_id' => $move_mission_id,
            'assignee_id' => $assignee_id,
        ]);

        if (0 === $result) {
            throw new \UnexpectedValueException(sprintf('Could not assign user "%d" to move mission "%d".', $assignee_id, $move_mission_id));
        }

        return $this;
    }
}
