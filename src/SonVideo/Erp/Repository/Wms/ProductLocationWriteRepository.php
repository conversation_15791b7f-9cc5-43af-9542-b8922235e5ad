<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Wms;

use App\Exception\InternalErrorException;
use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\LegacyPdo;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Entity\ProductCorrectionEntity;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Referential\Location;
use SonVideo\Erp\Referential\StockMoveType;
use SonVideo\Erp\Referential\Warehouse;
use SonVideo\Erp\Repository\LocationRepository;

/**
 * Class ProductLocationWriteRepository.
 */
class ProductLocationWriteRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private ProductLocationReadRepository $product_location_read_repository;

    /** @var array */
    protected $computed_quantities = [];

    /** @var int */
    protected $computed_diff_quantity = 0;

    /** ProductLocationWriteRepository constructor. */
    public function __construct(
        LegacyPdo $legacy_pdo,
        LocationRepository $location_repository,
        ProductLocationReadRepository $product_location_read_repository
    ) {
        parent::__construct($legacy_pdo);
        $this->product_location_read_repository = $product_location_read_repository;
    }

    /**
     * Make correction on quantity of products in the location.
     *
     * @return array New quantities after correction
     *
     * @throws \Exception
     */
    public function doQuantityCorrection(ProductCorrectionEntity $product_correction_entity): array
    {
        try {
            $this->legacy_pdo->beginTransaction();

            // Prepare params used for correction
            $correction_params = $product_correction_entity->toArray();

            // Compute quantities (locked and unused) and diff quantity
            $this->computeQuantities($product_correction_entity->product_id, $product_correction_entity->location_id);
            $this->computeDiffQuantity($product_correction_entity->quantity);

            // Computed diff quantity is used for the writing operations + additional keys needed for the stock moves
            $correction_params['quantity'] = $this->computed_diff_quantity;
            $correction_params['username'] = $product_correction_entity->user->get('utilisateur');
            $correction_params['type'] = StockMoveType::CORRECTION;

            // Verify if there are no discrepancies before any writing operations
            $this->canDoCorrectionInStockB($correction_params, $product_correction_entity->quantity)
                ->diffQuantityIsEnoughToCoverLockedQuantity()
                ->diffQuantityCoverAllDeliveryNeeds($correction_params)
                ->canDoCorrectionIfProductIsAutoPicked($correction_params, $product_correction_entity->location_id);

            // Special case of a correction on a supplier order
            if (null !== $product_correction_entity->supplier_order_id) {
                $correction_params['user_id'] = $product_correction_entity->user->get('id_utilisateur');

                $this->writeSupplierOrder($correction_params);

                // Hard coded comment for correction on supplier order
                $correction_params['comment'] = sprintf(
                    'Correction stock sur commande fournisseur "%s"',
                    $product_correction_entity->supplier_order_id
                );
            }

            $this->writeStock($correction_params);

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $this->product_location_read_repository->getQuantitiesOfProductInLocation(
            $product_correction_entity->product_id,
            $product_correction_entity->location_id
        );
    }

    /** @return $this */
    protected function computeQuantities(int $product_id, int $location_id): self
    {
        // retrieve quantities in the location
        $quantities = $this->product_location_read_repository->getQuantitiesOfProductInLocation(
            $product_id,
            $location_id
        );

        // compute sum of quantities and diff
        $this->computed_quantities = array_reduce(
            $quantities,
            function (array $sum, array $quantity) {
                if (null !== $quantity['delivery_ticket_id'] || $quantity['move_mission_id']) {
                    $sum['locked'] += $quantity['quantity'];
                } else {
                    $sum['unused'] += $quantity['quantity'];
                }

                return $sum;
            },
            ['unused' => 0, 'locked' => 0]
        );

        return $this;
    }

    /** @return $this */
    protected function computeDiffQuantity(int $quantity): self
    {
        $this->computed_diff_quantity = $quantity - array_sum($this->computed_quantities);

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     * @throws InternalErrorException
     */
    protected function canDoCorrectionInStockB(array $correction_params, int $original_quantity): self
    {
        // prevent correction greater than 1 if article in stock B
        $sql = <<<SQL
        SELECT stock_emplacement = 'b' FROM backOffice.article WHERE id_produit = :product_id;
        SQL;
        $is_in_stock_b = (bool) $this->legacy_pdo->fetchValue($sql, $correction_params);

        if ($is_in_stock_b && $original_quantity > 1) {
            throw new InternalErrorException(InternalError::PRODUCT_LOCATION_TOO_MUCH_QTY_IN_STOCK_B, new \LogicException('Too much quantity for product in stock B'));
        }

        // prevent having more than one product for a "stock B product" in the entire database
        if ($is_in_stock_b && 1 === $original_quantity) {
            $sql = <<<SQL
            SELECT SUM(quantite_stock) FROM backOffice.BO_STK_produit_depot WHERE id_produit = :product_id GROUP BY id_produit;
            SQL;
            $current_quantity = (int) $this->legacy_pdo->fetchValue($sql, $correction_params);

            if ($current_quantity > 0) {
                throw new InternalErrorException(InternalError::PRODUCT_LOCATION_STOCK_B_ALREADY_EXISTS, new \LogicException('Stock B already exists for this product'));
            }
        }

        return $this;
    }

    /**
     * @return $this
     *
     * @throws InternalErrorException
     */
    protected function diffQuantityIsEnoughToCoverLockedQuantity(): self
    {
        // if decrementing, prevent correction if there is not enough unused quantity
        if (
            $this->computed_diff_quantity < 0 &&
            $this->computed_quantities['unused'] + $this->computed_diff_quantity < 0
        ) {
            $this->logger->warning(
                'Quantity < Number of products linked to delivery notes or move missions in current location.'
            );

            throw new InternalErrorException(InternalError::PRODUCT_LOCATION_CORRECTED_QTY_DOES_COVER_LOCKED_QTY, new \LogicException(sprintf('Corrected quantity does not cover locked quantity, should be higher than "%d".', $this->computed_quantities['locked'])), ['computed_diff_quantity' => $this->computed_diff_quantity, 'computed_unused_quantities' => $this->computed_quantities['unused'], 'computed_locked_quantities' => $this->computed_quantities['locked']]);
        }

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     * @throws InternalErrorException
     */
    protected function diffQuantityCoverAllDeliveryNeeds(array $correction_params): self
    {
        // if decrementing, prevent correction if new total value can't cover all delivery needs
        $sql = <<<SQL
        SELECT backOffice.PDT_ART_qte_au_depart_depot(:product_id, backOffice.WMS_get_location_warehouse_id(:location_id));
        SQL;
        $delivery_needs = (int) $this->legacy_pdo->fetchValue($sql, $correction_params);

        $sql = <<<SQL
        SELECT backOffice.PDT_ART_qte_stock_depot(:product_id, backOffice.WMS_get_location_warehouse_id(:location_id));
        SQL;
        $qty_in_warehouse = (int) $this->legacy_pdo->fetchValue($sql, $correction_params);

        if ($delivery_needs > $qty_in_warehouse + $this->computed_diff_quantity) {
            $sql = <<<SQL
            SELECT GROUP_CONCAT(bl.id_bon_livraison)
            FROM backOffice.produit_bon_livraison pbl
              JOIN bon_livraison bl ON pbl.id_bon_livraison = bl.id_bon_livraison
            WHERE
              bl.id_depot = backOffice.WMS_get_location_warehouse_id(:location_id)
              AND bl.status = 'au depart'
              AND bl.id_transfert IS NULL
              AND pbl.id_produit = :product_id
            GROUP BY pbl.id_produit;
            SQL;
            $delivery_notes = $this->legacy_pdo->fetchValue($sql, $correction_params);

            throw new InternalErrorException(InternalError::PRODUCT_LOCATION_CORRECTED_QTY_DOES_COVER_STOCK_NEEDS, new \LogicException(sprintf('Corrected quantity does not cover stock needs for the following delivery notes: "%s"', $delivery_notes)), ['delivery_needs' => $delivery_needs, 'qty_in_warehouse' => $qty_in_warehouse, 'delivery_notes' => $delivery_notes, 'computed_diff_quantity' => $this->computed_diff_quantity]);
        }

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    protected function writeSupplierOrder(array $correction_params): self
    {
        $sql = <<<SQL
        CALL backOffice.SUPPLIER_ORDER_CORRECT_DELIVERED_QUANTITY(:supplier_order_id, :product_id, :quantity, :user_id)
        SQL;

        $this->legacy_pdo->fetchValue($sql, $correction_params);

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    protected function writeStock(array $correction_params): self
    {
        // increase unused quantity by diff
        $sql = <<<SQL
        CALL backOffice.WMS_upsert_product_location(:location_id, :product_id, NULL, NULL, :quantity, @result)
        SQL;
        $this->legacy_pdo->fetchValueFromOutputParameter($sql, $correction_params);

        // add correction log
        $sql = <<<SQL
        INSERT INTO backOffice.mouvement_stock(id_produit, date_creation, utilisateur, type, commentaire, quantite, prix_achat, id_emplacement, id_commande_fournisseur)
        VALUE (:product_id, now(), :username, :type, :comment, :quantity, backOffice.PDT_ART_px_achat(:product_id), :location_id, :supplier_order_id)
        SQL;
        $this->legacy_pdo->fetchAffected($sql, $correction_params);

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    protected function canDoCorrectionIfProductIsAutoPicked(array $correction_params, int $location_id): self
    {
        $sql = <<<SQL
        SELECT is_auto_picked = 1 FROM backOffice.article WHERE id_produit = :product_id;
        SQL;
        $is_product_auto_picked = (bool) $this->legacy_pdo->fetchValue($sql, $correction_params);

        $sql = <<<SQL
        SELECT WMS_get_location_warehouse_id(:location_id);
        SQL;
        $warehouse_id = (int) $this->legacy_pdo->fetchValue($sql, $correction_params);

        if (
            $is_product_auto_picked &&
            Location::VIRTUAL_STOCK_LOCATION !== $location_id &&
            Warehouse::CHAMPIGNY_WAREHOUSE === $warehouse_id
        ) {
            throw new InternalErrorException(InternalError::PRODUCT_LOCATION_INAPROPRIATE_FOR_AUTO_PICKED_PRODUCT, new \LogicException(sprintf('"%s" is not an appropriate location for an auto picked product', $location_id)));
        }

        return $this;
    }
}
