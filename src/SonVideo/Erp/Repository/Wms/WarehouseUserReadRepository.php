<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Wms;

use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Entity\WarehouseUserEntity;

/**
 * Class WarehouseUserReadRepository.
 */
class WarehouseUserReadRepository extends AbstractLegacyRepository
{
    public function findActiveByWarehouseId(int $warehouse_id): array
    {
        $sql = <<<SQL
        SELECT
          sgu.id                                          AS user_id,
          sgu.username                                    AS username,
          sgup.email                                      AS email,
          backOffice.GET_COMPUTED_USER_NAME_BY_ID(sgu.id) AS full_name,
          backOffice.USR_codebarre(sgu.id, sgu.password)  AS barcode,
          sgup.warehouse_id                               AS warehouse_id
          FROM
            backOffice.sf_guard_user                      sgu
              INNER JOIN backOffice.sf_guard_user_profile sgup ON sgup.id = sgu.id
          WHERE sgup.warehouse_id = :warehouse_id
            AND sgu.is_active
        SQL;

        $data = $this->legacy_pdo->fetchAll($sql, ['warehouse_id' => $warehouse_id]);

        $users = [];
        foreach ($data as $datum) {
            $users[] = $this->data_loader->hydrate($datum, WarehouseUserEntity::class);
        }

        return $users;
    }
}
