<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Wms;

use App\Sql\AbstractLegacyRepository;

/**
 * Class WarehouseUserWriteRepository.
 */
class WarehouseUserWriteRepository extends AbstractLegacyRepository
{
    /** @return $this */
    public function assign(int $warehouse_id, int $user_id): self
    {
        $sql = <<<SQL
        UPDATE backOffice.sf_guard_user_profile
        SET warehouse_id = :warehouse_id
        WHERE id = :user_id;
        SQL;

        $result = $this->legacy_pdo->fetchAffected($sql, ['warehouse_id' => $warehouse_id, 'user_id' => $user_id]);

        if (0 === $result) {
            throw new \UnexpectedValueException(sprintf('Could not assign user "%d" to warehouse "%d".', $user_id, $warehouse_id));
        }

        return $this;
    }

    /** @return $this */
    public function unassign(int $warehouse_id, int $user_id): self
    {
        $sql = <<<SQL
        UPDATE backOffice.sf_guard_user_profile
        SET warehouse_id = NULL
        WHERE id = :user_id
          AND warehouse_id = :warehouse_id;
        SQL;

        $result = $this->legacy_pdo->fetchAffected($sql, ['user_id' => $user_id, 'warehouse_id' => $warehouse_id]);

        if (0 === $result) {
            throw new \UnexpectedValueException(sprintf('Could not un-assign user "%d" from warehouse "%d".', $user_id, $warehouse_id));
        }

        return $this;
    }
}
