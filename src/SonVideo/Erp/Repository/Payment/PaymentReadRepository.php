<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Payment;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

/**
 * Class PaymentReadRepository.
 */
class PaymentReadRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'payment_id' => 'p.id_paiement',
        'moyen' => 'p.payment_methods',
        'paiement' => 'p.name',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                p.id_paiement AS payment_id,
                p.moyen       AS payment_methods,
                p.paiement    AS name
                FROM
                  backOffice.paiement p
              WHERE {conditions}
            ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
