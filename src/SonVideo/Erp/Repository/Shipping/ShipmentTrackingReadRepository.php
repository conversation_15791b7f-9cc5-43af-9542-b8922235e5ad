<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Shipping;

use App\Sql\AbstractLegacyRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Entity\Shipping\ShipmentTrackingEntity;
use SonVideo\Erp\Entity\Shipping\ShipmentTrackingParcelEntity;

/**
 * Class ShipmentTrackingReadRepository.
 */
class ShipmentTrackingReadRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /**
     * findById.
     *
     * @throws \Exception
     */
    public function findById(int $delivery_ticket_id): ShipmentTrackingEntity
    {
        $sql = <<<SQL
        SELECT
          bl.id_bon_livraison     AS delivery_ticket_id,
          cmd.id_commande         AS order_id,
          cmd.no_commande_origine AS external_order_number,
          t.transporteur          AS carrier_name,
          btpl.libelle_produit    AS shipping_method_name,
          COUNT(beebc.id) = 0     AS is_manual_shipment
          FROM
            backOffice.bon_livraison                      bl
              INNER JOIN backOffice.transporteur          t ON bl.id_transporteur = t.id_transporteur
              INNER JOIN backOffice.BO_TPT_PDT_liste      btpl ON bl.id_pdt_transporteur = btpl.id
              INNER JOIN backOffice.commande              cmd ON bl.id_commande = cmd.id_commande
              INNER JOIN backOffice.colis                       parcel ON bl.id_bon_livraison = parcel.id_bon_livraison
              LEFT JOIN  backOffice.BO_EXP_expeditions_bl_colis beebc ON parcel.no_colis = BINARY beebc.no_colis
          WHERE bl.id_bon_livraison = :delivery_ticket_id
        SQL;
        $tracking_information = $this->legacy_pdo->fetchOne($sql, ['delivery_ticket_id' => $delivery_ticket_id]);
        if (!$tracking_information) {
            throw new \Exception(sprintf('Could not found tracking information with delivery ticket id "%s".', $delivery_ticket_id));
        }

        $shipment_tracking_entity = $this->data_loader->hydrate($tracking_information, ShipmentTrackingEntity::class);
        $shipment_tracking_entity->parcels = $this->addParcels(
            $delivery_ticket_id,
            $shipment_tracking_entity->is_manual_shipment
        );

        return $shipment_tracking_entity;
    }

    /** addParcels */
    protected function addParcels(int $delivery_ticket_id, bool $is_manual): array
    {
        // Populate parcels differently if the the sticker has been generated manually (not scanned + manual tracking number)
        $sql = $is_manual ? $this->getQueryForManualParcels() : $this->getQueryForParcels();

        // We use fetchAll, as fetchObjects forces all object rows to be casted as strings and bypass Symfony serializer.
        $pool = $this->legacy_pdo->fetchAll($sql, ['delivery_ticket_id' => $delivery_ticket_id]);
        $parcels = [];
        foreach ($pool as $line) {
            $parcels[] = $this->data_loader->hydrate($line, ShipmentTrackingParcelEntity::class);
        }

        return $parcels;
    }

    /** getQueryForParcels */
    protected function getQueryForParcels(): string
    {
        return <<<SQL
        SELECT
          parcel.no_colis    AS tracking_number,
          COUNT(p.reference) AS quantity,
          p.reference        AS sku
          FROM
            backOffice.colis                                            parcel
              LEFT JOIN  backOffice.BO_EXP_expeditions_bl_colis         beebc ON parcel.no_colis = BINARY beebc.no_colis
              LEFT JOIN  backOffice.BO_EXP_expeditions_bl_colis_produit beebcp ON beebc.id = beebcp.colis_id
              INNER JOIN backOffice.produit                             p ON beebcp.produit_id = p.id_produit AND p.type IN ('article')
          WHERE parcel.id_bon_livraison = :delivery_ticket_id
          GROUP BY parcel.no_colis, p.reference
          ORDER BY tracking_number ASC
        SQL;
    }

    /** getQueryForManualParcels */
    protected function getQueryForManualParcels(): string
    {
        return <<<SQL
        SELECT
          parcel.no_colis AS tracking_number,
          pbl.quantite    AS quantity,
          p.reference     AS sku
          FROM
            backOffice.colis                              parcel
              INNER JOIN backOffice.bon_livraison         bl ON parcel.id_bon_livraison = bl.id_bon_livraison
              INNER JOIN backOffice.produit_bon_livraison pbl ON bl.id_bon_livraison = pbl.id_bon_livraison
              INNER JOIN backOffice.produit               p ON pbl.id_produit = p.id_produit AND p.type IN ('article')
          WHERE parcel.id_bon_livraison = :delivery_ticket_id
          GROUP BY pbl.id
          ORDER BY tracking_number ASC
        SQL;
    }

    /** getUndeliveredFranceExpressParcels */
    public function getUndeliveredFranceExpressParcels(): array
    {
        $sql = <<<SQL
        SELECT
          c.id_colis AS parcel_id,
          c.no_colis AS parcel_number,
          'son-video.com' AS sent_from,
          (
            SELECT
              exists(
                SELECT ds.id_statut
                  FROM
                    colis_statut ds
                  WHERE ds.statut IN ('livre', 'livre_retard') AND ds.id_colis = c.id_colis)
            ) AS is_delivered
          FROM
            bon_livraison                                         bl
              INNER JOIN BO_EXP_franceexpress_dispatch_note_items dni ON bl.id_bon_livraison = dni.correlation_id
              INNER JOIN BO_EXP_franceexpress_dispatch_note       dn ON dni.dispatch_note_id = dn.id
              INNER JOIN colis                                    c ON bl.id_bon_livraison = c.id_bon_livraison
              LEFT JOIN  colis_franceexpress_tracking             cft ON c.id_colis = cft.id_colis
          WHERE dni.type = 'bon_livraison' AND
            cft.id_colis IS NULL AND
            length(c.no_colis) = 8 AND
            (bl.date_validation BETWEEN NOW() - INTERVAL 31 DAY AND NOW() OR bl.date_validation IS NULL)
          GROUP BY c.no_colis
          HAVING is_delivered = 0
        UNION
        SELECT
          c.id_colis AS parcel_id,
          c.no_colis AS parcel_number,
          'havre' AS sent_from,
          (
            SELECT
              exists(
                SELECT ds.id_statut
                  FROM
                    colis_statut ds
                  WHERE ds.statut IN ('livre', 'livre_retard') AND ds.id_colis = c.id_colis)
            ) AS is_delivered
          FROM
            bon_livraison                             bl
              INNER JOIN colis                        c ON bl.id_bon_livraison = c.id_bon_livraison
              LEFT JOIN  colis_franceexpress_tracking cft ON c.id_colis = cft.id_colis
          WHERE bl.id_transporteur = 19 AND bl.id_pdt_transporteur = 29 AND length(c.no_colis) = 8 AND
            cft.id_colis IS NULL AND bl.id_commande IS NOT NULL AND
            (bl.date_validation BETWEEN NOW() - INTERVAL 31 DAY AND NOW() OR bl.date_validation IS NULL)
          GROUP BY c.no_colis
          HAVING is_delivered = 0
        ;
        SQL;

        return $this->legacy_pdo->fetchObjects($sql);
    }
}
