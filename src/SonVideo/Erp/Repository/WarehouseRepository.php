<?php

namespace SonVideo\Erp\Repository;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Helper\Where;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Entity\WarehouseEntity;
use SonVideo\Erp\Entity\WarehouseV2Entity;
use SonVideo\Erp\Repository\Wms\WarehouseUserReadRepository;

class WarehouseRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'warehouse_id' => 'bsd.id',
        'name' => 'bsd.nom_depot',
        'address' => 'bsd.adresse',
        'postal_code' => 'bsd.code_postal',
        'city' => 'bsd.ville',
        'country_id' => 'bsd.id_pays',
        'country_name' => 'p.pays',
        'country_code' => 'p.code_2_lettres',
        'phone_number' => 'bsd.telephone',
        'email_address' => 'bsd.email',
        'manager_id' => 'bsd.id_user',
        'description' => 'bsd.description',
        'transfer_name' => 'bsd.nom_transfert',
        'basket_name' => 'bsd.nom_panier',
        'basket_address' => 'bsd.adresse_panier',
        'basket_city' => 'bsd.ville_panier',
        'google_map_link' => 'bsd.localisation',
        'sort_order' => 'bsd.ordre',
        'opened' => 'bsd.horaire',
        'opened_days' => 'bsd.horaires_jours',
        'opened_hours' => 'bsd.horaires_heures',
        'is_active' => 'bsd.is_active',
        'is_active_in_bo' => 'bsd.is_active_bo',
        'shorthand_name' => 'bsd.abreviation',
        'use_auto_picking' => 'bsd.use_auto_picking',
        'wms_code' => 'bsd.code',
        'allowed_inventory_types' => 'bsd.allowed_inventory_types',
        'shipment_method_id' => 'btpl.id',
    ];

    private WarehouseUserReadRepository $user_read_repository;

    public function __construct(LegacyPdo $legacy_pdo, WarehouseUserReadRepository $user_read_repository)
    {
        parent::__construct($legacy_pdo);
        $this->user_read_repository = $user_read_repository;
    }

    public function getAllPaginated(int $page = 1, int $limit = 50, string $order_by = '', array $filter = []): Pager
    {
        $sql = <<<SQL
        SELECT SQL_CALC_FOUND_ROWS
          id                                                        AS warehouse_id,
          nom_depot                                                 AS name,
          ordre                                                     AS ordering,
          TRIM(REPLACE(UPPER(CONCAT(nom_depot, '-', id)), ' ', '')) AS barcode,
          use_auto_picking                                          AS use_auto_picking
        FROM backOffice.BO_STK_depot
        {where}
        {order_by}
        SQL;
        $where = $this->createWhere($filter);
        $sql = strtr($sql, [
            '{where}' => $where,
            '{order_by}' => $this->getOrderByClause($order_by),
        ]);

        return $this->legacy_pdo->paginateObjects($page, $limit, $sql, $where->getParams());
    }

    /** Return a Where object out of provided filter */
    protected function createWhere(array $filter): Where
    {
        $where = new Where();
        if (isset($filter['is_active_bo'])) {
            $where->addCondition('is_active_bo', 'is_active_bo', $filter['is_active_bo']);
        }

        return $where;
    }

    /** @throws NotFoundException */
    public function findOneById(int $warehouse_id): WarehouseEntity
    {
        $sql = <<<SQL
        {base_sql}
        WHERE id = :warehouse_id;
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        $warehouse = $this->legacy_pdo->fetchOne($sql, ['warehouse_id' => $warehouse_id]);

        if (false === $warehouse) {
            throw new NotFoundException('Warehouse not found.');
        }

        return $this->data_loader->hydrate($warehouse, WarehouseEntity::class);
    }

    /** @throws NotFoundException */
    public function findOneByCarrierId(int $carrier_id): WarehouseEntity
    {
        $sql = <<<SQL
        {base_sql}
        WHERE id_transporteur_emport = :carrier_id;
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        $warehouse = $this->legacy_pdo->fetchOne($sql, ['carrier_id' => $carrier_id]);

        if (false === $warehouse) {
            throw new NotFoundException('Warehouse not found.');
        }

        return $this->data_loader->hydrate($warehouse, WarehouseEntity::class);
    }

    /** @throws NotFoundException */
    public function findAllParameters(int $warehouse_id): array
    {
        $sql = <<<SQL
        SELECT wp.warehouse_id, wp.parameter, wp.parameter_value
        FROM backOffice.BO_STK_depot d
        LEFT JOIN backOffice.WMS_warehouse_parameter wp ON d.id = wp.warehouse_id
        WHERE d.id = :warehouse_id;
        SQL;

        $parameters = $this->legacy_pdo->fetchAll($sql, ['warehouse_id' => $warehouse_id]);

        if (0 == count($parameters)) {
            throw new NotFoundException('Warehouse not found');
        }

        // no data for this existing warehouse => empty array
        if (null === $parameters[0]['warehouse_id']) {
            return [];
        }

        // cast warehouse_id value in int
        foreach ($parameters as $key => $parameter) {
            $parameters[$key]['warehouse_id'] = (int) $parameter['warehouse_id'];
        }

        return $parameters;
    }

    public function findAllLocations(QueryBuilder $query_builder): array
    {
        $sql = <<<SQL
        SELECT wl.location_id, wl.code, wl.is_active
        FROM backOffice.WMS_location wl
          INNER JOIN backOffice.WMS_area wa ON wl.area_id = wa.area_id
        WHERE {where}
        SQL;
        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
        ]);

        return $this->legacy_pdo->fetchObjects($sql, $query_builder->getWhereParameters());
    }

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                bsd.id                                                            AS warehouse_id,
                bsd.nom_depot                                                     AS name,
                bsd.adresse                                                       AS address,
                bsd.code_postal                                                   AS postal_code,
                bsd.ville                                                         AS city,
                bsd.id_pays                                                       AS country_id,
                p.pays                                                            AS country_name,
                p.code_2_lettres                                                  AS country_code,
                bsd.telephone                                                     AS phone_number,
                bsd.email                                                         AS email_address,
                bsd.id_user                                                       AS manager_id,
                bsd.description                                                   AS description,
                bsd.nom_transfert                                                 AS transfer_name,
                bsd.nom_panier                                                    AS basket_name,
                bsd.adresse_panier                                                AS basket_address,
                bsd.ville_panier                                                  AS basket_city,
                bsd.localisation                                                  AS google_map_link,
                bsd.ordre                                                         AS sort_order,
                bsd.horaire                                                       AS opened,
                bsd.horaires_jours                                                AS opened_days,
                bsd.horaires_heures                                               AS opened_hours,
                bsd.is_active                                                     AS is_active,
                bsd.is_active_bo                                                  AS is_active_in_bo,
                bsd.abreviation                                                   AS shorthand_name,
                bsd.use_auto_picking                                              AS use_auto_picking,
                bsd.code                                                          AS wms_code,
                bsd.allowed_inventory_types                                       AS allowed_inventory_types,
                bsd.business_hours                                                AS business_hours,
                bsd.delivery_days                                                 AS delivery_days,
                REPLACE(UPPER(CONCAT(bsd.nom_depot, '-', bsd.id)), ' ', '')       AS warehouse_barcode,
                IF(bsd.use_auto_picking, wwp.parameter_value, NULL)               AS location_id,
                IF(bsd.use_auto_picking, wl.code, NULL)                           AS location_code,
                IF(bsd.use_auto_picking, wl.label, NULL)                          AS location_label,
                btpl.id                                                           AS shipment_method_id
                FROM
                  backOffice.BO_STK_depot                        bsd
                    INNER JOIN backOffice.pays                   p ON p.id_pays = bsd.id_pays
                    LEFT JOIN backOffice.WMS_warehouse_parameter wwp
                                ON bsd.id = wwp.warehouse_id AND wwp.parameter = 'default_location'
                    LEFT JOIN backOffice.WMS_location            wl ON wwp.parameter_value = wl.location_id
                    LEFT JOIN backOffice.BO_TPT_PDT_liste        btpl ON btpl.transporteur_id = bsd.id_transporteur_emport
              WHERE {conditions}
              ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        $pager = $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );

        $warehouses = [];

        foreach ($pager->getResults() as $datum) {
            // add user list
            $datum->users = $this->user_read_repository->findActiveByWarehouseId((int) $datum->warehouse_id);

            // and format data types properly
            $warehouses[] = $this->data_loader->hydrate((array) $datum, WarehouseV2Entity::class)->toArray();
        }

        $pager->setResults($warehouses);

        return $pager;
    }

    private function getBaseSql(): string
    {
        return <<<SQL
        SELECT
          id,
          nom_depot,
          code,
          is_active
          FROM backOffice.BO_STK_depot
        SQL;
    }

    /** @throws SqlErrorMessageException */
    public function warehouseExists(int $warehouse_id): bool
    {
        $sql = <<<'SQL'
            SELECT
              EXISTS(
                SELECT 1 FROM backOffice.BO_STK_depot bsp WHERE bsp.id = :warehouse_id
              ) AS entry;
        SQL;

        return (bool) $this->legacy_pdo->fetchValue($sql, ['warehouse_id' => $warehouse_id]);
    }

    /** @throws \JsonException */
    public function updateWarehouseBusinessHours(int $warehouse_id, array $business_hours): void
    {
        $sql = <<<'SQL'
            UPDATE backOffice.BO_STK_depot
            SET business_hours = :business_hours
            WHERE id = :warehouse_id
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'warehouse_id' => $warehouse_id,
            'business_hours' => json_encode($business_hours, JSON_THROW_ON_ERROR),
        ]);
    }

    /** @throws \JsonException */
    public function updateWarehouseDeliveryDays(int $warehouse_id, array $delivery_days): void
    {
        $sql = <<<'SQL'
            UPDATE backOffice.BO_STK_depot
            SET delivery_days = :delivery_days
            WHERE id = :warehouse_id
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'warehouse_id' => $warehouse_id,
            'delivery_days' => json_encode($delivery_days, JSON_THROW_ON_ERROR),
        ]);
    }
}
