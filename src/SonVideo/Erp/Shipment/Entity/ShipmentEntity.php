<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Shipment\Entity;

use App\Entity\AbstractEntity;

class ShipmentEntity extends AbstractEntity
{
    public int $shipment_id;

    public int $carrier_id;

    public string $carrier_name;

    public ?string $shipment_account = null;

    public int $slip_number;

    public string $created_at;

    public ?string $closed_at = null;

    public int $status;

    public int $parcel_quantity;

    /** @var PartialShipmentDeliveryNoteEntity[] */
    public array $shipment_delivery_notes = [];
}
