<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Process\DeliveryNote;

use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\DeliveryNote\Entity\DeliveryNoteEntity;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Entity\LocationEntity;
use SonVideo\Erp\Referential\AreaType;
use SonVideo\Erp\Referential\DeliveryTicketLogStatus;
use SonVideo\Erp\Referential\DeliveryTicketStatus;
use SonVideo\Erp\Referential\DeliveryTicketTransferStatus;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Repository\LocationProductRepository;
use SonVideo\Erp\Repository\LocationRepository;
use SonVideo\Erp\Repository\MoveRepository;
use SonVideo\Erp\Repository\Shipping\DeliveryTicketActivityLogWriteRepository;
use SonVideo\Erp\Repository\Wms\ProductLocationReadRepository;
use SonVideo\Erp\User\Entity\UserEntity;

/**
 * Class ProductMover.
 */
class ProductMover implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private LegacyPdo $legacy_pdo;

    private MoveRepository $move_repository;

    private ProductLocationReadRepository $product_location_repository;

    private DeliveryNoteRepository $delivery_note_repository;

    private LocationRepository $location_repository;

    private LocationProductRepository $location_product_repository;

    private DeliveryTicketActivityLogWriteRepository $delivery_activity_log_repository;

    /** ProductMover constructor. */
    public function __construct(
        LegacyPdo $legacy_pdo,
        MoveRepository $move_repository,
        ProductLocationReadRepository $product_location_repository,
        DeliveryNoteRepository $delivery_note_repository,
        LocationRepository $location_repository,
        LocationProductRepository $location_product_repository,
        DeliveryTicketActivityLogWriteRepository $delivery_activity_log_repository
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->move_repository = $move_repository;
        $this->product_location_repository = $product_location_repository;
        $this->delivery_note_repository = $delivery_note_repository;
        $this->location_repository = $location_repository;
        $this->location_product_repository = $location_product_repository;
        $this->delivery_activity_log_repository = $delivery_activity_log_repository;
    }

    /**
     * Do the movements for multiple products on multiple delivery notes.
     * The logic will change based on origin and destination locations types.
     *
     * @return array|array[]
     *
     * @throws InternalErrorException
     * @throws NotFoundException
     */
    public function moveMultipleProducts(
        int $origin_location_id,
        int $destination_location_id,
        array $delivery_ticket_ids,
        array $products,
        UserEntity $user
    ): array {
        // retrieve origin and destination locations
        $origin_location = $this->location_repository->findOneById($origin_location_id);
        $destination_location = $this->location_repository->findOneById($destination_location_id);

        $result_summary = [];
        if (
            AreaType::STOCK === $origin_location->area->area_type_id &&
            AreaType::TRANSIT === $destination_location->area->area_type_id
        ) {
            $this->moveFromStockToTransit(
                $origin_location,
                $destination_location,
                $delivery_ticket_ids,
                $products,
                $user
            );
        } elseif (
            AreaType::TRANSIT === $origin_location->area->area_type_id &&
            in_array($destination_location->area->area_type_id, AreaType::PREPA_TYPES)
        ) {
            $result_summary = $this->moveFromTransitToPreparation(
                $origin_location,
                $destination_location,
                $delivery_ticket_ids,
                $user
            );
        } else {
            throw new InternalErrorException(InternalError::DELIVERY_NOTE_MOVEMENT_TYPE_NOT_HANDLED, new \LogicException('Type of movement not handled'), ['origin_location_type_id' => $origin_location->area->area_type_id, 'destination_location_type_id' => $destination_location->area->area_type_id]);
        }

        return $result_summary;
    }

    /**
     * Multiple picking: Assign products quantities to delivery notes and do the movements.
     *
     * @throws Exception
     */
    protected function moveFromStockToTransit(
        LocationEntity $origin_location,
        LocationEntity $destination_location,
        array $delivery_ticket_ids,
        array $products_quantities,
        UserEntity $user
    ) {
        $this->legacy_pdo->beginTransaction();
        try {
            $delivery_notes = $this->getDeliveryNotes($delivery_ticket_ids);

            $this->checkDeliveryNotes($delivery_notes);

            $movements = $this->computeStockToTransitMoves($products_quantities, $delivery_notes);

            $this->logger->notice('Do movements');
            foreach ($movements as $movement) {
                $this->move_repository->createInternalProductMove(
                    $origin_location->location_id,
                    $destination_location->location_id,
                    $movement['product_id'],
                    $movement['delivery_ticket_id'],
                    null,
                    $movement['quantity'],
                    $user->get('utilisateur')
                );
            }

            $this->legacy_pdo->commit();
        } catch (Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }
    }

    /**
     * End of multiple picking: Assign products quantities to delivery notes and do the movements.
     *
     * @return array{move: array<int, array{delivery_note_id: int, product_id: int, quantity: mixed}>, incomplete: array<int, array{delivery_note_id: int, product_id: int, need: float|int, picked: mixed}>}
     *
     * @throws Exception
     */
    protected function moveFromTransitToPreparation(
        LocationEntity $origin_location,
        LocationEntity $destination_location,
        array $delivery_notes_ids,
        UserEntity $user
    ): array {
        $this->legacy_pdo->beginTransaction();
        try {
            $computation = $this->computeTransitToPreparationMoves(
                $origin_location,
                $destination_location,
                $delivery_notes_ids
            );

            $this->logger->notice(
                'Do movements from transit to preparation which complete a delivery note',
                $computation['move']
            );
            foreach ($computation['move'] as $movement) {
                $this->move_repository->createInternalProductMove(
                    $origin_location->location_id,
                    $destination_location->location_id,
                    $movement['product_id'],
                    $movement['delivery_note_id'],
                    null,
                    $movement['quantity'],
                    $user->get('utilisateur')
                );
            }

            $this->logger->notice('Mark as abandoned the incomplet pickings');
            foreach ($computation['incomplete'] as $incomplete) {
                $this->delivery_activity_log_repository->write(
                    $incomplete['delivery_note_id'],
                    DeliveryTicketLogStatus::PICKING_ABORTED,
                    $user->id_utilisateur
                );

                $this->delivery_note_repository->autoArrangeVirtualProducts($incomplete['delivery_note_id']);
            }

            $this->legacy_pdo->commit();
        } catch (Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }

        return $computation;
    }

    /**
     * Return location products filtered by product ids.
     * Quantities are also filtered by delivery notes ids.
     */
    protected function filterLocationProducts(
        array $location_products,
        array $products_ids,
        array $delivery_notes_ids
    ): array {
        return array_map(
            // overwrite quantities to filter out the quantities not linked to the delivery notes
            function (array $filtered_product) use ($delivery_notes_ids) {
                // note: array_values resets the numerical indexes of the filtered array
                $filtered_product['quantities'] = array_values(
                    array_filter(
                        $filtered_product['quantities'],
                        fn ($quantity): bool => in_array($quantity['delivery_ticket_id'], $delivery_notes_ids)
                    )
                );

                return $filtered_product;
            },
            // filter out location products not requested
            array_values(
                array_filter(
                    $location_products,
                    fn ($location_product): bool => in_array($location_product['product_id'], $products_ids)
                )
            )
        );
    }

    /**
     * Get the delivery notes out of their ids.
     *
     * @return DeliveryNoteEntity[]
     */
    protected function getDeliveryNotes(array $delivery_note_ids): array
    {
        $this->logger->notice('Get delivery notes', ['delivery_note_ids' => $delivery_note_ids]);

        return array_map(
            fn ($delivery_note_id): DeliveryNoteEntity => $this->delivery_note_repository->findOneById(
                $delivery_note_id
            ),
            $delivery_note_ids
        );
    }

    /**
     * Check the delivery notes:
     * - are all in the same warehouse
     * - have a status which authorize movement.
     *
     * @param DeliveryNoteEntity[] $delivery_notes
     *
     * @throws InternalErrorException
     */
    protected function checkDeliveryNotes(array $delivery_notes)
    {
        $this->logger->notice('Check delivery notes');
        $warehouse_id = null;
        foreach ($delivery_notes as $delivery_note) {
            // must use the same warehouse
            if (null === $warehouse_id) {
                $warehouse_id = $delivery_note->warehouse_id;
            }
            if ($warehouse_id !== $delivery_note->warehouse_id) {
                throw new InternalErrorException(InternalError::DELIVERY_NOTE_NOT_IN_SAME_WAREHOUSE, new \LogicException('Some delivery note does not belong to the same warehouse'), ['delivery_note_id' => $delivery_note->delivery_note_id, 'warehouse_id' => $delivery_note->warehouse_id]);
            }

            // status must be correct
            if (
                DeliveryTicketStatus::LEAVING !== $delivery_note->status &&
                (DeliveryTicketStatus::TRANSFER !== $delivery_note->status ||
                    !in_array($delivery_note->transfer_status, DeliveryTicketTransferStatus::PICKABLE_STATUSES))
            ) {
                throw new InternalErrorException(InternalError::DELIVERY_NOTE_STATUS_INCORRECT, new \LogicException('Delivery note status is incorrect'), ['delivery_note' => $delivery_note]);
            }
        }
    }

    /**
     * Compute how the products quantities should be assigned to different delivery notes to complete them in the order.
     *
     * @param DeliveryNoteEntity[] $delivery_notes
     *
     * @throws InternalErrorException
     */
    protected function computeStockToTransitMoves(array $products_quantities, array $delivery_notes): array
    {
        $this->logger->notice('Compute quantities to move for each product, on which delivery note');
        $assignments = [];
        foreach ($products_quantities as $product_quantity) {
            $product_id = (int) $product_quantity['product_id'];

            $product_locations = $this->product_location_repository->getProductLocationsForProduct($product_id);

            // Big computation of quantities to move for each delivery note
            $rest_to_assign = $product_quantity['quantity'];
            foreach ($delivery_notes as $delivery_note) {
                if (0 === $rest_to_assign) {
                    continue;
                }

                $needed = array_reduce(
                    $delivery_note->delivery_note_products,
                    function ($count, $delivery_product) use ($product_id) {
                        if ($delivery_product->product_id === $product_id) {
                            $count += $delivery_product->quantity;
                        }

                        return $count;
                    },
                    0
                );
                $already_assigned = array_reduce(
                    $product_locations,
                    fn ($count, $product_location) => $count +
                        array_reduce(
                            $product_location['quantities'],
                            function ($count, array $quantity) use ($delivery_note) {
                                if ($quantity['delivery_ticket_id'] === $delivery_note->delivery_note_id) {
                                    $count += $quantity['quantity'];
                                }

                                return $count;
                            },
                            0
                        ),
                    0
                );

                // in certain circumstance, the quantity needed may be lower than movement in stock (delivery edition)
                $can_move = max($needed - $already_assigned, 0);
                $to_move = min($can_move, $rest_to_assign);

                if ($to_move > 0) {
                    $assignments[] = [
                        'product_id' => $product_id,
                        'delivery_ticket_id' => $delivery_note->delivery_note_id,
                        'quantity' => $to_move,
                    ];

                    $rest_to_assign -= $to_move;
                }
            }

            // should never happen in real condition, but stay prudent
            if ($rest_to_assign > 0) {
                throw new InternalErrorException(InternalError::DELIVERY_NOTE_MOVE_QUANTITY_EXCEED_NEEDS, new \LogicException('You try to move too much quantity compared to delivery needs'), ['product_id' => $product_id, 'overflow' => $rest_to_assign]);
            }
        }

        return $assignments;
    }

    /**
     * @return array{move: array<int, array{delivery_note_id: int, product_id: int, quantity: mixed}>, incomplete: array<int, array{delivery_note_id: int, product_id: int, need: float|int, picked: mixed}>}
     *
     * @throws InternalErrorException
     * @throws NotFoundException
     */
    protected function computeTransitToPreparationMoves(
        LocationEntity $origin_location,
        LocationEntity $destination_location,
        array $delivery_notes_ids
    ): array {
        $this->logger->notice('Compute which delivery notes will be completed and which will not');
        $result = [
            'move' => [],
            'incomplete' => [],
        ];
        $origin_location_products = $this->location_product_repository->findAll($origin_location->location_id);
        $destination_location_products = $this->location_product_repository->findAll(
            $destination_location->location_id
        );
        $delivery_notes = $this->getDeliveryNotes($delivery_notes_ids);
        foreach ($delivery_notes as $delivery_note) {
            foreach ($delivery_note->delivery_note_products as $delivery_note_product) {
                // extract the location product for quantity already prepared (in destination location)
                $products_prepared = $this->filterLocationProducts(
                    $destination_location_products,
                    [$delivery_note_product->product_id],
                    [$delivery_note->delivery_note_id]
                );
                $already_prepared = $products_prepared[0]['quantities'][0]['quantity'] ?? 0;

                $need = $delivery_note_product->quantity - $already_prepared;
                if (0 === $need) {
                    continue;
                }

                // extract the location product for quantity picked (in origin location)
                $products_picked = $this->filterLocationProducts(
                    $origin_location_products,
                    [$delivery_note_product->product_id],
                    [$delivery_note->delivery_note_id]
                );
                $picked = $products_picked[0]['quantities'][0]['quantity'] ?? 0;

                // Should never happen, but stay vigilent
                if ($picked > $need) {
                    throw new InternalErrorException(InternalError::DELIVERY_NOTE_PICKED_QUANTITY_EXCEED_REQUIREMENT, new \LogicException('Quantity picked exceed the requirement'), ['delivery_note_id' => $delivery_note->delivery_note_id, 'product_id' => $delivery_note_product->product_id, 'need' => $need, 'picked' => $picked]);
                }

                if ($need > $picked) {
                    $result['incomplete'][] = [
                        'delivery_note_id' => $delivery_note->delivery_note_id,
                        'product_id' => $delivery_note_product->product_id,
                        'need' => $need,
                        'picked' => $picked,
                    ];

                    continue 2;
                }

                $result['move'][] = [
                    'delivery_note_id' => $delivery_note->delivery_note_id,
                    'product_id' => $delivery_note_product->product_id,
                    'quantity' => $picked,
                ];
            }
        }

        // if a delivery is incomplete, all previous moves are removed
        $result['move'] = array_filter(
            $result['move'],
            fn ($movement): bool => !in_array(
                $movement['delivery_note_id'],
                array_column($result['incomplete'], 'delivery_note_id')
            )
        );

        return $result;
    }
}
