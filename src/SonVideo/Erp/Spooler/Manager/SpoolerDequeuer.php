<?php

namespace SonVideo\Erp\Spooler\Manager;

use Psr\Log\LoggerInterface;
use Son<PERSON>ideo\Erp\Referential\SpoolerEventAction;
use SonVideo\Erp\Spooler\Collection\SpoolerEventHandlerCollection;
use SonVideo\Erp\Spooler\Contract\SpoolerEventHandlerInterface;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerReadRepository;

class SpoolerDequeuer
{
    private LoggerInterface $logger;

    private SpoolerEventHandlerCollection $spooler_event_handler_collection;

    public function __construct(
        SpoolerReadRepository $spooler_repository,
        LoggerInterface $logger,
        SpoolerEventHandlerCollection $spooler_event_handler_collection
    ) {
        $this->logger = $logger;
        $this->spooler_event_handler_collection = $spooler_event_handler_collection;
    }

    public function dequeue(): void
    {
        foreach (SpoolerEventAction::DEQUEUEABLE_ORDERED_EVENTS as $event_name) {
            /** @var SpoolerEventHandlerInterface $handler|null */
            $handler = $this->spooler_event_handler_collection->getHandler($event_name);

            if (null === $handler) {
                $this->logger->warning(sprintf('Handler not found for event "%s"', $event_name));

                continue;
            }

            $this->logger->info('Dequeue-ing with handler', [
                'handler' => get_class($handler),
            ]);

            $handler->handle();
        }
    }
}
