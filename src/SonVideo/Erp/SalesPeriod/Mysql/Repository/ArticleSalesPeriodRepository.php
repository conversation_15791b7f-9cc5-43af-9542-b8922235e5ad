<?php

namespace SonVideo\Erp\SalesPeriod\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\DataLoader\MapToEntityTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\SalesPeriod\Dto\CreationContext\ArticleSalesPeriodCreationContextDto;
use SonVideo\Erp\SalesPeriod\Dto\UpdateContext\ArticleSalesPeriodUpdateContextDto;

final class ArticleSalesPeriodRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface, SerializerAwareInterface
{
    use LegacyPdoAwareTrait;
    use MapToEntityTrait;
    use DataLoaderAwareTrait;
    use SerializerAwareTrait;

    public const COLUMNS_MAPPING = [
        'article_id' => 'sp.id_produit',
        'sale_id' => 'sp.id',
        'is_active' => 'sp.actif',
        'order_quantity' => 'sp.quantite_commande',
        'sales_period_id' => 's.id',
        'start_at' => 's.date_debut',
        'stock_30d_quantity' => 'sp.quantite_stock_30j',
        'selling_price' => 'sp.prix_vente_solde',
        'initial_selling_price' => 'sp.prix_vente_constate_solde',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              {base_sql}
                WHERE {conditions}
              ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function create(ArticleSalesPeriodCreationContextDto $dto): int
    {
        $sql = <<<SQL
            INSERT INTO backOffice.solde_produit (actif, id_produit, id_solde, prix_vente_solde, prix_vente_constate_solde)
            VALUES
                (:is_active, :article_id, :sales_period_id, :selling_price, :initial_selling_price)
            ;
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'is_active' => true,
            'article_id' => $dto->article_id,
            'sales_period_id' => $dto->sales_period_id,
            'selling_price' => $dto->selling_price,
            'initial_selling_price' => $dto->initial_selling_price,
        ]);

        return $this->legacy_pdo->lastInsertId();
    }

    public function getInitialSellingPrice(int $article_id): float
    {
        $sql = <<<SQL
            SELECT
                prix_vente
            FROM backOffice.article
            WHERE id_produit = :article_id
        SQL;

        return $this->legacy_pdo->fetchValue($sql, ['article_id' => $article_id]);
    }

    /** @return false|array */
    public function findOneById(int $id)
    {
        $sql = <<<SQL
            {base_sql}
              WHERE sp.id = :id
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        return $this->legacy_pdo->fetchOne($sql, ['id' => $id]);
    }

    public function update(ArticleSalesPeriodUpdateContextDto $dto): int
    {
        $sql = <<<SQL
        UPDATE backOffice.solde_produit
        SET actif = :is_active,
            prix_vente_solde = :selling_price
        WHERE id = :article_sale_id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, $this->serializer->normalize($dto));
    }

    public function updateActive(bool $is_active, int $article_sale_id): int
    {
        $sql = <<<SQL
            UPDATE backOffice.solde_produit
                SET actif = :is_active
            WHERE id = :article_sale_id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'is_active' => $is_active,
            'article_sale_id' => $article_sale_id,
        ]);
    }

    private function getBaseSql(): string
    {
        return <<<SQL
            SELECT
                  sp.id_produit AS article_id,
                  sp.id AS sale_id,
                  sp.actif AS is_active,
                  sp.quantite_commande AS order_quantity,
                  s.id AS sales_period_id,
                  s.date_debut AS start_at,
                  sp.quantite_stock_30j AS stock_30d_quantity,
                  sp.prix_vente_solde AS selling_price,
                  sp.prix_vente_constate_solde AS initial_selling_price
              FROM backOffice.article a
                INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
                INNER JOIN backOffice.solde_produit sp ON a.id_produit = sp.id_produit
                INNER JOIN backOffice.solde s ON sp.id_solde = s.id
        SQL;
    }
}
