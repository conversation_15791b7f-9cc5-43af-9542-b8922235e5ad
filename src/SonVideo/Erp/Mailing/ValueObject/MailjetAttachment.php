<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\ValueObject;

/**
 * Utility class which format an attachment content for usage in mailjet API.
 */
final class MailjetAttachment
{
    public const TYPE_PDF = 'application/pdf';
    public const TYPE_CSV = 'text/csv';

    private string $base64_content;

    private string $filename;

    private string $mime_type;

    public function __construct(string $content, string $filename, string $mime_type)
    {
        $this->base64_content = base64_encode($content);
        $this->filename = $filename;
        $this->mime_type = $mime_type;
    }

    /** @return array{ContentType: string, Filename: string, Base64Content: string} */
    public function toArray(): array
    {
        return [
            'ContentType' => $this->mime_type,
            'Filename' => $this->filename,
            'Base64Content' => $this->base64_content,
        ];
    }
}
