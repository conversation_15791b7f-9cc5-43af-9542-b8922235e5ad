<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Manager\Contact;

use SonVideo\Erp\Mailing\Manager\AbstractEmailDispatcher;
use Symfony\Component\Validator\Constraint;

class ContactEmailDispatcher extends AbstractEmailDispatcher
{
    protected const BUSINESS_KEY = 'contact';

    /** {@inheritDoc} */
    protected function getValidationRules(array $data): Constraint
    {
        return ContactEmailPayloadValidation::rules();
    }
}
