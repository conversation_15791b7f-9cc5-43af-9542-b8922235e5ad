<?php

namespace SonVideo\Erp\Mailing\Manager\CustomerOrderAvailable;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;

class CustomerOrderAvailableEmailPayloadValidation
{
    public static function rules(): Constraint
    {
        return new Assert\Collection([
            'to' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
            'context' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer_order_id' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'ts' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'customer' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Collection([
                            'customer_id' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('integer'),
                            ]),
                            'civility' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'firstname' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                            'lastname' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                        ]),
                    ]),
                    'count_articles' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'articles' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Type('array'),
                        new Assert\All([
                            new Assert\Collection([
                                'sku' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('string'),
                                ]),
                                'quantity' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('integer'),
                                ]),
                                'description' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('string'),
                                ]),
                                'url_image' => new Assert\Required([new Assert\Type('string'), new Assert\NotNull()]),
                                'image_size' => new Assert\Required([new Assert\Type('integer')]),
                            ]),
                        ]),
                    ]),
                    'prices' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Collection([
                            'discount_amount' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'discount_amount_formatted' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'delivery_fees' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'delivery_fees_formatted' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'extra_cost' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'extra_cost_formatted' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'selling_price_tax_included' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'selling_price_tax_included_formatted' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'discount_amount_without_delivery_fees' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'discount_amount_without_delivery_fees_formatted' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                        ]),
                    ]),
                ]),
            ]),
            '_rel' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'customer_order' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                ]),
            ]),
        ]);
    }
}
