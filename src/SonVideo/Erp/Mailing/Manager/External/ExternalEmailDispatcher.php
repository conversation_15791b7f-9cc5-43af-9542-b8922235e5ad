<?php

namespace SonVideo\Erp\Mailing\Manager\External;

use League\Flysystem\FileNotFoundException;
use League\Flysystem\MountManager;
use SonVideo\Erp\Mailing\Manager\AbstractEmailDispatcher;
use SonVideo\Erp\Mailing\Manager\EmailSystemEventFactory;
use SonVideo\Erp\Mailing\ValueObject\MailjetAttachment;
use Symfony\Component\Validator\Constraint;

class ExternalEmailDispatcher extends AbstractEmailDispatcher
{
    protected const BUSINESS_KEY = 'external';

    private MountManager $mount_manager;

    public function __construct(MountManager $mount_manager, EmailSystemEventFactory $email_system_event_factory)
    {
        $this->mount_manager = $mount_manager;
        parent::__construct($email_system_event_factory);
    }

    /** {@inheritDoc} */
    protected function getValidationRules(array $data): Constraint
    {
        return ExternalEmailPayloadValidation::rules();
    }

    /** @throws FileNotFoundException */
    protected function getAttachments(array $data): array
    {
        if (!array_key_exists('_attachments', $data)) {
            return [];
        }

        $mailjetAttachments = [];

        foreach ($data['_attachments'] as $attachment) {
            $filesystem = $this->mount_manager->getFilesystem($attachment['filesystem']);
            $filePath = $attachment['file_path'];

            // The file must exist
            if (!$filesystem->has($filePath)) {
                throw new \UnexpectedValueException(sprintf('File not found at "%s"', $filePath));
            }

            $mailjetAttachments[] = new MailjetAttachment(
                $filesystem->read($filePath),
                $attachment['file_name'],
                $attachment['mime_type']
            );
        }

        return $mailjetAttachments;
    }
}
