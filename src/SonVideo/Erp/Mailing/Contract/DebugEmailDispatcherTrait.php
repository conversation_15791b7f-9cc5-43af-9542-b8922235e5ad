<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Contract;

trait DebugEmailDispatcherTrait
{
    /** @var array */
    protected $debug_options = [];

    public function setDebugOptions(array $options): DebugEmailDispatcherInterface
    {
        $this->debug_options = $options;

        return $this;
    }

    /** @return mixed */
    public function getOption(string $key)
    {
        return $this->debug_options[$key] ?? null;
    }
}
