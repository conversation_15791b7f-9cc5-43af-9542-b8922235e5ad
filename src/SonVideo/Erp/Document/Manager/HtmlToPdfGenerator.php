<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Document\Manager;

use Http\Adapter\Guzzle6\Client as GuzzleCLient;
use TheCodingMachine\Gotenberg\Client;
use TheCodingMachine\Gotenberg\ClientException;
use TheCodingMachine\Gotenberg\DocumentFactory;
use TheCodingMachine\Gotenberg\FilesystemException;
use TheCodingMachine\Gotenberg\HTMLRequest;
use TheCodingMachine\Gotenberg\Request as GotenbergRequest;
use TheCodingMachine\Gotenberg\RequestException;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

class HtmlToPdfGenerator
{
    private const DEFAULT_MARGINS = [0, 0, 0, 0];
    private const USE_LANDSCAPE = false;
    private const USE_A4_FOOTER = false;

    private Environment $twig;

    private ?string $project_path = null;

    private string $erp_gotenberg_endpoint;

    /** PDFGenerator constructor. */
    public function __construct(Environment $twig, string $erp_gotenberg_endpoint)
    {
        $this->twig = $twig;
        $this->erp_gotenberg_endpoint = $erp_gotenberg_endpoint;
    }

    /** @return $this */
    public function setProjectPath(string $project_path): self
    {
        $this->project_path = $project_path;

        return $this;
    }

    /**
     * @throws ClientException
     * @throws FilesystemException
     * @throws RequestException
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     */
    public function generate(array $content, string $template_path, array $options = []): string
    {
        // For big PDFs, make sure that the script does not time out
        set_time_limit(0);

        $html = $this->twig->render($template_path, $content);

        // Use the instance shared on the docker network (not accessible from outside)
        $client = new Client($this->erp_gotenberg_endpoint, new GuzzleCLient());

        // Create a dummy proxy file where the generated html will be located
        $index = DocumentFactory::makeFromString('index.html', $html);

        // Create the Gotenberg request
        $request = new HTMLRequest($index);

        // Use style from app path, use the content as dummy proxy like the index.html
        // The target html file must also include a style tag pointing to the style.css file (eg: it does not need to exist in the app)
        // Gotenberg uses those dummy files and path to create its own webpage and render the content to parse
        // with the chrome engine (<link href="style.css" rel="stylesheet" type="text/css">)

        $assets = [DocumentFactory::makeFromPath('style.css', sprintf('%s/public/output.css', $this->project_path))];
        $request->setAssets($assets);

        $request->setPaperSize($options['paper_size'] ?? GotenbergRequest::A4);

        // Arbitrary default (tiny) margins (in inches)
        $request->setMargins($options['margins'] ?? self::DEFAULT_MARGINS);

        $request->setLandscape($options['use_landscape'] ?? self::USE_LANDSCAPE);

        // Default delay to prevent timeout on long multi page documents
        $request->setWaitTimeout(30);

        if ($options['use_a4_footer'] ?? self::USE_A4_FOOTER) {
            // Beware : CSS & other assets are not forwarded to footer & header (eg: no image & no style)
            $request->setFooter(
                DocumentFactory::makeFromString('footer.html', $this->twig->render('document/pdf/footer.html.twig'))
            );
        }

        return $client->post($request)->getBody();
    }
}
