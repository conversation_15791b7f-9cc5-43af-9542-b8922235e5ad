<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\SupplierOrderProduct\Mysql\Repository;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\SupplierOrderProduct\Entity\SupplierOrderProductEntity;

class SupplierOrderProductRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'supplier_order_product_id' => 'pcf.supplier_order_product_id',
        'supplier_order_id' => 'pcf.id_commande_fournisseur',
        'article_id' => 'pcf.id_produit',
        'ordered_quantity' => 'pcf.quantite_commandee',
        'delivered_quantity' => 'pcf.quantite_livree',
        'is_delivered' => 'pcf.quantite_livree = pcf.quantite_commandee',
        'sku' => 'p.reference',
        'article_name' => 'a.modele',
        'article_image' => 'COALESCE(bc.media_300_square_uri, bc.media_largest_uri)',
        'supplier_id' => 'cf.id_fournisseur',
        'status' => 'cf.status',
        'created_at' => 'cf.date_creation',
        'updated_at' => 'pcf.modif_date',
        'sent_at' => 'CAST(email.date_envoie AS DATE)',
        'dispute' => 'cf.litige',
        'supplier_recall' => 'cf.rappel_fournisseur',
        'expected_delivery_date' => 'pcf.date_livraison_prevue',
        'billed' => '(pcf.date_facturation IS NOT NULL AND pcf.quantite_commandee = pcf.quantite_facturee)',
        'delivered' => 'pcf.quantite_commandee = floor(pcf.quantite_livree)',
        'backorder_quantity' => 'a.V_qte_cmd_attente',
        'available_backorder_quantity' => 'backOffice.PDT_ART_compute_backorder_quantity(a.id_produit)',
        'safety_stock' => 'a.V_stock_securite',
        'stock_quantity' => 'a.V_quantite_stock',
    ];

    public function findAllPaginated(QueryBuilder $query_builder, ?string $backorder_order_by): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *,
          IF(
            lpcf.id IS NOT NULL,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'expected_delivery_id', lpcf.id,
                'supplier_order_product_id', lpcf.id_produit_commande_fournisseur,
                'expected_delivery_date', lpcf.date_livraison_prevue,
                'expected_quantity', lpcf.quantite_livraison_prevue
              )
            ),
            JSON_ARRAY()
          ) AS expected_deliveries
          FROM (
            SELECT
              backOffice.PDT_ART_compute_backorder_quantity(a.id_produit) as available_backorder_quantity,
              if(
                backOffice.PDT_ART_compute_backorder_quantity(a.id_produit) < 0,
                '50 red',
                if(
                  a.V_qte_cmd_attente > a.V_quantite_stock AND backOffice.PDT_ART_compute_backorder_quantity(a.id_produit) > 0,
                  '40 orange',
                  if(
                    a.V_quantite_stock - a.V_qte_cmd_attente < a.V_stock_securite,
                    '30 yellow',
                    if(
                      a.V_quantite_stock = 0 AND a.V_qte_cmd_attente = 0,
                      '20 gray',
                      if(
                        a.V_quantite_stock - a.V_qte_cmd_attente >= a.V_stock_securite,
                        '10 green',
                        '20 gray'
                      )
                    )
                  )
                )
              ) AS product_state,
              pcf.id AS supplier_order_product_id,
              pcf.id_commande_fournisseur AS supplier_order_id,
              pcf.id_produit AS article_id,
              pcf.quantite_commandee AS ordered_quantity,
              pcf.quantite_livree AS delivered_quantity,
              pcf.quantite_livree = pcf.quantite_commandee AS is_delivered,
              p.reference AS sku,
              a.reference_fournisseur AS supplier_reference,
              a.description_courte AS description,
              a.modele AS article_name,
              a.V_delai_lvr AS article_delay,
              backOffice.PDT_statut(a.id_produit) AS article_status,
              COALESCE(bc.media_300_square_uri, bc.media_largest_uri) AS article_image,
              f.id_fournisseur AS supplier_id,
              f.fournisseur AS supplier_name,
              cf.date_creation AS created_at,
              pcf.modif_date AS updated_at,
              email.date_envoie AS sent_at,
              cf.status,
              pcf.prix_achat AS bought_price_tax_excluded,
              (pcf.prix_achat * (pcf.tva + 1)) AS bought_price_tax_included,
              pcf.date_livraison_prevue AS expected_delivery_date,
              a.V_qte_cmd_attente AS backorder_quantity,
              a.V_stock_securite AS safety_stock,
              a.V_quantite_stock AS stock_quantity
            FROM backOffice.produit_commande_fournisseur pcf
            INNER JOIN backOffice.commande_fournisseur cf ON pcf.id_commande_fournisseur = cf.id_commande_fournisseur
            INNER JOIN backOffice.fournisseur f ON cf.id_fournisseur = f.id_fournisseur
            INNER JOIN backOffice.produit p ON p.id_produit = pcf.id_produit
            INNER JOIN backOffice.article a ON a.id_produit = pcf.id_produit
            LEFT JOIN backOffice.batch_catalog bc ON bc.article_id = pcf.id_produit
            LEFT JOIN backOffice.BO_CMD_FRN_mail email ON cf.id_commande_fournisseur = email.id
          WHERE {conditions}
          GROUP BY pcf.id
        ) tmp
        LEFT JOIN backOffice.livraison_produit_commande_fournisseur lpcf ON tmp.supplier_order_product_id = lpcf.id_produit_commande_fournisseur
        GROUP BY tmp.supplier_order_product_id
        {order_by}
        SQL;

        switch (strtoupper($backorder_order_by)) {
            case 'ASC':
                $query_builder->setOrderByCriterion([
                    ['sort_by' => 'product_state', 'sort_direction' => 'asc'],
                    ['sort_by' => 'backorder_quantity', 'sort_direction' => 'desc'],
                ]);
                break;
            case 'DESC':
                $query_builder->setOrderByCriterion([
                    ['sort_by' => 'product_state', 'sort_direction' => 'desc'],
                    ['sort_by' => 'available_backorder_quantity', 'sort_direction' => 'asc'],
                ]);
                break;
        }

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function upsertSupplierOrderProduct(
        int $supplier_order_id,
        int $product_id,
        int $quantity,
        float $price
    ): int {
        $sql = <<<SQL
        INSERT INTO backOffice.produit_commande_fournisseur
            (id_commande_fournisseur, id_produit, quantite_commandee, prix_achat)
        VALUES
            (:supplier_order_id, :product_id, :quantity, :price)
        ON DUPLICATE KEY UPDATE
            quantite_commandee = quantite_commandee + :quantity,
            prix_achat = :price;
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'supplier_order_id' => $supplier_order_id,
            'product_id' => $product_id,
            'quantity' => $quantity,
            'price' => $price,
        ]);

        return $this->legacy_pdo->lastInsertId();
    }

    /** @throws NotFoundException */
    public function findOneById(int $supplier_order_product_id): SupplierOrderProductEntity
    {
        $sql = <<<SQL
        SELECT
            pcf.id AS supplier_order_product_id,
            pcf.id_commande_fournisseur AS supplier_order_id,
            pcf.id_produit AS article_id,
            pcf.quantite_commandee AS ordered_quantity,
            pcf.quantite_livree AS delivered_quantity,
            pcf.quantite_livree = pcf.quantite_commandee AS is_delivered,
            pcf.modif_date AS updated_at,
            pcf.prix_achat AS bought_price_tax_excluded,
            (pcf.prix_achat * (pcf.tva + 1)) AS bought_price_tax_included,
            pcf.date_livraison_prevue  AS expected_delivery_date
        FROM backOffice.produit_commande_fournisseur pcf
        WHERE id = :supplier_order_product_id
        SQL;

        $supplier_order_product = $this->legacy_pdo->fetchOne($sql, [
            'supplier_order_product_id' => $supplier_order_product_id,
        ]);

        if (false === $supplier_order_product) {
            throw new NotFoundException('Supplier order product not found.');
        }

        return $this->data_loader->hydrate($supplier_order_product, SupplierOrderProductEntity::class);
    }

    /** @throws SqlErrorMessageException */
    public function exists(int $supplier_order_id, int $product_id): bool
    {
        $sql = <<<SQL
        SELECT
          EXISTS(
            SELECT 1
            FROM backOffice.produit_commande_fournisseur
            WHERE id_commande_fournisseur = :supplier_order_id
            AND id_produit = :product_id
          ) AS entry;
        SQL;
        $result = $this->legacy_pdo->fetchValue($sql, [
            'supplier_order_id' => $supplier_order_id,
            'product_id' => $product_id,
        ]);

        return (bool) $result;
    }

    public function pictureSupplierOrderProduct(): array
    {
        $sql = <<<SQL
        SELECT
            subdate(current_date, 1) AS date,
            cf.id_fournisseur AS supplier_id,
            pcf.id AS supplier_order_product_id,
            pcf.id_commande_fournisseur AS supplier_order_id,
            pcf.id_produit AS product_id,
            pcf.quantite_livree AS delivered_quantity,
            pcf.quantite_commandee AS ordered_quantity,
            pcf.date_livraison_prevue AS expected_delivery_date,
            pcf.date_livraison_effective AS actual_delivery_date,
            pcf.prix_achat AS buying_price,
            bfl.status AS dispute,
            cf.type AS supplier_order_type
        FROM backOffice.produit_commande_fournisseur pcf
        INNER JOIN backOffice.commande_fournisseur cf ON pcf.id_commande_fournisseur = cf.id_commande_fournisseur
        LEFT JOIN backOffice.BO_FRN_litige_produit bflp ON bflp.id_produit_commande_fournisseur = pcf.id
        LEFT JOIN backOffice.BO_FRN_litige bfl ON bfl.id = bflp.id_litige AND bfl.status = 1
        WHERE (pcf.quantite_livree < pcf.quantite_commandee AND cf.status = 'en cours')
        OR (pcf.date_livraison_effective = subdate(current_date, 1) OR pcf.modif_date = subdate(current_date, 1))
        SQL;

        return $this->legacy_pdo->fetchAll($sql);
    }

    public function fetchSupplierOrderDisputes(): array
    {
        $sql = <<<SQL
        SELECT
            now() AS date,
            count(distinct cf.id_commande_fournisseur) AS nb_supplier_order,
            count(BFl.id) AS nb_supplier_order_dispute
        FROM backOffice.commande_fournisseur cf
        LEFT JOIN backOffice.BO_FRN_litige BFl ON cf.id_commande_fournisseur = BFl.id_commande_fournisseur AND BFl.status=1
        WHERE cf.status='en cours';
        SQL;

        return $this->legacy_pdo->fetchOne($sql);
    }

    /** @throws \Exception */
    public function makeStockEntry(
        int $user_id,
        ?int $supplier_order_id,
        ?int $transfer_id,
        int $product_id,
        int $quantity,
        int $warehouse_id
    ): ?string {
        $sql = <<<SQL
            CALL backOffice.WMS_entre_stock(:user_id, :supplier_order_id, :transfer_id, :product_id, :quantity, :warehouse_id);
        SQL;

        return $this->legacy_pdo->fetchValue($sql, [
            'user_id' => $user_id,
            'supplier_order_id' => $supplier_order_id,
            'transfer_id' => $transfer_id,
            'product_id' => $product_id,
            'quantity' => $quantity,
            'warehouse_id' => $warehouse_id,
        ]);
    }
}
