<?php

namespace SonVideo\Erp\Task\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Task\Entity\TaskEntity;

class TaskRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'task_id' => 't.id',
        'customer_order_id' => 't.id_commande',
        'customer_id' => 't.id_prospect',
        'parcel_id' => 't.id_colis',
        'parcel_number' => 'cl.no_colis',
        'article_id' => 't.id_produit',
        'sku' => 'pdt.reference',
        'user_id' => 't.id_utilisateur',
        'user_origin_id' => 't.id_utilisateur_origine',
        'type_id' => 't.id_type',
        'created_at' => 't.date_creation',
        'limited_at' => 't.date_limite',
        'finished_at' => 't.date_cloture',
        'updated_at' => 't.derniere_modification',
        'subject' => 't.sujet',
        'comment' => 't.commentaire',
        'description' => 't.description',
        'supplier_order_id' => 't.id_commande_fournisseur',
        'carrier_id' => 'tr.transporteur',
        'customer_order_id_from_delivery_note' => 'bl.id_commande',
        'customer_id_from_customer_order' => 'c.id_prospect',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                t.id                                           AS task_id,
                t.id_commande                                  AS customer_order_id,
                t.id_prospect                                  AS customer_id,
                t.id_colis                                     AS parcel_id,
                cl.no_colis                                    AS parcel_number,
                t.id_produit                                   AS article_id,
                pdt.reference                                  AS sku,
                t.id_utilisateur                               AS user_id,
                backOffice.GET_COMPUTED_USER_NAME_BY_ID(up.id) AS user_name,
                t.id_utilisateur_origine                       AS user_origin_id,
                t.id_type                                      AS type_id,
                t.date_creation                                AS created_at,
                t.date_limite                                  AS limited_at,
                t.date_cloture                                 AS finished_at,
                t.derniere_modification                        AS updated_at,
                (NOW() > t.date_limite)                        AS is_expired,
                t.sujet                                        AS subject,
                t.commentaire                                  AS comment,
                backOffice.TC_message(t.id)                    AS description,
                t.id_commande_fournisseur                      AS supplier_order_id,
                tr.transporteur                                AS carrier_id,
                bl.id_commande                                 AS customer_order_id_from_delivery_note,
                c.id_prospect                                  AS customer_id_from_customer_order,
                CASE
                  WHEN t.id_commande IS NOT NULL
                    THEN
                    CASE
                      WHEN t.id_type = 20
                        THEN backOffice.GET_COMPUTED_USER_NAME_BY_ID(up.id)
                      ELSE CONCAT(c.cnt_fct_civilite, ' ', c.cnt_fct_nom, ' ', c.cnt_fct_prenom) END
                  WHEN t.id_prospect IS NOT NULL
                    THEN
                    CASE
                      WHEN p.cnt_nom != 'INCONNU'
                        THEN CONCAT(p.cnt_civilite, ' ', p.cnt_nom, ' ', p.cnt_prenom)
                      ELSE p.cnt_email END
                  END                                          AS sent_by
                FROM
                  backOffice.TC_tache t
                    LEFT JOIN backOffice.commande c ON t.id_commande = c.id_commande
                    LEFT JOIN backOffice.produit pdt ON t.id_produit = pdt.id_produit
                    LEFT JOIN backOffice.prospect p ON t.id_prospect = p.id_prospect
                    LEFT JOIN backOffice.colis cl ON t.id_colis = cl.id_colis
                    LEFT JOIN backOffice.bon_livraison bl ON cl.id_bon_livraison = bl.id_bon_livraison
                    LEFT JOIN backOffice.transporteur tr ON tr.id_transporteur = bl.id_transporteur
                    INNER JOIN backOffice.sf_guard_user_profile up ON up.id = t.id_utilisateur
                WHERE {conditions}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /**
     * create.
     *
     * add a message in the list of task of the specified user
     *
     * @throws Exception
     */
    public function create(
        int $user_id,
        string $subject,
        string $comment = null,
        string $description = null,
        int $customer_order_id = null
    ): int {
        $sql = <<<SQL
        INSERT INTO backOffice.TC_tache
        SET
          id_commande    = :customer_order_id,
          id_utilisateur = :user_id,
          id_type        = :type_id,
          date_creation  = NOW(),
          date_limite    = DATE_ADD(NOW(), INTERVAL 48 HOUR),
          sujet          = :subject,
          commentaire    = :comment,
          description    = :description;
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'user_id' => $user_id,
            'subject' => $subject,
            'type_id' => TaskEntity::TYPE_ORDER_COMMENT,
            'comment' => $comment,
            'description' => $description,
            'customer_order_id' => $customer_order_id,
        ]);
    }

    /** @throws Exception */
    public function countTasksByTypeAndCustomerOrder(int $type_id, int $customer_order_id): bool
    {
        $sql = <<<SQL
        SELECT COUNT(id_commande)
        FROM backOffice.TC_tache
        WHERE id_commande = :id_commande AND id_type = :type_id;
        SQL;

        return $this->legacy_readonly_pdo->fetchValue($sql, [
            'id_commande' => $customer_order_id,
            'type_id' => $type_id,
        ]);
    }

    /** @throws Exception */
    public function createOrderTask(int $type_id, int $customer_order_id, string $description = null): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.TC_tache
            (id_commande, id_utilisateur, id_type, date_creation, date_limite, sujet, description)
        SELECT
            :customer_order_id,
            tt.id_utilisateur_defaut,
            :type_id,
            NOW(),
            DATE_ADD(NOW(), INTERVAL tt.limite HOUR),
            tt.nom,
            :description
        FROM backOffice.TC_type tt
        WHERE id = :type_id

        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'customer_order_id' => $customer_order_id,
            'type_id' => $type_id,
            'description' => $description,
        ]);
    }

    public function createProductTask(
        int $type_id,
        int $product_id,
        string $description = null,
        int $user_id = null
    ): int {
        $sql = <<<SQL
        INSERT INTO backOffice.TC_tache
            (id_produit, id_utilisateur, id_type, date_creation, date_limite, sujet, description)
        SELECT
            :product_id,
            COALESCE(:user_id, tt.id_utilisateur_defaut),
            :type_id,
            NOW(),
            DATE_ADD(NOW(), INTERVAL tt.limite HOUR),
            tt.nom,
            :description
        FROM backOffice.TC_type tt
        WHERE id = :type_id

        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'product_id' => $product_id,
            'type_id' => $type_id,
            'description' => $description,
            'user_id' => $user_id,
        ]);
    }

    /** @throws Exception */
    public function countTasksByUser(int $user_id): int
    {
        $sql = <<<SQL
        SELECT COUNT(*)
        FROM backOffice.TC_tache t
        WHERE t.id_utilisateur = :user_id
         AND t.date_cloture IS NULL;
        SQL;

        return $this->legacy_readonly_pdo->fetchValue($sql, ['user_id' => $user_id]);
    }
}
