<?php

namespace SonVideo\Erp\Task\Entity;

use App\Entity\AbstractEntity;

class TaskEntity extends AbstractEntity
{
    public const TYPE_ORDER_COMMENT = 20;
    public const TYPE_YAPU_LAST = 29;
    public const TYPE_ERROR_UPDATE_PRICE = 43;

    public int $task_id;

    public ?int $customer_order_id = null;

    public ?int $customer_id = null;

    public ?int $parcel_id = null;

    public ?string $parcel_number = null;

    public ?int $article_id = null;

    public ?string $sku = null;

    public int $user_id;

    public string $user_name;

    public ?int $user_origin_id = null;

    public ?int $type_id = null;

    public \DateTimeInterface $created_at;

    public ?\DateTimeInterface $limited_at = null;

    public ?\DateTimeInterface $finished_at = null;

    public ?\DateTimeInterface $updated_at = null;

    public bool $is_expired;

    public ?string $subject = null;

    public ?string $comment = null;

    public ?string $description = null;

    public ?int $supplier_order_id = null;

    public ?int $carrier_id = null;

    public ?int $customer_order_id_from_delivery_note = null;

    public ?int $customer_id_from_customer_order = null;

    public ?string $sent_by = null;
}
