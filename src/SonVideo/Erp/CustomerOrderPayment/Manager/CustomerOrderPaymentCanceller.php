<?php

namespace SonVideo\Erp\CustomerOrderPayment\Manager;

use App\Contract\DataLoaderAwareInterface;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\LegacyPdo;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository;

class CustomerOrderPaymentCanceller implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    private LegacyPdo $legacy_pdo;

    private CustomerOrderPaymentWriteRepository $customer_order_payment_write_repository;

    private CustomerOrderRepository $customer_order_repository;

    private CustomerOrderPaymentReadRepository $customer_order_payment_read_repository;

    public function __construct(
        LegacyPdo $legacy_pdo,
        CustomerOrderRepository $customer_order_repository,
        CustomerOrderPaymentReadRepository $customer_order_payment_read_repository,
        CustomerOrderPaymentWriteRepository $customer_order_payment_write_repository
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->customer_order_payment_write_repository = $customer_order_payment_write_repository;
        $this->customer_order_repository = $customer_order_repository;
        $this->customer_order_payment_read_repository = $customer_order_payment_read_repository;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws \Exception
     */
    public function cancel(int $customer_order_id): void
    {
        if (!$this->customer_order_repository->exists($customer_order_id)) {
            throw new NotFoundException(sprintf('Customer order not found with id "%s"', $customer_order_id));
        }

        if (1 != $this->customer_order_payment_read_repository->hasAwaitingPayment($customer_order_id)) {
            throw new NotFoundException(sprintf('No awaiting payment found for the customer order "%s"', $customer_order_id));
        }

        try {
            $this->legacy_pdo->beginTransaction();

            $this->customer_order_payment_write_repository->cancel($customer_order_id);

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }
    }
}
