<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrderPayment\Manager;

use SonVideo\Erp\Client\Erpv1PaymentSoapClient;
use SonVideo\Erp\CustomerOrder\Dto\CustomerOrderBasicInfo;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrderPayment\Exception\BankRedirectionPayloadException;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;
use SonVideo\Erp\Payment\Manager\PaymentV2State;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class BankRedirectionHandler
{
    private ValidatorInterface $validator;

    private CustomerOrderPaymentReadRepository $customer_order_payment_read_repository;

    private Erpv1PaymentSoapClient $soap_client;

    private string $erpv1_payment_webservice_url;

    private PaymentV2State $payment_v2_state;

    private CustomerOrderRepository $customer_order_repository;

    public function __construct(
        ValidatorInterface $validator,
        CustomerOrderPaymentReadRepository $customer_order_payment_read_repository,
        string $erpv1_payment_webservice_url,
        Erpv1PaymentSoapClient $soap_client,
        PaymentV2State $payment_v2_state,
        CustomerOrderRepository $customer_order_repository
    ) {
        $this->validator = $validator;
        $this->customer_order_payment_read_repository = $customer_order_payment_read_repository;
        $this->soap_client = $soap_client;
        $this->erpv1_payment_webservice_url = $erpv1_payment_webservice_url;
        $this->payment_v2_state = $payment_v2_state;
        $this->customer_order_repository = $customer_order_repository;
    }

    /**
     * @return array|false|void
     *
     * @throws BankRedirectionPayloadException
     * @throws \SoapFault
     */
    public function handle(array $data)
    {
        // Validate payload
        $errors = $this->validator->validate($data, BankRedirectionPayloadValidation::rules());

        if (count($errors) > 0) {
            throw new BankRedirectionPayloadException(sprintf('Invalid request parameters : %s', ConstraintMessageFormatter::prettify($errors)));
        }

        // Fetch remaining payments
        $remaining_payments = $this->customer_order_payment_read_repository->fetchAwaitingPayments(
            $data['customer_order_id']
        );

        if ([] === $remaining_payments) {
            return false;
        }

        $basic_infos = $this->customer_order_repository->findBasicInfoFor($data['customer_order_id']);
        if (!$basic_infos instanceof CustomerOrderBasicInfo) {
            throw new BankRedirectionPayloadException(sprintf('Could not load customer order basic information with id "%s"', $data['customer_order_id']));
        }

        foreach ($remaining_payments as $payment) {
            // Make sure that payment handled by another handler (Payment v2) do not pass in the old pipeline
            if ($this->payment_v2_state->canHandle($payment->payment_method_code, $basic_infos->customer_id)) {
                continue;
            }

            try {
                $response = $this->soap_client
                    ->init('', [
                        // our soap implementation in legacy requires to override the SOAP version
                        'soap_version' => SOAP_1_1,
                        'location' => $this->erpv1_payment_webservice_url,
                        // no wsdl supported, this key is required
                        'uri' => $data['request_uri'],
                        'stream_context' => stream_context_create([
                            'ssl' => [
                                'verify_peer' => false,
                                'verify_peer_name' => false,
                            ],
                        ]),
                    ])
                    ->__soapCall(
                        'obt_serveur_banque',
                        BankRedirectionParameterBuilder::getParametersFor((array) $payment, $data['articles'])
                    );

                // Still useful for debugging purpose
                $this->soap_client->logRequestAndResponse();

                // Format the response to be usable with HTML code
                // Handle only one redirection at a time
                return $this->output($this->extract($response));
            } catch (\Exception $exception) {
                $this->soap_client->logRequestAndResponse('error');

                throw $exception;
            }
        }

        return null;
    }

    /** @return array{url: string, method: string, query_string: string} */
    private function extract(string $raw_xml_response): array
    {
        $xml = new \DomDocument();
        if (!$xml->loadXML($raw_xml_response)) {
            throw new \InvalidArgumentException('Failed to load xml from redirection string');
        }

        $root = $xml->documentElement;

        return [
            'url' => $this->extractNodeValueFromRoot('url', $root),
            'method' => $this->extractNodeValueFromRoot('method', $root),
            'query_string' => $this->extractNodeValueFromRoot('query_string', $root),
        ];
    }

    private function extractNodeValueFromRoot(string $key, \DOMElement $root): string
    {
        return $root->getElementsByTagName($key)->item(0)->nodeValue;
    }

    private function output(array $params): array
    {
        return 'GET' === $params['method']
            ? // paypal, fianet, svdcc
            [
                'redirect_to' => sprintf('%s?%s', $params['url'], $params['query_string']),
            ]
            : // other payments
            [
                'auto_form' => $params['query_string'],
            ];
    }
}
