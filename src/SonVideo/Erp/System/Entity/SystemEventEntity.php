<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\System\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

class SystemEventEntity extends AbstractEntity
{
    public string $id_for_type;

    public ?\DateTimeInterface $created_at = null;

    public ?string $type = null;

    /** @var array|JsonType|null */
    public ?array $payload = null;

    /** @var array|JsonType|null */
    public ?array $emitter = null;

    public ?int $main_id = null;
}
