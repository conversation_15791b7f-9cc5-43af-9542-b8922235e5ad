<?php
/*
 * This file is part of erp package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\SupplierOrder\Entity;

use App\Entity\AbstractEntity;
use SonVideo\Erp\SupplierOrder\Entity\Children\SupplierOrderProductEntity;

class SupplierOrderEntity extends AbstractEntity
{
    public int $supplier_order_id;

    public int $supplier_id;

    public string $supplier_name;

    public int $warehouse_id;

    public string $created_at;

    public string $status;

    public string $comment;

    public ?\DateTimeInterface $sent_at = null;

    public ?int $ordered_quantity = null;

    public ?int $delivered_quantity = null;

    public ?\DateTimeInterface $updated_at = null;

    /** @var SupplierOrderProductEntity[] */
    public array $supplier_order_products = [];
}
