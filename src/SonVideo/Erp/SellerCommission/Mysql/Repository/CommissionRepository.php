<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\SellerCommission\Mysql\Repository;

use App\DataLoader\MapToEntityTrait;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\SellerCommission\Entity\CommissionEntity;

class CommissionRepository extends AbstractLegacyRepository
{
    use MapToEntityTrait;

    public const COLUMNS_MAPPING = [
        'user_id' => 'sgup.id',
        'is_active' => 'sgu.is_active',
        'warehouse_role' => 'mscrsu.role',
        'retail_store_id' => 'mscrsu.retail_store_id',
    ];

    public function findAllAtWhere(string $at, QueryBuilder $query_builder): array
    {
        $sql = <<<SQL
        SELECT
            sgup.id AS user_id,
            sgu.username,
            backOffice.format_person_fullname(sgup.prenom, sgup.nom) AS full_name,
            mscrsu.retail_store_id AS warehouse_id,
            bsd.nom_depot AS warehouse_name,
            mscrsu.role AS warehouse_role,
            mscrsu.level AS warehouse_role_level,
            JSON_OBJECT(
                'b2c_hifi', mscb_b2c_hifi.amount,
                'b2c_image', mscb_b2c_image.amount,
                'b2b', mscb_b2b.amount,
                'long_duration_warranty', msldwb.amount,
                'team', mscb_team.amount,
                'manager', mscb_manager.amount,
                'total', (
                    COALESCE(mscb_b2c_hifi.amount, 0) +
                    COALESCE(mscb_b2c_image.amount, 0) +
                    COALESCE(mscb_b2b.amount, 0) +
                    COALESCE(msldwb.amount, 0) +
                    COALESCE(mscb_team.amount, 0) +
                    COALESCE(mscb_manager.amount, 0)
                )
            ) AS bonuses_overview,
            JSON_OBJECT(
                'turnover_tax_excluded', (
                    COALESCE(mscb_b2c_hifi.turnover_tax_excluded, 0) +
                    COALESCE(mscb_b2c_image.turnover_tax_excluded, 0) +
                    COALESCE(mscb_b2b.turnover_tax_excluded, 0) +
                    COALESCE(msldwb.turnover_tax_excluded, 0)
                ),
                'margin_tax_excluded', (
                    COALESCE(mscb_b2c_hifi.margin_tax_excluded, 0) +
                    COALESCE(mscb_b2c_image.margin_tax_excluded, 0) +
                    COALESCE(mscb_b2b.margin_tax_excluded, 0)
                ),
                'markup_rate', (
                    (
                        COALESCE(mscb_b2c_hifi.margin_tax_excluded, 0) +
                        COALESCE(mscb_b2c_image.margin_tax_excluded, 0) +
                        COALESCE(mscb_b2b.margin_tax_excluded, 0)
                    ) * 100 /
                    (
                        COALESCE(mscb_b2c_hifi.turnover_tax_excluded, 0) +
                        COALESCE(mscb_b2c_image.turnover_tax_excluded, 0) +
                        COALESCE(mscb_b2b.turnover_tax_excluded, 0) +
                        COALESCE(msldwb.turnover_tax_excluded, 0)
                    )
                )
            ) AS global,
            JSON_OBJECT(
                'turnover_tax_excluded', mscrs.turnover_tax_excluded,
                'margin_tax_excluded', mscrs.margin_tax_excluded,
                'turnover_tax_excluded_b2c_commissionable', mscrs.turnover_tax_excluded_b2c_commissionable,
                'margin_tax_excluded_b2c_commissionable', mscrs.margin_tax_excluded_b2c_commissionable
            ) AS retail_store,
            JSON_OBJECT(
                'turnover_tax_excluded', mscrs.turnover_objective,
                'turnover_tax_excluded_rate', (mscrs.turnover_tax_excluded * 100 / mscrs.turnover_objective)
            ) AS goals,
            JSON_OBJECT(
                'turnover_tax_excluded', mscb_b2c_hifi.turnover_tax_excluded,
                'turnover_tax_excluded_commissionable', mscb_b2c_hifi.turnover_tax_excluded_commissionable,
                'margin_tax_excluded', mscb_b2c_hifi.margin_tax_excluded,
                'margin_tax_excluded_commissionable', mscb_b2c_hifi.margin_tax_excluded_commissionable,
                'margin_rate_threshold', mscb_b2c_hifi.margin_rate_threshold
            ) AS b2c_hifi,
            JSON_OBJECT(
                'turnover_tax_excluded', mscb_b2c_image.turnover_tax_excluded,
                'turnover_tax_excluded_commissionable', mscb_b2c_image.turnover_tax_excluded_commissionable,
                'margin_tax_excluded', mscb_b2c_image.margin_tax_excluded,
                'margin_tax_excluded_commissionable', mscb_b2c_image.margin_tax_excluded_commissionable,
                'margin_rate_threshold', mscb_b2c_image.margin_rate_threshold
            ) AS b2c_image,
            JSON_OBJECT(
                'turnover_tax_excluded', mscb_b2b.turnover_tax_excluded,
                'turnover_tax_excluded_commissionable', mscb_b2b.turnover_tax_excluded_commissionable,
                'margin_tax_excluded', mscb_b2b.margin_tax_excluded,
                'margin_tax_excluded_commissionable', mscb_b2b.margin_tax_excluded_commissionable,
                'margin_rate_threshold', mscb_b2b.margin_rate_threshold
            ) AS b2b,
            JSON_OBJECT(
                'quantity_eligible_products', msldwb.quantity_eligible_products,
                'quantity_eligible_products_commissionable', msldwb.quantity_eligible_products_commissionable,
                'quantity_warranty_sold', msldwb.quantity_warranty_sold,
                'quantity_warranty_sold_commissionable', msldwb.quantity_warranty_sold_commissionable,
                'turnover_tax_excluded', msldwb.turnover_tax_excluded,
                'attachment_rate_threshold', msldwb.attachment_rate_threshold,
                'attachment_rate_intermediate_threshold', mscldw.attachment_rate_min_threshold
            ) AS long_duration_warranty,
            -- catch up three months
            JSON_OBJECT(
                'b2c_hifi', mscbcu_b2c_hifi.amount,
                'b2c_image', mscbcu_b2c_image.amount,
                'b2b', mscbcu_b2b.amount,
                'long_duration_warranty', msldwbcu.amount,
                'manager', mscbcu_manager.amount,
                'team', mscbcu_team.amount,
                'total', (
                    COALESCE(mscbcu_b2c_hifi.amount, 0) +
                    COALESCE(mscbcu_b2c_image.amount, 0) +
                    COALESCE(mscbcu_b2b.amount, 0) +
                    COALESCE(msldwbcu.amount, 0) +
                    COALESCE(mscbcu_manager.amount, 0) +
                    COALESCE(mscbcu_team.amount, 0)
                )
            ) AS catch_up_three_months_bonuses_overview,
            JSON_OBJECT(
                'turnover_tax_excluded', (
                    COALESCE(mscbcu_b2c_hifi.turnover_tax_excluded, 0) +
                    COALESCE(mscbcu_b2c_image.turnover_tax_excluded, 0) +
                    COALESCE(mscbcu_b2b.turnover_tax_excluded, 0) +
                    COALESCE(msldwbcu.turnover_tax_excluded, 0)
                ),
                'margin_tax_excluded', (
                    COALESCE(mscbcu_b2c_hifi.margin_tax_excluded, 0) +
                    COALESCE(mscbcu_b2c_image.margin_tax_excluded, 0) +
                    COALESCE(mscbcu_b2b.margin_tax_excluded, 0)
                ),
                'markup_rate', (
                    (
                        COALESCE(mscbcu_b2c_hifi.margin_tax_excluded, 0) +
                        COALESCE(mscbcu_b2c_image.margin_tax_excluded, 0) +
                        COALESCE(mscbcu_b2b.margin_tax_excluded, 0)
                    ) * 100 /
                    (
                        COALESCE(mscbcu_b2c_hifi.turnover_tax_excluded, 0) +
                        COALESCE(mscbcu_b2c_image.turnover_tax_excluded, 0) +
                        COALESCE(mscbcu_b2b.turnover_tax_excluded, 0) +
                        COALESCE(msldwbcu.turnover_tax_excluded, 0)
                    )
                )
            ) AS catch_up_three_months_global,
            JSON_OBJECT(
                'turnover_tax_excluded', mscrscu.turnover_tax_excluded,
                'margin_tax_excluded', mscrscu.margin_tax_excluded,
                'turnover_tax_excluded_b2c_commissionable', mscrscu.turnover_tax_excluded_b2c_commissionable,
                'margin_tax_excluded_b2c_commissionable', mscrscu.margin_tax_excluded_b2c_commissionable
            ) AS catch_up_three_months_retail_store,
            JSON_OBJECT(
                'turnover_tax_excluded', mscrscu.turnover_objective,
                'turnover_tax_excluded_rate', (mscrscu.turnover_tax_excluded * 100 / mscrscu.turnover_objective)
            ) AS catch_up_three_months_goals,
            JSON_OBJECT(
                'turnover_tax_excluded', mscbcu_b2c_hifi.turnover_tax_excluded,
                'turnover_tax_excluded_commissionable', mscbcu_b2c_hifi.turnover_tax_excluded_commissionable,
                'margin_tax_excluded', mscbcu_b2c_hifi.margin_tax_excluded,
                'margin_tax_excluded_commissionable', mscbcu_b2c_hifi.margin_tax_excluded_commissionable,
                'margin_rate_threshold', mscbcu_b2c_hifi.margin_rate_threshold
            ) AS catch_up_three_months_b2c_hifi,
            JSON_OBJECT(
                'turnover_tax_excluded', mscbcu_b2c_image.turnover_tax_excluded,
                'turnover_tax_excluded_commissionable', mscbcu_b2c_image.turnover_tax_excluded_commissionable,
                'margin_tax_excluded', mscbcu_b2c_image.margin_tax_excluded,
                'margin_tax_excluded_commissionable', mscbcu_b2c_image.margin_tax_excluded_commissionable,
                'margin_rate_threshold', mscbcu_b2c_image.margin_rate_threshold
            ) AS catch_up_three_months_b2c_image,
            JSON_OBJECT(
                'turnover_tax_excluded', mscbcu_b2b.turnover_tax_excluded,
                'turnover_tax_excluded_commissionable', mscbcu_b2b.turnover_tax_excluded_commissionable,
                'margin_tax_excluded', mscbcu_b2b.margin_tax_excluded,
                'margin_tax_excluded_commissionable', mscbcu_b2b.margin_tax_excluded_commissionable,
                'margin_rate_threshold', mscbcu_b2b.margin_rate_threshold
            ) AS catch_up_three_months_b2b,
            JSON_OBJECT(
                'quantity_eligible_products', msldwbcu.quantity_eligible_products,
                'quantity_eligible_products_commissionable', msldwbcu.quantity_eligible_products_commissionable,
                'quantity_warranty_sold', msldwbcu.quantity_warranty_sold,
                'quantity_warranty_sold_commissionable', msldwbcu.quantity_warranty_sold_commissionable,
                'turnover_tax_excluded', msldwbcu.turnover_tax_excluded,
                'attachment_rate_threshold', msldwbcu.attachment_rate_threshold,
                'attachment_rate_intermediate_threshold', mscldwcu.attachment_rate_min_threshold
            ) AS catch_up_three_months_long_duration_warranty
        FROM backOffice.sf_guard_user_profile sgup
        INNER JOIN backOffice.sf_guard_user sgu ON sgup.id = sgu.id
        INNER JOIN data_warehouse.monthly_seller_commission_retail_store_user mscrsu ON
            sgup.id = mscrsu.user_id AND
            mscrsu.computed_at = :at
        INNER JOIN backOffice.BO_STK_depot bsd ON mscrsu.retail_store_id = bsd.id
        INNER JOIN data_warehouse.monthly_seller_commission_retail_store mscrs ON
            mscrsu.retail_store_id = mscrs.retail_store_id AND
            mscrs.computed_at = :at
        -- monthly seller commission
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus mscb_b2c_hifi ON
            sgup.id = mscb_b2c_hifi.user_id AND
            mscb_b2c_hifi.computed_at = :at AND
            mscb_b2c_hifi.seller_commission_context_id = 'B2C_HIFI'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus mscb_b2c_image ON
            sgup.id = mscb_b2c_image.user_id AND
            mscb_b2c_image.computed_at = :at AND
            mscb_b2c_image.seller_commission_context_id = 'B2C_IMAGE'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus mscb_b2b ON
            sgup.id = mscb_b2b.user_id AND
            mscb_b2b.computed_at = :at AND
            mscb_b2b.seller_commission_context_id = 'B2B'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus mscb_manager ON
            sgup.id = mscb_manager.user_id AND
            mscb_manager.computed_at = :at AND
            mscb_manager.seller_commission_context_id = 'MANAGER_BONUS'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus mscb_team ON
            sgup.id = mscb_team.user_id AND
            mscb_team.computed_at = :at AND
            mscb_team.seller_commission_context_id = 'TEAM_BONUS'
        LEFT JOIN data_warehouse.monthly_seller_long_duration_warranty_bonus msldwb ON
            sgup.id = msldwb.user_id AND
            msldwb.computed_at = :at
        LEFT JOIN data_warehouse.monthly_seller_commission_long_duration_warranty mscldw ON
            sgup.id = mscldw.user_id AND
            mscldw.computed_at = :at
        -- catch up
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus_catch_up mscbcu_b2c_hifi ON
            sgup.id = mscbcu_b2c_hifi.user_id AND
            mscbcu_b2c_hifi.computed_at = :at AND
            mscbcu_b2c_hifi.seller_commission_context_id = 'B2C_HIFI' AND
            mscbcu_b2c_hifi.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus_catch_up mscbcu_b2c_image ON
            sgup.id = mscbcu_b2c_image.user_id AND
            mscbcu_b2c_image.computed_at = :at AND
            mscbcu_b2c_image.seller_commission_context_id = 'B2C_IMAGE' AND
            mscbcu_b2c_image.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus_catch_up mscbcu_b2b ON
            sgup.id = mscbcu_b2b.user_id AND
            mscbcu_b2b.computed_at = :at AND
            mscbcu_b2b.seller_commission_context_id = 'B2B' AND
            mscbcu_b2b.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus_catch_up mscbcu_manager ON
            sgup.id = mscbcu_manager.user_id AND
            mscbcu_manager.computed_at = :at AND
            mscbcu_manager.seller_commission_context_id = 'MANAGER_BONUS' AND
            mscbcu_manager.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_commission_bonus_catch_up mscbcu_team ON
            sgup.id = mscbcu_team.user_id AND
            mscbcu_team.computed_at = :at AND
            mscbcu_team.seller_commission_context_id = 'TEAM_BONUS' AND
            mscbcu_team.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_long_duration_warranty_bonus_catch_up msldwbcu ON
            sgup.id = msldwbcu.user_id AND
            msldwbcu.computed_at = :at AND
            msldwbcu.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_commission_retail_store_catch_up mscrscu ON
            mscrsu.retail_store_id = mscrscu.retail_store_id AND
            mscrscu.computed_at = :at AND
            mscrscu.catch_up_type = 'three_months'
        LEFT JOIN data_warehouse.monthly_seller_commission_long_duration_warranty_catch_up mscldwcu ON
            sgup.id = mscldwcu.user_id AND
            mscldwcu.computed_at = :at
        WHERE {conditions}
        SQL;

        $sql = strtr($sql, ['{conditions}' => $query_builder->getWhere()]);

        return $this->mapToEntities(
            $this->legacy_pdo->fetchObjects($sql, array_merge(['at' => $at], $query_builder->getWhereParameters())),
            CommissionEntity::class
        );
    }
}
