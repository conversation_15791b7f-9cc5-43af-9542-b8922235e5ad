<?php

namespace SonVideo\Erp\Export\GroupDigital\Manager;

use App\Database\PgDataWarehouse\DataSchema\CustomerOrderModel;
use League\Csv\CannotInsertRecord;
use League\Csv\Exception;
use League\Csv\Reader;
use League\Csv\Writer;
use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Export\GroupDigital\Repository\Mysql\ReferenceGroupDigitalRepository;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;
use SonVideo\Erp\Filesystem\Manager\GroupDigitalFtp;

final class GroupDigitalCAToCsvExporter implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private const EXPORT_HEADERS = [
        'shop' => 'Magasin',
        'date' => 'Date_Vente',
        'reference' => 'Code_Article',
        'ean' => 'Code_Barres',
        'supplier' => 'Code_Fourn',
        'brand' => 'Code_Marque',
        'product_range' => 'Gamme',
        'quantity' => 'Qte',
        'total_net_excl_tax' => 'CA_HT',
        'total_net_incl_tax' => 'CA_TTC',
    ];

    /** @var string[] */
    private const FLOAT_COLUMNS = ['total_net_excl_tax', 'total_net_incl_tax'];

    public const CODE_MAGASIN = '426001';

    private CustomerOrderModel $customer_orders_repository;

    private ReferenceGroupDigitalRepository $reference_group_digital_repository;

    private ExportedFile $exported_file;

    private GroupDigitalFtp $ftp;

    public function __construct(
        CustomerOrderModel $customer_orders_repository,
        ReferenceGroupDigitalRepository $reference_group_digital_repository,
        ExportedFile $exported_file,
        GroupDigitalFtp $group_digital_ftp
    ) {
        $this->customer_orders_repository = $customer_orders_repository;
        $this->reference_group_digital_repository = $reference_group_digital_repository;
        $this->exported_file = $exported_file;
        $this->ftp = $group_digital_ftp;
    }

    /**
     * @throws CannotInsertRecord
     * @throws Exception
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function export(string $from_date): void
    {
        $this->logger->debug('Create CSV structure from records...');

        $group_digital_refs = $this->reference_group_digital_repository->getAllRefsAsArray();

        $stream = tmpfile();
        $writer = Writer::createFromStream($stream)
            ->setOutputBOM(Reader::BOM_UTF8)
            ->setDelimiter(';')
            ->setNewline("\r\n");

        $records = $this->customer_orders_repository->getExportGroupDigitalCAGenerator(self::CODE_MAGASIN, $from_date);

        $this->logger->debug('Create CSV header line...');
        $writer->insertOne(array_values(self::EXPORT_HEADERS));

        $this->logger->debug('Write result in CSV file...');
        foreach ($records as $record) {
            if (!isset($group_digital_refs[$record['ean']])) {
                // Skip lines where EAN is not known by Group Digital
                continue;
            }
            $writer->insertOne($this->formatFields($record, $group_digital_refs));
        }

        $this->logger->notice(sprintf('<comment>Memory usage: %s</comment>', memory_get_usage()));

        $file_name = sprintf('CA_%s_%s.CSV', date('YmdHi'), self::CODE_MAGASIN);
        $this->exported_file->createOrOverwriteStream($file_name, $stream);

        // Send to Group Digital FTP
        $this->ftp->createOrOverwrite(
            $this->ftp::GROUP_DIGITAL_FTP_PATH,
            $file_name,
            $this->exported_file->getFilesystem()->read($file_name)
        );

        $this->logger->notice(sprintf('<comment>Peak memory usage: %s</comment>', memory_get_peak_usage()));
    }

    private function formatFields($record, array $group_digital_refs): array
    {
        // Replace fields 'supplier' and 'brand' with values from $group_digital_refs (Group Digital references for this EAN).
        $record = array_merge($record, $group_digital_refs[$record['ean']]);

        foreach (self::FLOAT_COLUMNS as $fieldname) {
            $record[$fieldname] = number_format($record[$fieldname], 2, '.', '');
        }

        return array_replace(self::EXPORT_HEADERS, $record);
    }
}
