<?php

namespace SonVideo\Erp\AntiFraud\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudCustomerOrder;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudCustomerOrderPayment;

final class AntiFraudWriteRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public function upsertForCustomerOrder(AntiFraudCustomerOrder $anti_fraud_customer_order): void
    {
        $sql = <<<'MYSQL'
        INSERT INTO backOffice.anti_fraud_customer_order (customer_order_id, status, reason)
        VALUES (:customer_order_id, :status, :reason)
            ON DUPLICATE KEY UPDATE status = :status, reason = :reason
        MYSQL;

        $this->legacy_pdo->fetchAffected($sql, $anti_fraud_customer_order->toArray());
    }

    public function upsertForCustomerOrderPayment(
        AntiFraudCustomerOrderPayment $anti_fraud_customer_order_payment
    ): void {
        $sql = <<<'MYSQL'
        INSERT INTO backOffice.anti_fraud_customer_order_payment (customer_order_payment_id, status, reason)
        VALUES (:customer_order_payment_id, :status, :reason)
            ON DUPLICATE KEY UPDATE status = :status, reason = :reason
        MYSQL;

        $this->legacy_pdo->fetchAffected($sql, $anti_fraud_customer_order_payment->toArray());
    }
}
