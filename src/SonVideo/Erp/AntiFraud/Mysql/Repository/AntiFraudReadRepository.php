<?php

namespace SonVideo\Erp\AntiFraud\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudForCustomerOrder\AntiFraudForCustomerOrderMeta;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudForCustomerOrder\AntiFraudForCustomerOrderStatuses;

final class AntiFraudReadRepository implements LegacyPdoAwareInterface, SerializerAwareInterface, LoggerAwareInterface
{
    use LegacyPdoAwareTrait;
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    /** @AntiFraudForCustomerOrderMeta[] */
    public function getRulesInfosForDisplay(array $brands, array $subcategories, array $skus): array
    {
        $sql = <<<'MYSQL'
         SELECT
           'brand' AS type,
           b.id_marque AS id,
           NULL AS sku,
           b.marque COLLATE latin1_general_ci AS name
           FROM backOffice.marque b
           WHERE
             {brands}
         UNION
         SELECT
           'subcategory' AS type,
           sc.id AS id,
           NULL AS sku,
           sc.souscategorie COLLATE latin1_general_ci AS name
           FROM backOffice.CTG_TXN_souscategorie sc
           WHERE
             {subcategories}
         UNION
         SELECT
           'sku' AS type,
           NULL AS id,
           p.reference AS sku,
           a.description_courte COLLATE latin1_general_ci AS name
           FROM backOffice.produit p
           INNER JOIN backOffice.article a ON p.id_produit = a.id_produit
           WHERE
             {skus}
         ;
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchAll(
            strtr($sql, [
                '{brands}' => [] === $brands ? 'FALSE' : 'b.id_marque IN (:brands)',
                '{subcategories}' => [] === $subcategories ? 'FALSE' : 'sc.id IN (:subcategories)',
                '{skus}' => [] === $skus ? 'FALSE' : 'p.reference IN (:skus)',
            ]),
            ['brands' => $brands, 'subcategories' => $subcategories, 'skus' => $skus]
        );

        return $this->serializer->denormalize($result, AntiFraudForCustomerOrderMeta::class . '[]');
    }

    public function findAntiFraudStatusesForCustomerOrder(int $customer_order_id): AntiFraudForCustomerOrderStatuses
    {
        $sql = <<<'MYSQL'
        SELECT
          c.id_commande AS customer_order_id,
          CASE WHEN afco.anti_fraud_id IS NOT NULL
          THEN JSON_OBJECT(
              'status', afco.status,
              'created_at', afco.created_at,
              'reason', afco.reason
           )
          END AS anti_fraud_customer_order,
          CASE WHEN tmp.customer_order_id IS NOT NULL
          THEN JSON_ARRAYAGG(
              JSON_OBJECT(
                'customer_order_payment_id', tmp.customer_order_payment_id,
                'creation_proof', tmp.creation_proof,
                'status', tmp.status,
                'created_at', tmp.created_at,
                'reason', tmp.reason
              )
            )
           ELSE JSON_ARRAY() END AS anti_fraud_customer_order_payments
          FROM
            backOffice.commande c
              LEFT JOIN backOffice.anti_fraud_customer_order afco ON c.id_commande = afco.customer_order_id
              LEFT JOIN (
              SELECT
                pc.id_commande AS customer_order_id,
                pc.creation_justificatif AS creation_proof,
                afcop.*
                FROM
                  backOffice.paiement_commande pc
                    LEFT JOIN backOffice.anti_fraud_customer_order_payment afcop ON pc.id = afcop.customer_order_payment_id
                WHERE
                  pc.id_commande = :customer_order_id
                  AND afcop.anti_fraud_id IS NOT NULL
              ) tmp ON c.id_commande = tmp.customer_order_id
          WHERE
            c.id_commande = :customer_order_id
          GROUP BY c.id_commande
        ;
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchOne($sql, ['customer_order_id' => $customer_order_id]);

        $this->logger->debug('Query result', [
            'results' => $result,
            'lgt' => strlen($result['anti_fraud_customer_order_payments'] ?? 'should not happen'),
            'type' => var_export($result, true),
        ]);

        if (null !== $result['anti_fraud_customer_order']) {
            $result['anti_fraud_customer_order'] = json_decode(
                $result['anti_fraud_customer_order'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
        }

        $result['anti_fraud_customer_order_payments'] = json_decode(
            $result['anti_fraud_customer_order_payments'],
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        return $this->serializer->denormalize($result, AntiFraudForCustomerOrderStatuses::class);
    }
}
