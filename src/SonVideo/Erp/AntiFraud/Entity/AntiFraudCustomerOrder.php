<?php

namespace SonVideo\Erp\AntiFraud\Entity;

final class AntiFraudCustomerOrder
{
    private int $customer_order_id;

    private string $status;

    private AntiFraudFormattedReason $reason;

    public function __construct(int $customer_order_id, string $status, AntiFraudFormattedReason $reason)
    {
        $this->customer_order_id = $customer_order_id;
        $this->status = $status;
        $this->reason = $reason;
    }

    /** @return array{customer_order_id: int, status: string, reason: string} */
    public function toArray(): array
    {
        return [
            'customer_order_id' => $this->customer_order_id,
            'status' => $this->status,
            'reason' => $this->reason->toJson(),
        ];
    }
}
