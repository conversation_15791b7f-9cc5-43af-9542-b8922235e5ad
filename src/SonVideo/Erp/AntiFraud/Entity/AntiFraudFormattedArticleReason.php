<?php

namespace SonVideo\Erp\AntiFraud\Entity;

final class AntiFraudFormattedArticleReason
{
    private string $sku;

    private string $key;

    private array $details;

    public function __construct(string $sku, string $key, array $details = [])
    {
        $this->sku = $sku;
        $this->key = $key;
        $this->details = $details;
    }

    public function withDetails(array $details): self
    {
        return new self($this->sku, $this->key, $details);
    }

    /** @return array{sku: string, key: string, details: mixed[]} */
    public function toArray(): array
    {
        return [
            'sku' => $this->sku,
            'key' => $this->key,
            'details' => $this->details,
        ];
    }
}
