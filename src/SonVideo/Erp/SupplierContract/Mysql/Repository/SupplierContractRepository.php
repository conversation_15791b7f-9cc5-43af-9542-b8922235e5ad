<?php

namespace SonVideo\Erp\SupplierContract\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\SupplierContract\Dto\CreationContext\SupplierContractCreationContextDto;
use SonVideo\Erp\SupplierContract\Dto\UpdateContext\SupplierContractUpdateContext;
use SonVideo\Erp\SupplierContract\Entity\SupplierContractEntity;

final class SupplierContractRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'supplier_id' => 'sc.supplier_id',
        'brand_id' => 'sc.brand_id',
        'year' => 'sc.year',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
                SELECT
                    sc.supplier_contract_id,
                    sc.supplier_id,
                    sc.brand_id,
                    sc.year,
                    sc.discount_description,
                    sc.pam,
                    sc.rfa,
                    sc.additional_rewards,
                    sc.unconditional_discount
                FROM backOffice.supplier_contract sc
                WHERE {conditions}
                ) tmp
            {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->setColumnsMapping(self::COLUMNS_MAPPING)->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            SupplierContractEntity::class
        );
    }

    public function create(SupplierContractCreationContextDto $dto): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.supplier_contract
        (supplier_id, brand_id, year, discount_description, pam, rfa, additional_rewards, unconditional_discount)
        VALUES
            (:supplier_id, :brand_id, :year, :discount_description, :pam, :rfa, :additional_rewards, :unconditional_discount)
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'supplier_id' => $dto->supplier_id,
            'brand_id' => $dto->brand_id,
            'year' => $dto->year,
            'discount_description' => $dto->discount_description,
            'pam' => json_encode($dto->pam),
            'rfa' => json_encode($dto->rfa),
            'additional_rewards' => json_encode($dto->additional_rewards),
            'unconditional_discount' => $dto->unconditional_discount,
        ]);

        return $this->legacy_pdo->lastInsertId();
    }

    /** @throws Exception */
    public function update(SupplierContractUpdateContext $dto): int
    {
        $sql = <<<SQL
        UPDATE backOffice.supplier_contract SET
            discount_description = :discount_description,
            pam = :pam,
            rfa = :rfa,
            additional_rewards = :additional_rewards,
            unconditional_discount = :unconditional_discount
        WHERE supplier_contract_id = :supplier_contract_id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'discount_description' => $dto->discount_description,
            'pam' => json_encode($dto->pam),
            'rfa' => json_encode($dto->rfa),
            'additional_rewards' => json_encode($dto->additional_rewards),
            'supplier_contract_id' => $dto->supplier_contract_id,
            'unconditional_discount' => $dto->unconditional_discount,
        ]);
    }

    /** @throws Exception */
    public function findSupplierContractUnconditionalDiscountAmountIfExistByArticleId(int $article_id): ?int
    {
        $sql = <<<SQL
        SELECT
            sc.unconditional_discount
        FROM backOffice.article a
                 LEFT JOIN backOffice.supplier_contract sc ON sc.supplier_id = a.id_fournisseur
            AND (sc.brand_id = a.id_marque OR sc.brand_id IS NULL)
            AND (sc.year = YEAR(CURDATE()))
                 LEFT JOIN backOffice.supplier_contract sc2 ON sc2.supplier_id = a.id_fournisseur
            AND sc2.brand_id = a.id_marque
            AND (sc2.year = YEAR(CURDATE()))
        WHERE a.id_produit = :article_id
          AND (sc.brand_id IS NOT NULL OR sc2.supplier_contract_id IS NULL);
        SQL;

        $value = $this->legacy_readonly_pdo->fetchValue($sql, ['article_id' => $article_id]);

        return false !== $value ? (int) $value : null;
    }
}
