<?php

namespace SonVideo\Erp\Category\Mysql\Repository;

use App\Exception\InternalErrorException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Category\Dto\CreationContext\CategoryContextDto;
use SonVideo\Erp\Category\Dto\UpdateContext\CategoryContextUpdateDto;
use SonVideo\Erp\Referential\InternalError;

class CategoryRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'category_id' => 'ctc.id_categorie',
        'name' => 'ctc.categorie',
        'domain_id' => 'ctc.dft_domaine_id',
        'custom_code' => 'ctc.code_douanier',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
              SELECT
                  ctc.id_categorie  AS category_id,
                  ctc.categorie     AS name,
                  ctc.code_douanier AS custom_code,
                  ctc.dft_domaine_id AS domain_id
                FROM
                  backOffice.CTG_TXN_categorie ctc
                WHERE {conditions}
                GROUP BY category_id
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function create(CategoryContextDto $dto): int
    {
        try {
            $this->legacy_pdo->beginTransaction();

            $sql = <<<SQL
                INSERT INTO backOffice.CTG_TXN_categorie(categorie, dft_domaine_id)
                VALUES (:category_name, :domain_id)
            SQL;

            $this->legacy_pdo->fetchAffected($sql, [
                'category_name' => $dto->category_name,
                'domain_id' => $dto->domain_id,
            ]);

            $category_id = $this->legacy_pdo->lastInsertId();

            $sql = <<<SQL
                INSERT INTO backOffice.CTG_TXN_categorie_CTG_TXN_domaine(id_domaine, id_categorie)
                VALUES (:domain_id, :category_id)
            SQL;

            $this->legacy_pdo->fetchAffected($sql, [
                'domain_id' => $dto->domain_id,
                'category_id' => $category_id,
            ]);

            $this->legacy_pdo->commit();

            return $category_id;
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }
    }

    /** @throws InternalErrorException */
    public function update(CategoryContextUpdateDto $category): int
    {
        $sql = <<<SQL
        UPDATE backOffice.CTG_TXN_categorie
        SET categorie = :name,
            dft_domaine_id = :domain_id,
            code_douanier = :custom_code
        WHERE id_categorie = :category_id
        SQL;

        try {
            $nb_affected_rows = $this->legacy_pdo->fetchAffected($sql, [
                'category_id' => $category->category_id,
                'name' => $category->category_name,
                'domain_id' => $category->domain_id,
                'custom_code' => $category->custom_code,
            ]);
        } catch (Exception $exception) {
            $message = $exception->getMessage();
            throw new InternalErrorException(InternalError::GENERIC, new Exception($message));
        }

        return $nb_affected_rows;
    }
}
