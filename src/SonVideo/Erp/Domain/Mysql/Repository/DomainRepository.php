<?php

namespace SonVideo\Erp\Domain\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

class DomainRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'domain_id' => 'ctd.id',
        'name' => 'ctd.domaine',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
              SELECT
                  ctd.id        AS domain_id,
                  ctd.domaine   AS name
                FROM
                  backOffice.CTG_TXN_domaine ctd
                WHERE {conditions}
                GROUP BY domain_id
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
