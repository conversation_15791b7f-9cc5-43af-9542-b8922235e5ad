<?php

namespace SonVideo\Erp\CustomerOrderStatus\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use SonVideo\Erp\CustomerOrderStatus\Entity\CustomerOrderStatus;

class CustomerOrderStatusRepository implements LegacyPdoAwareInterface, SerializerAwareInterface
{
    use LegacyPdoAwareTrait;
    use SerializerAwareTrait;

    /** @return CustomerOrderStatus[] */
    public function fetchAll(): array
    {
        $sql = <<<SQL
        SELECT
            cs.status_id,
            cs.priority,
            cs.name,
            cs.description
        FROM backOffice.commande_status cs
        SQL;

        return $this->serializer->denormalize($this->legacy_pdo->fetchAll($sql), CustomerOrderStatus::class . '[]');
    }
}
