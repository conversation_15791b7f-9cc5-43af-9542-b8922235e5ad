<?php

namespace SonVideo\Erp\Wms\Manager;

use App\Exception\NotFoundException;
use SonVideo\Erp\Wms\Mysql\Repository\ParcelRepository;

class ParcelUpdater
{
    private ParcelRepository $repo;

    public function __construct(ParcelRepository $repo)
    {
        $this->repo = $repo;
    }

    /**
     * Update the parcel number for the parcel.
     *
     * @throws NotFoundException
     */
    public function updateParcelNumber(int $parcel_id, string $parcel_number): void
    {
        if (!$this->repo->parcelExist($parcel_id)) {
            throw new NotFoundException(sprintf('Parcel %s not found', $parcel_id));
        }

        $this->repo->updateParcelNumber($parcel_id, $parcel_number);
    }
}
