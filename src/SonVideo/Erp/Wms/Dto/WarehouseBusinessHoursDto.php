<?php

namespace SonVideo\Erp\Wms\Dto;

use Symfony\Component\Validator\Constraints as Assert;

final class WarehouseBusinessHoursDto
{
    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $sun = null;

    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $mon = null;

    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $tue = null;

    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $wed = null;

    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $thu = null;

    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $fri = null;

    /**
     * @Assert\NotNull
     *
     * @var string[]
     */
    public ?array $sat = null;

    /**
     * @return array{sun: string[], mon: string[], tue: string[], wed: string[], thu: string[], fri: string[], sat:
     *                    string[]}
     */
    public function toArray(): array
    {
        return [
            'sun' => $this->sun,
            'mon' => $this->mon,
            'tue' => $this->tue,
            'wed' => $this->wed,
            'thu' => $this->thu,
            'fri' => $this->fri,
            'sat' => $this->sat,
        ];
    }
}
