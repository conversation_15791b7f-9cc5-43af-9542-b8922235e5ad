<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Wms\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Referential\DeliveryTicketLogStatus;
use SonVideo\Erp\Referential\DeliveryTicketWorkflowStatus;

class DeliveryNotePickingWriteRepository extends AbstractLegacyRepository
{
    /**
     * Mark the starting of a picking in database.
     *
     * @throws \Exception
     */
    public function start(int $delivery_note_id, string $username, int $user_id): void
    {
        try {
            $this->legacy_pdo->beginTransaction();

            $sql = <<<SQL
            UPDATE backOffice.bon_livraison
            SET picked_by = :username,
              workflow_status = :status
              WHERE id_bon_livraison = :delivery_note_id;
            SQL;
            $status = DeliveryTicketWorkflowStatus::PICKING_STARTED;
            $result = $this->legacy_pdo->fetchAffected($sql, [
                'username' => $username,
                'delivery_note_id' => $delivery_note_id,
                'status' => $status,
            ]);
            if (0 === $result) {
                throw new \UnexpectedValueException(sprintf('Failed to mark the delivery note %s as picking started.', $delivery_note_id));
            }

            $sql = <<<SQL
                INSERT INTO backOffice.delivery_ticket_activity_log
                SET delivery_ticket_id = :delivery_note_id,
                  action = :action,
                  created_by = :user_id;
            SQL;
            $this->legacy_pdo->fetchAffected($sql, [
                'delivery_note_id' => $delivery_note_id,
                'action' => DeliveryTicketLogStatus::PICKING_STARTED,
                'user_id' => $user_id,
            ]);

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }
    }
}
