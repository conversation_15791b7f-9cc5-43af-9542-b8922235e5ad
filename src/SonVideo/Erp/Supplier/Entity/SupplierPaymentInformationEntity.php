<?php

namespace SonVideo\Erp\Supplier\Entity;

use App\Entity\AbstractEntity;
use Symfony\Component\Validator\Constraints as Assert;

class SupplierPaymentInformationEntity extends AbstractEntity
{
    /**
     * @var int
     * @Assert\NotBlank()
     */
    public $supplier_id;

    /** @var int */
    public $supplier_payment_id;

    /** @var float */
    public $discount_rate;

    /** @var float|null */
    public $franco;

    /** @var string */
    public $delivery_cost;

    /** @var int|null */
    public $discount_payment_deadline;

    /** @var int */
    public $payment_deadline_id;

    /** @var string */
    public $comment;
}
