<?php

namespace SonVideo\Erp\SubCategory\Dto\UpdateContext;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;
use Symfony\Component\Validator\Constraints as Assert;

class SubcategoryContextDto extends AbstractEntity
{
    /** @Assert\NotBlank() */
    public int $subcategory_id;

    /** @Assert\NotBlank() */
    public string $name;

    /** @Assert\NotBlank() */
    public int $parent_category_id;

    public ?int $bbac_subtype_id = null;

    /** @Assert\NotBlank() */
    public ?string $warranty_type = null;

    public bool $outsize;

    /** @Assert\NotBlank() */
    public ?float $charged_delivery = null;

    /** @Assert\NotBlank() */
    public string $subcategory_type;

    /** @var array|JsonType */
    public array $seller_commission_config = [];

    public ?string $custom_code = null;

    public ?string $ecotax_code = null;
}
