<?php

namespace SonVideo\Erp\SubCategory\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Category\Mysql\Repository\CategoryRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\SubCategory\Dto\CreationContext\SubcategoryContextDto;
use SonVideo\Erp\SubCategory\Exception\SubcategoryNameAlreadyExistException;
use SonVideo\Erp\SubCategory\Mysql\Repository\SubcategoryRepository;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SubcategoryCreator implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    private SubcategoryRepository $subcategory_repository;

    private SerializerInterface $serializer;

    private ValidatorInterface $validator;

    private QueryBuilder $query_builder;

    private CategoryRepository $category_repository;

    public function __construct(
        SubcategoryRepository $subcategory_repository,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        QueryBuilder $query_builder,
        CategoryRepository $category_repository
    ) {
        $this->subcategory_repository = $subcategory_repository;
        $this->serializer = $serializer;
        $this->validator = $validator;
        $this->query_builder = $query_builder;
        $this->category_repository = $category_repository;
    }

    /** @throws ExceptionInterface
     * @throws SubcategoryNameAlreadyExistException
     * @throws NotFoundException
     * @throws InternalErrorException
     */
    public function create(int $category_id, string $subcategory_name): int
    {
        $subcategory_name = trim($subcategory_name);

        $does_exist = $this->subcategory_repository->doesSubcategoryNameExist($subcategory_name);

        if ($does_exist) {
            throw new SubcategoryNameAlreadyExistException('Subcategory name already exists');
        }

        $this->query_builder->setWhere(['category_id' => ['_eq' => $category_id]], CategoryRepository::COLUMNS_MAPPING);

        $category = $this->category_repository->findAllPaginated($this->query_builder)->getResults();

        if ([] === $category) {
            throw new NotFoundException(sprintf('No category found with id %s', $category_id));
        }
        /** @var SubcategoryContextDto $dto */
        $dto = $this->serializer->denormalize(
            [
                'category_id' => $category_id,
                'subcategory_name' => $subcategory_name,
                'domain_id' => $category[0]->domain_id,
            ],
            SubcategoryContextDto::class
        );
        $errors = $this->validator->validate($dto);

        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }

        return $this->subcategory_repository->create($dto);
    }
}
