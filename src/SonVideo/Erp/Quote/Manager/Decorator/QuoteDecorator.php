<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Manager\Decorator;

use App\Exception\SqlErrorMessageException;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\Mailing\Manager\HeraldMailjetUtility;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Entity\QuoteLineEntity;
use SonVideo\Erp\Referential\QuoteLineProductType;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;

final class QuoteDecorator
{
    private float $total_discount_tax_excluded = 0.0;
    private float $total_discount_tax_included = 0.0;
    private float $total_price_tax_excluded = 0.0;
    private float $total_vat = 0.0;
    private float $total_price_tax_included = 0.0;
    private $computed_vat_rate = 0.0;

    private ArticleReadRepository $article_read_repository;

    private CustomerRepository $customer_repository;

    public function __construct(ArticleReadRepository $article_read_repository, CustomerRepository $customer_repository)
    {
        $this->article_read_repository = $article_read_repository;
        $this->customer_repository = $customer_repository;
    }

    /** @throws \Exception */
    public function decorate(QuoteEntity $entity): array
    {
        $this->init();
        $quote = $entity->toArray();
        $quote['message'] ??= '';

        $this->computed_vat_rate = 1 + $quote['vat_rate'];

        // products + warranties decoration
        $quote['articles_count'] = 0;
        $new_lines = [];
        foreach ($quote['quote_line_aggregates'] ?? [] as $quote_line) {
            if (QuoteLineEntity::TYPE_PRODUCT !== $quote_line['type']) {
                $new_lines[] = $quote_line;
                continue;
            }

            $new_lines[] = $this->decorateProduct($quote_line);
            foreach ($this->extractWarrantyLines($quote_line) as $warranty_line) {
                $new_lines[] = $warranty_line;
            }
            ++$quote['articles_count'];
        }
        $quote['quote_line_aggregates'] = $new_lines;

        // shipment decoration
        $has_shipment_cost = QuoteEntity::TYPE_QUOTATION === $quote['type'] && isset($quote['shipment_method']['cost']);
        $shipment_cost = ($has_shipment_cost ? $quote['shipment_method']['cost'] : 0.0) ?? 0.0;
        $quote['shipment_method']['cost'] = $shipment_cost;
        $quote['shipment_method']['cost_tax_excluded'] = $shipment_cost / $this->computed_vat_rate;
        $quote['shipment_method']['cost_formatted'] = '-';
        $quote['shipment_method']['cost_tax_excluded_formatted'] = '-';
        if ($has_shipment_cost && 0.0 === $shipment_cost) {
            $quote['shipment_method']['cost_formatted'] = 'Offert';
            $quote['shipment_method']['cost_tax_excluded_formatted'] = 'Offert';
        }
        if ($has_shipment_cost && 0.0 !== $shipment_cost) {
            $quote['shipment_method']['cost_formatted'] = HeraldMailjetUtility::formatToCurrency($shipment_cost);
            $quote['shipment_method']['cost_tax_excluded_formatted'] = HeraldMailjetUtility::formatToCurrency(
                $quote['shipment_method']['cost_tax_excluded']
            );
        }

        // prices decoration
        $quote['prices'] = [
            'computed_vat_rate' => $this->computed_vat_rate,
            'total_discount_tax_excluded' => $this->total_discount_tax_excluded,
            'total_discount_tax_included' => $this->total_discount_tax_included,
            'total_discount_tax_included_formatted' => HeraldMailjetUtility::formatToCurrency(
                $this->total_discount_tax_included
            ),
            'total_price_tax_excluded' => $this->total_price_tax_excluded + $quote['shipment_method']['cost_tax_excluded'],
            'total_vat' => $this->total_vat + ($shipment_cost - $quote['shipment_method']['cost_tax_excluded']),
            'total_vat_formatted' => HeraldMailjetUtility::formatToCurrency(
                $this->total_vat + ($shipment_cost - $quote['shipment_method']['cost_tax_excluded'])
            ),
            'total_price_tax_included' => $this->total_price_tax_included + $shipment_cost,
            'total_price_tax_included_formatted' => HeraldMailjetUtility::formatToCurrency(
                $this->total_price_tax_included + $shipment_cost
            ),
        ];

        // firstname and lastname used for quote offer email
        if (QuoteEntity::TYPE_OFFER === $quote['type']) {
            $customer = $this->customer_repository->getById($quote['customer_id']);
            $quote['customer'] = [
                'firstname' => $customer->get('cnt_prenom'),
                'lastname' => $customer->get('cnt_nom'),
            ];
        }

        // dates decoration
        $quote['created_at_formatted'] = $entity->created_at->format('d/m/Y H:i');
        $quote['expired_at_formatted'] =
            $entity->expired_at instanceof \DateTimeInterface ? $entity->expired_at->format('d/m/Y H:i:s') : '-';

        return $quote;
    }

    /** @throws SqlErrorMessageException */
    private function decorateProduct(array $quote_line_product): array
    {
        $data = [];
        $product = $quote_line_product['data']['product'];

        $data['selling_price_tax_excluded'] = $product['selling_price_tax_included'] / $this->computed_vat_rate;

        // The discount amount should not be negative
        $data['unit_discount_amount_abs_tax_included'] = abs($quote_line_product['data']['unit_discount_amount']);
        $data['unit_discount_amount_abs_tax_excluded'] =
            $data['unit_discount_amount_abs_tax_included'] / $this->computed_vat_rate;

        $total_price_with_discount_applied =
            $data['selling_price_tax_excluded'] - $data['unit_discount_amount_abs_tax_excluded'];
        $data['total_price_tax_excluded'] =
            $total_price_with_discount_applied * $quote_line_product['data']['quantity'];

        $total_price_with_discount_applied =
            $product['selling_price_tax_included'] - $data['unit_discount_amount_abs_tax_included'];
        $data['total_price_tax_included'] =
            $total_price_with_discount_applied * $quote_line_product['data']['quantity'];

        $data['selling_price_tax_included'] = $product['selling_price_tax_included'];
        $data['selling_price_tax_included_formatted'] = HeraldMailjetUtility::formatToCurrency(
            $product['selling_price_tax_included']
        );
        $data['total_discount_amount'] =
            $quote_line_product['data']['quantity'] * $data['unit_discount_amount_abs_tax_included'];
        $data['total_discount_amount_formatted'] = HeraldMailjetUtility::formatToCurrency(
            $data['total_discount_amount']
        );

        $data['total_price'] =
            $quote_line_product['data']['quantity'] * $product['selling_price_tax_included'] -
            $data['total_discount_amount'];
        $data['total_price_formatted'] = HeraldMailjetUtility::formatToCurrency($data['total_price']);
        $data['url'] = $this->article_read_repository->findUrlById($product['product_id']);

        // External apps don't need to differentiate article from destock
        $quote_line_product['data']['product']['type'] =
            QuoteLineProductType::DESTOCK === $product['type'] ? QuoteLineProductType::ARTICLE : $product['type'];

        $quote_line_product['data'] = array_merge($quote_line_product['data'], $data);

        $this->collectProductPrices($quote_line_product['data']);

        return $quote_line_product;
    }

    private function extractWarrantyLines(array $quote_line_product): array
    {
        $selected_warranties = $quote_line_product['data']['selected_warranties'] ?? [];

        return array_map(function (array $warranty) use ($quote_line_product): array {
            $data = $warranty;
            $data['quantity'] = $quote_line_product['data']['quantity'];
            $data['price_tax_included_formatted'] = HeraldMailjetUtility::formatToCurrency(
                $data['unit_selling_price_tax_included']
            );
            $data['price_tax_excluded'] = $warranty['unit_selling_price_tax_included'] / $this->computed_vat_rate;
            $data['price_tax_excluded_formatted'] = HeraldMailjetUtility::formatToCurrency($data['price_tax_excluded']);
            $data['total_price_tax_included'] = $warranty['unit_selling_price_tax_included'] * $data['quantity'];
            $data['total_price_tax_included_formatted'] = HeraldMailjetUtility::formatToCurrency(
                $data['total_price_tax_included']
            );
            $data['total_price_tax_excluded'] = $data['total_price_tax_included'] / $this->computed_vat_rate;
            $data['total_price_tax_excluded_formatted'] = HeraldMailjetUtility::formatToCurrency(
                $data['total_price_tax_excluded']
            );

            $this->collectWarrantyPrices($data);

            return [
                'type' => QuoteLineEntity::TYPE_PRODUCT_WARRANTY,
                'data' => $data,
                'display_order' => $quote_line_product['display_order'],
            ];
        }, $selected_warranties);
    }

    private function collectProductPrices(array $data): void
    {
        $this->total_discount_tax_excluded += $data['unit_discount_amount_abs_tax_excluded'] * $data['quantity'];
        $this->total_discount_tax_included += $data['unit_discount_amount_abs_tax_included'] * $data['quantity'];
        $this->total_price_tax_excluded += $data['total_price_tax_excluded'];
        $this->total_price_tax_included += $data['total_price_tax_included'];
        $this->total_vat = $this->total_price_tax_included - $this->total_price_tax_excluded;
    }

    private function collectWarrantyPrices(array $data): void
    {
        $this->total_price_tax_excluded += $data['total_price_tax_excluded'];
        $this->total_price_tax_included += $data['total_price_tax_included'];
        $this->total_vat = $this->total_price_tax_included - $this->total_price_tax_excluded;
    }

    private function init(): void
    {
        $this->total_discount_tax_excluded = 0.0;
        $this->total_discount_tax_included = 0.0;
        $this->total_price_tax_excluded = 0.0;
        $this->total_vat = 0.0;
        $this->total_price_tax_included = 0.0;
        $this->computed_vat_rate = 0.0;
    }
}
