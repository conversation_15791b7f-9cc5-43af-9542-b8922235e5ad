<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Manager;

use App\Contract\DataLoaderAwareInterface;
use App\DataLoader\Type\JsonType;
use App\Exception\NotFoundException;
use App\Formatter\String\StringFormatter;
use App\Sql\LegacyPdo;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use SonVideo\Erp\Quote\Contract\QuoteHelperTrait;
use SonVideo\Erp\Quote\Contract\QuoteLineProductCreateTrait;
use SonVideo\Erp\Quote\Contract\QuoteRepositoryAwareInterface;
use SonVideo\Erp\Quote\Contract\QuoteRepositoryAwareTrait;
use SonVideo\Erp\Quote\Contract\QuoteRetrieverTrait;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Entity\QuoteLineEntity;
use SonVideo\Erp\Quote\Entity\QuoteSubtypeEntity;
use SonVideo\Erp\Quote\Exception\QuoteValidationException;
use SonVideo\Erp\Quote\Manager\Validation\AddressValidation;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use SonVideo\Erp\User\Entity\UserEntity;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Validator\Validation;

class QuoteManager implements QuoteRepositoryAwareInterface, DataLoaderAwareInterface
{
    use QuoteRepositoryAwareTrait;
    use QuoteHelperTrait;
    use QuoteRetrieverTrait;
    use QuoteLineProductCreateTrait;

    private LegacyPdo $legacy_pdo;

    private SystemEventLogger $system_event_logger;

    private ShipmentMethodReadRepository $shipment_method_read_repository;

    protected QuoteShippingEligibilityManager $quote_shipping_eligibility_manager;

    private CurrentUser $current_user;

    public function __construct(
        LegacyPdo $legacy_pdo,
        SystemEventLogger $system_event_logger,
        ShipmentMethodReadRepository $shipment_method_read_repository,
        CurrentUser $current_user,
        QuoteShippingEligibilityManager $quote_shipping_eligibility_manager
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->system_event_logger = $system_event_logger;
        $this->shipment_method_read_repository = $shipment_method_read_repository;
        $this->current_user = $current_user;
        $this->quote_shipping_eligibility_manager = $quote_shipping_eligibility_manager;
    }

    /**
     * Update a quote.
     *
     * @throws NotFoundException
     * @throws \Exception
     */
    public function update(int $quote_id, array $data, UserEntity $user): int
    {
        if ([] === $data) {
            throw new \UnexpectedValueException('No data supplied for update');
        }

        $quote = $this->retrieveFullyHydratedQuote($quote_id, $this->quote_repository);

        $this->checkIsNotLocked($quote);
        $this->checkAuthorizedColumns($data);
        if (isset($data['created_by']) && $data['created_by'] !== $quote->created_by) {
            $this->checkCreatorChange($quote, $user);
        }
        if (isset($data['shipment_method'])) {
            $this->checkShipmentChange($quote, $data['shipment_method']);
        }
        if (isset($data['quote_subtype'])) {
            $this->checkSubtypeChange($data['quote_subtype'], $quote->quote_subtype);
        }

        // Reset shipment method after updating shipping address
        if (
            $quote->shipment_method instanceof JsonType &&
            $quote->shipping_address instanceof JsonType &&
            isset($data['shipping_address'])
        ) {
            $data['shipment_method'] = null;
        }

        // If shipping address is a retail store, set shipment method
        if (isset($data['shipping_address']['shipment_method_id'])) {
            $data['shipment_method'] = $this->shipment_method_read_repository
                ->getQuoteShipmentMethod($data['shipping_address']['shipment_method_id'])
                ->toArray();
        }

        $data = $this->transliterateData($data);
        $this->validateAddresses($data);

        try {
            $this->legacy_pdo->beginTransaction();

            $updated = $this->quote_repository->update($quote_id, $data, $user->get('id_utilisateur'));

            // The action log is handled via mysql trigger

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $updated;
    }

    private function checkAuthorizedColumns(array $data): void
    {
        $columns_authorized_for_update = [
            'type',
            'created_by',
            'quote_subtype',
            'expired_at',
            'valid_until',
            'message',
            'billing_address',
            'shipping_address',
            'shipment_method',
        ];

        $not_authorized_columns = array_keys(array_diff_key($data, array_flip($columns_authorized_for_update)));

        if ([] !== $not_authorized_columns) {
            throw new \UnexpectedValueException(sprintf('Invalid column(s): [%s]. Authorized columns are: [%s]', implode(', ', $not_authorized_columns), implode(', ', $columns_authorized_for_update)));
        }
    }

    private function checkCreatorChange(QuoteEntity $quote, UserEntity $user): void
    {
        if ('sent' === $quote->status) {
            throw new \UnexpectedValueException('The creator of a sent quote cannot be changed');
        }

        if ($quote->created_by !== $user->id_utilisateur) {
            throw new \UnexpectedValueException(sprintf('The user %d cannot change the creator of the quote %d which belong to user %d', $user->id_utilisateur, $quote->quote_id, $quote->created_by));
        }
    }

    /** @throws NotFoundException */
    private function checkShipmentChange(QuoteEntity $quote, array $new_shipment_method): void
    {
        if ($this->userCanCustomizeShipmentMethod()) {
            return;
        }

        $allowed_shipment_methods = $this->quote_shipping_eligibility_manager->retrieveEligibleShipmentMethods(
            $quote->quote_id
        );
        $matching_shipments = array_filter(
            $allowed_shipment_methods,
            function (array $shipment_method) use ($new_shipment_method): bool {
                if ($shipment_method['shipment_method_id'] !== $new_shipment_method['shipment_method_id']) {
                    return false;
                }

                return !(
                    abs($shipment_method['cost'] - $new_shipment_method['cost']) > 0.00001 &&
                    !isset($new_shipment_method['initial_cost'])
                );
            },
            ARRAY_FILTER_USE_BOTH
        );

        if (1 === count($matching_shipments)) {
            return;
        }

        throw new \UnexpectedValueException('You are not allowed choose this shipment method');
    }

    private function userCanCustomizeShipmentMethod(): bool
    {
        return $this->current_user->hasPermissions([UserPermission::QUOTE_SHIPMENT_METHOD_CUSTOMIZE]);
    }

    /**
     * Check if an user is allowed to change a quote subtype.
     *
     * @throws \UnexpectedValueException
     */
    private function checkSubtypeChange(string $new_subtype, string $old_subtype = null): void
    {
        if ($this->checkAllowedSubtype($old_subtype) && $this->checkAllowedSubtype($new_subtype)) {
            return;
        }

        throw new \UnexpectedValueException('You are not allowed to change the quote\'s subtype');
    }

    /** Check if an user is allowed on a quote subtype */
    private function checkAllowedSubtype(string $subtype = null): bool
    {
        // emulate QUOTE_SUBTYPE_CLASSIQUE_SELECT
        if (QuoteSubtypeEntity::CLASSIQUE === $subtype || null === $subtype) {
            return true;
        }

        // permission have a naming convention QUOTE_SUBTYPE_[:SUBTYPE_KEY]_SELECT
        $const = UserPermission::class . '::QUOTE_SUBTYPE_' . $subtype . '_SELECT';

        return defined($const) && $this->current_user->hasPermissions([constant($const)]);
    }

    /**
     * Delete a line.
     *
     * @throws NotFoundException
     * @throws \Exception
     */
    public function deleteLine(int $quote_id, int $quote_line_id, UserEntity $user): void
    {
        $quote = $this->retrieveFullyHydratedQuote($quote_id, $this->quote_repository);
        $this->checkIsNotLocked($quote);
        $quote_line = $this->quote_line_repository->findByWithType($quote_line_id);

        if (!$quote_line) {
            throw new \Exception(sprintf('Quote line not found with id "%s"', $quote_line_id));
        }

        if ($quote_id !== (int) $quote_line['quote_id']) {
            throw new \Exception('Mismatch between quote and quote line');
        }

        switch ($quote_line['quote_line_type']) {
            case QuoteLineEntity::TYPE_SECTION:
                $quote_line_info = $this->quote_line_section_repository->findById($quote_line['quote_line_child_id']);
                break;
            case QuoteLineEntity::TYPE_PRODUCT:
                $quote_line_info = $this->quote_line_product_repository->findById($quote_line['quote_line_child_id']);
                break;
            default:
                throw new NotFoundException('Quote line child not found');
        }

        try {
            $this->legacy_pdo->beginTransaction();

            $this->quote_line_repository->delete($quote_line);

            if (!$this->shouldKeepShipmentMethod($quote)) {
                $this->quote_repository->removeShipmentMethod($quote_id, $user->id_utilisateur);
            }

            $this->system_event_logger->log(
                new LoggableSystemEvent(LoggableSystemEvent::SYSTEM_ERP_MYSQL, 'quote_line.delete', [
                    '_rel' => [
                        'quote' => $quote_id,
                    ],
                    'data' => [
                        'deleted' => $quote_line_info,
                    ],
                    'meta' => [
                        'deleted_by' => [
                            'user_id' => $user->id_utilisateur,
                            'username' => $user->utilisateur,
                            'firstname' => $user->prenom,
                            'lastname' => $user->nom,
                        ],
                    ],
                ])
            );

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }
    }

    private function transliterateData(array $data): array
    {
        return $this->transliterate($data, [
            'message',
            'shipping_address' => ['cellphone', 'phone'],
            'billing_address' => ['cellphone', 'phone'],
        ]);
    }

    private function transliterate($data, array $property_to_transliterate): array
    {
        foreach ($property_to_transliterate as $key => $property) {
            if (is_array($property) && isset($data[$key])) {
                $data[$key] = $this->transliterate($data[$key], $property);

                continue;
            }

            if (is_array($property) || !isset($data[$property])) {
                continue;
            }

            $data[$property] = StringFormatter::transliterate($data[$property]);
        }

        return $data;
    }

    /** @throws QuoteValidationException */
    private function validateAddresses(array $data): void
    {
        if (isset($data['shipping_address'])) {
            $errors = Validation::createValidator()->validate($data['shipping_address'], AddressValidation::rules());
            if (count($errors) > 0) {
                throw new QuoteValidationException(sprintf('Shipping address is invalid : %s', ConstraintMessageFormatter::prettify($errors)));
            }
        }

        if (isset($data['billing_address'])) {
            $errors = Validation::createValidator()->validate($data['billing_address'], AddressValidation::rules());
            if (count($errors) > 0) {
                throw new QuoteValidationException(sprintf('Billing address is invalid : %s', ConstraintMessageFormatter::prettify($errors)));
            }
        }
    }
}
