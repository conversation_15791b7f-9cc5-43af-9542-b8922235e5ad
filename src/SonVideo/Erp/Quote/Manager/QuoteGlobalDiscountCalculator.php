<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Manager;

use App\Contract\DataLoaderAwareInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use SonVideo\Erp\Quote\Contract\QuoteHelperTrait;
use SonVideo\Erp\Quote\Contract\QuoteLineProductPricesTrait;
use SonVideo\Erp\Quote\Contract\QuoteRepositoryAwareInterface;
use SonVideo\Erp\Quote\Contract\QuoteRepositoryAwareTrait;
use SonVideo\Erp\Quote\Contract\QuoteRetrieverTrait;
use SonVideo\Erp\Quote\Entity\QuoteLineEntity;
use SonVideo\Erp\Quote\Entity\UpdatableQuoteLineProductEntity;
use SonVideo\Erp\Quote\Manager\Decorator\QuoteLineProductDecorator;
use SonVideo\Erp\User\Entity\UserEntity;

final class QuoteGlobalDiscountCalculator implements QuoteRepositoryAwareInterface, DataLoaderAwareInterface
{
    use QuoteRepositoryAwareTrait;
    use QuoteHelperTrait;
    use QuoteRetrieverTrait;
    use QuoteLineProductPricesTrait;

    private QuoteLineProductDecorator $decorator;

    public function __construct(QuoteLineProductDecorator $decorator)
    {
        $this->decorator = $decorator;
    }

    /**
     * @return UpdatableQuoteLineProductEntity[]
     *
     * @throws InternalErrorException
     * @throws NotFoundException
     */
    public function calculateWith(
        int $quote_id,
        UserEntity $user,
        bool $has_discount_admin_permission,
        float $discount_amount
    ): array {
        $quote = $this->retrieveFullyHydratedQuote($quote_id, $this->quote_repository);

        $this->checkIsNotLocked($quote);

        // Keeps only products
        $product_quote_lines = [];

        /** @var QuoteLineEntity $quote_line */
        foreach ($quote->quote_line_aggregates as $quote_line) {
            if ('product' !== $quote_line->type) {
                continue;
            }

            $product_quote_lines[] = $this->decorator->getEntityWithPrices(
                array_merge($quote_line->data, [
                    'quote_id' => $quote_id,
                    'quote_line_id' => $quote_line->quote_line_id,
                ]),
                UpdatableQuoteLineProductEntity::class
            );
        }

        return $this->allocateAProratedDiscount($product_quote_lines, $discount_amount, $has_discount_admin_permission);
    }
}
