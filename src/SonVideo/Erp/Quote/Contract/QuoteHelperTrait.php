<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Contract;

use App\Exception\InternalErrorException;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\HalMiddlewareBundle\Application\Gateway\Owner\OwnerGatewayInterface;
use SonVideo\HalMiddlewareBundle\Domain\Permission\Owner;

trait QuoteHelperTrait
{
    /**
     * @return $this
     *
     * @throws \Exception
     */
    protected function checkIsNotLocked(QuoteEntity $quote): self
    {
        if ($quote->isLocked()) {
            throw new InternalErrorException(InternalError::QUOTE_IS_LOCKED, new \Exception('Quote is locked'));
        }

        return $this;
    }

    protected function shouldKeepShipmentMethod(QuoteEntity $quote): bool
    {
        return $quote->shipment_method && $quote->shipment_method['is_retail_store'];
    }

    public function getDefaultQuoteSubtype(OwnerGatewayInterface $gateway, string $username): ?string
    {
        $account = $gateway->findAccountByUsername($username);

        if (!$account instanceof Owner) {
            return null;
        }

        return $account->getMeta()['preferences']['erp']['force_selection_quote_subtype'] ?? false ? null : 'CLASSIQUE';
    }
}
