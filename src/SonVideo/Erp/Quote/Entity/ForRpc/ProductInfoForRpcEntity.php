<?php
/*
 * This file is part of erp package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Entity\ForRpc;

use App\Entity\AbstractEntity;

class ProductInfoForRpcEntity extends AbstractEntity
{
    public int $product_id;

    public string $sku;

    public string $description;

    public string $short_description;

    public ?string $image = null;

    public float $selling_price_tax_included;

    public float $ecotax_price;

    public float $sorecop_price;

    public float $vat;

    public string $type;
}
