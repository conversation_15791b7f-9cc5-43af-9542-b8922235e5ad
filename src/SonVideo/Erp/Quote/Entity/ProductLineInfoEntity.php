<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Entity;

use App\Entity\AbstractEntity;

class ProductLineInfoEntity extends AbstractEntity
{
    public int $quantity;

    public float $unit_discount_amount;

    public float $margin;

    public ?float $selling_price_tax_excluded = null;

    public float $selling_price_with_discount;

    public float $margin_with_discount;

    public float $margin_rate_with_discount;

    public ProductInfoEntity $product;

    public float $selling_price_generally_observed;

    public string $status;

    /** @var ProductInfoSelectedWarrantyEntity[] */
    public array $selected_warranties = [];
}
