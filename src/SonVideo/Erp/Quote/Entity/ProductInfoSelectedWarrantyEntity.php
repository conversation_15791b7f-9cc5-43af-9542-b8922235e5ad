<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Entity;

use App\Adapter\Serializer\Type\JsonDenormalizableInterface;
use App\Entity\AbstractEntity;

class ProductInfoSelectedWarrantyEntity extends AbstractEntity implements JsonDenormalizableInterface
{
    public const TYPE_THEFT_BREAKDOWN = 'theft_breakdown';
    public const TYPE_EXTENSION = 'extension';

    public string $type;

    public string $label;

    public float $unit_selling_price_tax_included;

    public int $duration;
}
