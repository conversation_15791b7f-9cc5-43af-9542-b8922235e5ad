<?php

namespace SonVideo\Erp\Quote\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

class QuoteSubtypeRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'type' => 'qs.type',
        'label' => 'qs.label',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                qs.type AS type,
                qs.label AS label
              FROM backOffice.quote_subtype qs
                WHERE {where}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
