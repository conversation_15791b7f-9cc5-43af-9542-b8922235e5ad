<?php

namespace SonVideo\Erp\PromoOffer\Manager;

use App\Adapter\Serializer\SerializerInterface;
use Son<PERSON>ideo\Erp\PromoOffer\Entity\ActiveMarketingOperation;
use SonVideo\Erp\PromoOffer\Gateway\PromoOfferReadGateway;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class PromoOfferManager
{
    private PromoOfferReadGateway $gateway;

    private SerializerInterface $serializer;

    public function __construct(PromoOfferReadGateway $gateway, SerializerInterface $serializer)
    {
        $this->gateway = $gateway;
        $this->serializer = $serializer;
    }

    /** @return ActiveMarketingOperation[]
     * @throws ExceptionInterface
     */
    public function getActiveMarketingOperations(\DateTime $date = null): array
    {
        if (!$date instanceof \DateTime) {
            $date = new \DateTime();
        }

        $data = $this->gateway->find(
            [
                'start_at' => ['_lte' => $date->format('Y-m-d')],
                'end_at' => ['_gte' => $date->format('Y-m-d')],
                'definition' => ['_has_key' => 'marketing_operation'],
                'deactivated_reason' => ['_is_null' => true],
                'computed_article_ids' => ['_neq' => []],
            ],
            array_keys(get_class_vars(ActiveMarketingOperation::class))
        );

        return $this->serializer->denormalize(
            $data['data']['promo_offer_promo_offer'],
            ActiveMarketingOperation::class . '[]'
        );
    }
}
