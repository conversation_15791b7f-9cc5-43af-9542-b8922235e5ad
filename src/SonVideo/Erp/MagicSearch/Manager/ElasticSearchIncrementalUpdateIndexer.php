<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use Elasticsearch\Client as ElasticsearchClient;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchIndexDataProviderInterface;
use SonVideo\Erp\Referential\MagicSearch;
use SonVideo\Erp\System\Manager\SystemDatetimeRangeUpdater;
use Symfony\Component\Console\Output\OutputInterface;

final class ElasticSearchIncrementalUpdateIndexer implements LoggerAwareInterface, SerializerAwareInterface
{
    use LoggerAwareTrait;
    use SerializerAwareTrait;

    public const FEEDBACK_MASK = [
        'collected' => 0,
        'indexed' => 0,
        'errors' => 0,
        'finished' => 'FALSE',
    ];

    protected SystemDatetimeRangeUpdater $system_datetime_range_updater;

    private ElasticsearchClient $client;

    private ?IndexationFeebackManager $feedback_manager = null;

    public function __construct(ElasticsearchClient $client, SystemDatetimeRangeUpdater $system_datetime_range_updater)
    {
        $this->client = $client;
        $this->system_datetime_range_updater = $system_datetime_range_updater;
    }

    /** @throws \Exception */
    public function generate(OutputInterface $output, array $data_providers): array
    {
        $this->system_datetime_range_updater->setFromLastStopToNow(
            MagicSearch::SYSTEM_VARIABLE_START_AT,
            MagicSearch::SYSTEM_VARIABLE_STOP_AT
        );

        $this->feedback_manager = new IndexationFeebackManager($output, [
            'Entity',
            'Collected',
            'Indexed',
            'Error(s)',
            'Finished',
        ]);

        foreach ($data_providers as $data_provider) {
            // Prevent hard failures, the next index should run regardless of the current one result
            try {
                $this->feedback_manager->addRow($data_provider->getName(), self::FEEDBACK_MASK);

                $this->index($data_provider);
            } catch (\Exception $exception) {
                $this->logger->error($exception->getMessage(), ['exception' => $exception]);
            }
        }

        return $this->feedback_manager->refresh()->dump();
    }

    /** @return $this */
    protected function index(MagicSearchIndexDataProviderInterface $elk_data_provider): self
    {
        // Fetch entities that we want to index (use a generator for memory usage)
        $this->logger->debug(sprintf('Indexing %s in buffer index...', $elk_data_provider->getName()));

        foreach ($this->feedback_manager->startProgress($elk_data_provider->fetchGenerator()) as $raw_item) {
            // For feedback
            $this->feedback_manager->increment('collected');

            // Map data in entity to have proper field types
            $item = $this->serializer->normalize(
                $this->serializer->denormalize($raw_item, $elk_data_provider->getEntityClassName())
            );
            $response = $this->client->index([
                'index' => $elk_data_provider->getMainIndexName(),
                'id' => $item[$elk_data_provider->getIdKey()],
                'body' => $item,
            ]);

            $this->feedback_manager->increment('indexed');

            // Error management
            if (isset($response['errors']) && $response['errors']) {
                foreach ($response['items'] as $item) {
                    if (isset($item['index']['error'])) {
                        // For sentry
                        $this->logger->warning('ELK incremental update failed', $item['index']);

                        // For feedback
                        $this->feedback_manager->increment('errors')->decrement('indexed');
                    }
                }
            }

            // feedback in console
            $this->feedback_manager->refresh();
        }

        // ensures that the progress bar is at 100% + update Finished column
        $this->feedback_manager->finishProgress()->setArbitraryValue('finished', 'TRUE');

        return $this;
    }
}
