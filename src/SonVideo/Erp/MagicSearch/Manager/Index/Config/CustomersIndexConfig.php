<?php

namespace SonVideo\Erp\MagicSearch\Manager\Index\Config;

use SonVideo\Erp\Referential\MagicSearch;

final class CustomersIndexConfig
{
    public const INDEX_CONFIG = [
        'settings' => [
            'number_of_shards' => 1,
            'number_of_replicas' => 0,
            'max_ngram_diff' => 50,
            'analysis' => [
                'char_filter' => [
                    'digits_only' => [
                        'type' => 'pattern_replace',
                        'pattern' => '[^\\d]',
                    ],
                    'digits_and_spaces' => [
                        'type' => 'pattern_replace',
                        'pattern' => '[^\\d\\s]',
                    ],
                ],
                'filter' => [
                    'custom_word_delimiter' => [
                        'type' => 'word_delimiter_graph',
                        'catenate_all' => true,
                        'adjust_offsets' => false,
                    ],
                    'ngram_filter' => [
                        'type' => 'edge_ngram',
                        'min_gram' => 1,
                        'max_gram' => 20,
                        'token_chars' => ['letter', 'digit'],
                    ],
                    'not_empty' => [
                        'type' => 'length',
                        'min' => 1,
                    ],
                ],
                'analyzer' => [
                    'french_analyzer' => [
                        'type' => 'custom',
                        'tokenizer' => 'standard',
                        'filter' => ['lowercase', 'custom_word_delimiter'],
                    ],
                    'french_analyzer_ngram' => [
                        'type' => 'custom',
                        'tokenizer' => 'standard',
                        'filter' => ['lowercase', 'custom_word_delimiter', 'ngram_filter', 'stop'],
                    ],
                    'index_digit_analyzer' => [
                        'type' => 'custom',
                        'char_filter' => 'digits_only',
                        'tokenizer' => 'standard',
                        'filter' => ['ngram_filter'],
                    ],
                    'index_digit_analyzer_fuzzy' => [
                        'type' => 'custom',
                        'char_filter' => 'digits_only',
                        'tokenizer' => 'digit_edge_ngram_tokenizer',
                    ],
                    'search_digit_analyzer' => [
                        'type' => 'custom',
                        'char_filter' => 'digits_only',
                        'tokenizer' => 'keyword',
                    ],
                    'search_multiple_digit_analyzer' => [
                        'type' => 'custom',
                        'char_filter' => 'digits_and_spaces',
                        'tokenizer' => 'keyword',
                        // don't use the custom_word_delimiter as we don't want to
                        // create concatenated token
                        'filter' => ['trim', 'word_delimiter_graph'],
                    ],
                ],
                'normalizer' => [
                    'keyword_normalizer' => [
                        'type' => 'custom',
                        'filter' => ['lowercase'],
                    ],
                ],
                'tokenizer' => [
                    'digit_edge_ngram_tokenizer' => [
                        'type' => 'ngram',
                        'min_gram' => 1,
                        'max_gram' => 20,
                    ],
                ],
            ],
        ],
        'mappings' => [
            'properties' => [
                'customer_id' => [
                    'type' => 'text',
                    'analyzer' => 'index_digit_analyzer',
                    'search_analyzer' => 'search_multiple_digit_analyzer',
                ],
                'computed_name' => [
                    'type' => 'text',
                    'analyzer' => 'french_analyzer_ngram',
                    'search_analyzer' => 'french_analyzer',
                ],
                'lastname' => [
                    'type' => 'text',
                    'analyzer' => 'french_analyzer',
                    'fields' => [
                        'keyword' => [
                            'type' => 'keyword',
                            'normalizer' => 'keyword_normalizer',
                        ],
                    ],
                ],
                'firstname' => [
                    'type' => 'text',
                    'analyzer' => 'french_analyzer',
                    'fields' => [
                        'keyword' => [
                            'type' => 'keyword',
                            'normalizer' => 'keyword_normalizer',
                        ],
                    ],
                ],
                'email_address' => [
                    'type' => 'text',
                    'analyzer' => 'french_analyzer_ngram',
                    'search_analyzer' => 'french_analyzer',
                    'fields' => [
                        'keyword' => [
                            'type' => 'keyword',
                            'normalizer' => 'keyword_normalizer',
                        ],
                    ],
                ],
                'company_name' => [
                    'type' => 'text',
                    'analyzer' => 'french_analyzer',
                    'fields' => [
                        'keyword' => [
                            'type' => 'keyword',
                            'normalizer' => 'keyword_normalizer',
                        ],
                    ],
                ],
                'mobile_phone' => [
                    'type' => 'text',
                    'analyzer' => 'index_digit_analyzer_fuzzy',
                    'search_analyzer' => 'search_digit_analyzer',
                ],
                'phone' => [
                    'type' => 'text',
                    'analyzer' => 'index_digit_analyzer_fuzzy',
                    'search_analyzer' => 'search_digit_analyzer',
                ],
            ],
        ],
    ];
    public const MAIN_INDEX_NAME = MagicSearch::CUSTOMER_INDEX;
    public const BUFFER_INDEX_NAME = MagicSearch::CUSTOMER_INDEX . '_buffer';
}
