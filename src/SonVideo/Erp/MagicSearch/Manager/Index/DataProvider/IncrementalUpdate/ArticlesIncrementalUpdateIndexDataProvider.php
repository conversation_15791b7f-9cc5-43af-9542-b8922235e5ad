<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Manager\Index\DataProvider\IncrementalUpdate;

use SonVideo\Erp\MagicSearch\Contract\ArticlesMagicSearchIndexDataProviderTrait;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchIncrementUpdateInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchIndexDataProviderInterface;
use SonVideo\Erp\MagicSearch\Mysql\Repository\ArticleMagicSearchRepository;

final class ArticlesIncrementalUpdateIndexDataProvider implements MagicSearchIndexDataProviderInterface, MagicSearchIncrementUpdateInterface
{
    use ArticlesMagicSearchIndexDataProviderTrait;

    private ArticleMagicSearchRepository $article_repository;

    public function __construct(ArticleMagicSearchRepository $article_repository)
    {
        $this->article_repository = $article_repository;
    }

    /** {@inheritDoc} */
    public function fetchGenerator(): \Generator
    {
        return $this->article_repository->fetchArticlesGenerator(
            ArticleMagicSearchRepository::getIncrementalUpdateConditions()
        );
    }
}
