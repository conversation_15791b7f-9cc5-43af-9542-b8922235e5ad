<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use Elasticsearch\Client as ElasticsearchClient;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchIndexDataProviderInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ElasticSearchFullIndexer implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    public const BULK_DEFAULT_PARAMS = [
        'refresh' => 'wait_for',
        'body' => [],
    ];
    public const BULK_THRESHOLD_BEFORE_PUSHING_IN_BUFFER = 1000;
    public const BULK_FORCE_PUSH_TO_BUFFER = true;

    public const MAX_BUFFERING_FAILURES = 100;

    public const FEEDBACK_MASK = [
        'collected' => 0,
        'buffered' => 0,
        'errors' => 0,
        'finished' => 'FALSE',
    ];

    private ElasticsearchClient $client;

    private LoggerInterface $logger;

    private $collected_for_bulk_update;

    private ?IndexationFeebackManager $feedback_manager = null;

    private $current_data_provider;

    /** ElasticSearchIndexer constructor. */
    public function __construct(ElasticsearchClient $client, LoggerInterface $logger)
    {
        $this->client = $client;
        $this->logger = $logger;
    }

    /** @param MagicSearchIndexDataProviderInterface[] $data_providers */
    public function generate(OutputInterface $output, array $data_providers): array
    {
        $this->feedback_manager = new IndexationFeebackManager($output, [
            'Entity',
            'Collected',
            'Buffered',
            'Error(s)',
            'Finished',
        ]);

        foreach ($data_providers as $data_provider) {
            // Prevent hard failures, next index may have no problem
            try {
                $this->current_data_provider = $data_provider->getName();
                $this->feedback_manager->addRow($data_provider->getName(), static::FEEDBACK_MASK);

                $this->index($data_provider);
            } catch (\Exception $exception) {
                $this->logger->error($exception->getMessage(), ['exception' => $exception]);
            }
        }

        return $this->feedback_manager->refresh()->dump();
    }

    /**
     * @return $this
     *
     * @throws \Exception
     */
    protected function index(MagicSearchIndexDataProviderInterface $elk_data_provider): self
    {
        // Make sure that collected items is empty
        $this->collected_for_bulk_update = null;

        // Create a buffer index to prevent running long processes on the live index
        $this->createIndex($elk_data_provider->getBufferIndexName(), $elk_data_provider->getIndexConfig());
        // Fetch entities that we want to index (use a generator for memory usage)
        $this->logger->debug(sprintf('Indexing %s in buffer index...', $elk_data_provider->getName()));
        foreach ($this->feedback_manager->startProgress($elk_data_provider->fetchGenerator()) as $raw_item) {
            // Map data in entity to have proper field types
            $item = $this->serializer->normalize(
                $this->serializer->denormalize($raw_item, $elk_data_provider->getEntityClassName())
            );

            // Collect item in bulk collection (push to buffer each time threshold is reached)
            $this->collect($item, $elk_data_provider);
        }

        // Explicitly push remaining items to buffer
        $this->buffer(static::BULK_FORCE_PUSH_TO_BUFFER);

        // Move buffer to main index only if all previous tasks were completed successfully
        $this->logger->debug('');
        $this->logger->info(sprintf('Moving to main index : %s', $elk_data_provider->getMainIndexName()));

        $this->createIndex($elk_data_provider->getMainIndexName(), $elk_data_provider->getIndexConfig());

        $this->client->reindex([
            'wait_for_completion' => true,
            'body' => [
                'source' => [
                    'index' => $elk_data_provider->getBufferIndexName(),
                ],
                'dest' => ['index' => $elk_data_provider->getMainIndexName()],
            ],
        ]);

        // ensures that the progress bar is at 100% + update Finished column
        $this->feedback_manager->finishProgress()->setArbitraryValue('finished', 'TRUE');

        // cleanup
        $this->deleteIndex($elk_data_provider->getBufferIndexName());

        return $this;
    }

    /** @return $this */
    public function createIndex(string $index, array $body): self
    {
        $this->logger->debug(sprintf('Creating "%s" index...', $index));
        $this->deleteIndex($index);
        $this->client->indices()->create(['index' => $index, 'body' => $body]);

        return $this;
    }

    /** @return $this */
    public function deleteIndex(string $index): self
    {
        if ($this->client->indices()->exists(['index' => $index])) {
            $this->logger->debug(sprintf('Deleting previous "%s" index...', $index));

            $this->client->indices()->delete(['index' => $index]);
        }

        return $this;
    }

    /**
     * @return $this
     *
     * @throws \Exception
     */
    public function collect(array $item, MagicSearchIndexDataProviderInterface $data_provider): self
    {
        if (!is_array($this->collected_for_bulk_update)) {
            $this->collected_for_bulk_update = static::BULK_DEFAULT_PARAMS;
        }

        $this->collected_for_bulk_update['body'][] = [
            'index' => [
                '_id' => $item[$data_provider->getIdKey()],
                '_index' => $data_provider->getBufferIndexName(),
            ],
        ];

        $this->collected_for_bulk_update['body'][] = $item;

        // For feedback
        $this->feedback_manager->increment('collected');

        return $this->buffer();
    }

    /**
     * @param false $force
     *
     * @return $this
     *
     * @throws \Exception
     */
    public function buffer($force = false): self
    {
        $bulk_size = 0;

        if (is_array($this->collected_for_bulk_update)) {
            $bulk_size =
                (is_countable($this->collected_for_bulk_update['body'])
                    ? count($this->collected_for_bulk_update['body'])
                    : 0) / 2;
        }

        if ($force) {
            $this->logger->debug(sprintf('Forcing push to buffer index, current bulk size is %d', $bulk_size));
        }

        // Bailout if there is no item to push
        if (0 === $bulk_size) {
            return $this;
        }

        // Bailout if we have not reach the bulk threshold
        if (false === $force && static::BULK_THRESHOLD_BEFORE_PUSHING_IN_BUFFER > $bulk_size) {
            return $this;
        }

        // Push to elastic search buffer index
        $response = $this->client->bulk($this->collected_for_bulk_update);

        // For feedback
        $already_buffered = $this->feedback_manager->dump()[$this->current_data_provider]['buffered'];
        $this->feedback_manager->setArbitraryValue('buffered', $already_buffered + $bulk_size);

        // Error management
        if (isset($response['errors']) && $response['errors']) {
            foreach ($response['items'] as $item) {
                if (isset($item['index']['error'])) {
                    // For sentry
                    $this->logger->warning('ELK Buffer failed', $item['index']);

                    // For feedback
                    $this->feedback_manager->increment('errors')->decrement('buffered');

                    // Only stop whole process if more than x item failed to be buffered
                    if (
                        static::MAX_BUFFERING_FAILURES ===
                        $this->feedback_manager->dump()[$this->current_data_provider]['errors']
                    ) {
                        throw new \Exception(json_encode($item['index'], JSON_THROW_ON_ERROR));
                    }
                }
            }
        }

        // feedback in console
        $this->feedback_manager->refresh();

        // Reset bulk collection
        $this->collected_for_bulk_update = null;

        return $this;
    }
}
