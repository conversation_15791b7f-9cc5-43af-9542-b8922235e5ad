<?php

namespace SonVideo\Erp\MagicSearch\Manager\QueryProvider;

use SonVideo\Erp\MagicSearch\Contract\ArticlesMagicSearchQueryDataProviderTrait;
use SonVideo\Erp\MagicSearch\Contract\ElasticSearchBodyRequestInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface;
use SonVideo\Erp\MagicSearch\Entity\ElasticSearchQueryRequest;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;
use SonVideo\Erp\Referential\ArticleStatus;
use SonVideo\Erp\Referential\MagicSearch;

final class ArticlesQueryProvider implements MagicSearchQueryProviderInterface
{
    use ArticlesMagicSearchQueryDataProviderTrait;

    private const DESTOCK = 'destock';

    private array $special_keys = [];

    public function canHandle(string $key): bool
    {
        return in_array($key, [MagicSearch::ARTICLE_INDEX, MagicSearch::ALL_INDEX], true);
    }

    public function getContext(): string
    {
        return 'articles';
    }

    public function getBody(MagicSearchHttpRequestPayload $request_payload): ElasticSearchBodyRequestInterface
    {
        $search_terms = trim($request_payload->search_terms);

        if (false !== stripos($search_terms, self::DESTOCK)) {
            $this->special_keys[] = self::DESTOCK;
        }

        $query = [
            'function_score' => [
                'query' => [
                    'bool' => [],
                ],
                'functions' => $this->getQueryFunctionsBoosts($search_terms),
            ],
        ];

        if ('' !== $search_terms && [] === $request_payload->filters) {
            $should_conditions = $this->getStringSearchTermsConditions($search_terms);

            $query['function_score']['query']['bool']['should'] = $should_conditions;
        }

        // additional rule when search terms is not empty, and we are using facets filters
        if ('' !== $search_terms && [] !== $request_payload->filters) {
            $query['function_score']['query']['bool']['should'][] = [
                'multi_match' => [
                    'type' => 'best_fields',
                    'operator' => 'or',
                    'query' => $search_terms,
                    'fields' => ['big_search_field', 'sku'],
                    'fuzziness' => 'AUTO',
                    'prefix_length' => 0,
                ],
            ];
        }

        // exclusions
        if ([] !== $request_payload->excludes) {
            $query['function_score']['query']['bool']['must_not'] = $request_payload->excludes;
        }

        // facets filters
        if ([] !== $request_payload->filters) {
            $query['function_score']['query']['bool']['filter'] = $request_payload->filters;
        }

        return new ElasticSearchQueryRequest(self::getSourceColumns(), $query);
    }

    private function getQueryFunctionsBoosts(string $search_terms): array
    {
        $functions = [];

        // Exact match on sku
        if (!$this->containsSpace($search_terms)) {
            $functions[] = [
                'filter' => [
                    'match' => ['sku.keyword' => $search_terms],
                ],
                'weight' => 10,
            ];
        }

        // Partial match on article name
        $functions[] = [
            'filter' => [
                'match' => ['name' => $search_terms],
            ],
            'weight' => 2,
        ];

        // Partial match on article sku
        $functions[] = [
            'filter' => [
                'match' => ['sku' => $search_terms],
            ],
            'weight' => 2,
        ];

        // Exact match on article id
        if (ctype_digit($search_terms)) {
            $functions[] = [
                'filter' => [
                    'match' => ['article_id' => $search_terms],
                ],
                'weight' => 10,
            ];
        }

        // Partial match on article category name
        if (!ctype_digit($search_terms)) {
            $functions[] = [
                'filter' => [
                    'match' => ['category.name' => $search_terms],
                ],
                'weight' => 1,
            ];
        }

        // Partial  match on article subcategory name
        if (!ctype_digit($search_terms)) {
            $functions[] = [
                'filter' => [
                    'match' => ['subcategory.name' => $search_terms],
                ],
                'weight' => 1,
            ];
        }

        // Partial match on article brand name
        if (ctype_digit($search_terms)) {
            $functions[] = [
                // Part of the string match
                'filter' => [
                    'match' => ['brand.name' => $search_terms],
                ],
                'weight' => 3,
            ];
        }

        // Exact match on article declination name
        $functions[] = [
            'filter' => [
                'match' => ['related_products.declinations.keyword' => $search_terms],
            ],
            'weight' => 2,
        ];

        // Exact match on article component sku
        $functions[] = [
            'filter' => [
                'match' => ['related_products.components.keyword' => $search_terms],
            ],
            'weight' => 2,
        ];

        // Exact match on article substitute sku
        $functions[] = [
            'filter' => [
                'match' => ['related_products.substitute_of.keyword' => $search_terms],
            ],
            'weight' => 2,
        ];

        // Negative boost when matching on article type
        // Depends on special keys
        $functions[] = [
            'filter' => [
                'terms' => ['type' => $this->isSearchingDestock() ? ['compose'] : ['compose', 'destock']],
            ],
            'weight' => 0.8,
        ];

        // Negative boost when matching on article type and status
        $functions[] = [
            'filter' => [
                'bool' => [
                    'must' => [['match' => ['status' => ArticleStatus::YAPU]]],
                ],
            ],
            'weight' => 0.5,
        ];

        // Negative boost if not searching for destock
        // Otherwise increase boost
        $functions[] = [
            'filter' => [
                'bool' => [
                    'must' => [['match' => ['type' => 'destock']]],
                ],
            ],
            'weight' => $this->isSearchingDestock() ? 10 : 0.3,
        ];

        // Negative if the article is unavailable on son-video.com
        $functions[] = [
            'filter' => [
                'bool' => [
                    'must' => [['exists' => ['field' => 'unbasketable_reason']]],
                ],
            ],
            'weight' => 0.5,
        ];

        // boost if the article is available
        $functions[] = [
            'filter' => ['terms' => ['status' => [ArticleStatus::OUI]]],
            'weight' => 2,
        ];

        // boost (less) if the article is available but will not be replenished once the stock is empty
        $functions[] = [
            'filter' => ['terms' => ['status' => [ArticleStatus::LAST]]],
            'weight' => 1.8,
        ];

        // negative boost if the article has status NON
        $functions[] = [
            'filter' => ['match' => ['status' => ArticleStatus::NON]],
            'weight' => 0.3,
        ];

        // Boost if the article is in stock
        $functions[] = [
            'filter' => ['range' => ['stock' => ['gt' => 0]]],
            'weight' => 1.5,
        ];

        return $functions;
    }

    private function getStringSearchTermsConditions(string $search_terms): array
    {
        if (false === $this->containsSpace($search_terms)) {
            $should_conditions[] = [
                'multi_match' => [
                    'type' => 'best_fields',
                    'operator' => 'or',
                    'query' => $search_terms,
                    'fields' => ['barcodes.code128', 'barcodes.eans', 'sku'],
                    'fuzziness' => 'AUTO',
                    'prefix_length' => 0,
                ],
            ];
        }

        $should_conditions[] = [
            'multi_match' => [
                'type' => 'best_fields',
                'operator' => 'or',
                'query' => $search_terms,
                'fields' => ['big_search_field', 'sku'],
                'fuzziness' => 'AUTO',
                'prefix_length' => 0,
            ],
        ];

        $should_conditions[] = [
            'multi_match' => [
                'operator' => 'or',
                'query' => $search_terms,
                'type' => 'best_fields',
                'fields' => ['brand.name', 'category.name', 'subcategory.name'],
                'fuzziness' => 'AUTO',
                'prefix_length' => 0,
            ],
        ];

        return $should_conditions;
    }

    /** Checks if the search terms contain a space character. */
    private function containsSpace(string $search_terms): bool
    {
        return false !== strpos($search_terms, ' ');
    }

    private function isSearchingDestock(): bool
    {
        return in_array(self::DESTOCK, $this->special_keys, true);
    }
}
