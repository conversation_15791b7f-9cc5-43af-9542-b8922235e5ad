<?php

namespace SonVideo\Erp\MagicSearch\Manager\QueryProvider\Aggregations;

use SonVideo\Erp\MagicSearch\Contract\ArticlesMagicSearchQueryDataProviderTrait;
use SonVideo\Erp\MagicSearch\Contract\ElasticSearchBodyRequestInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface;
use SonVideo\Erp\MagicSearch\Entity\ElasticSearchAggregationsQueryRequest;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;

class ArticlesAggregationsQueryProvider implements MagicSearchQueryProviderInterface
{
    use ArticlesMagicSearchQueryDataProviderTrait;

    private const CONTEXT_NAME = 'articles_aggregations';

    public function canHandle(string $key): bool
    {
        return self::CONTEXT_NAME === $key;
    }

    public function getContext(): string
    {
        return self::CONTEXT_NAME;
    }

    public function decorateResults(array $results): array
    {
        return [
            'brands' => $results['aggregations']['brands']['buckets'],
            'categories' => $results['aggregations']['categories']['buckets'],
            'availability' => $results['aggregations']['availability']['buckets'],
            'subcategories' => $results['aggregations']['subcategories']['buckets'],
        ];
    }

    public function getBody(MagicSearchHttpRequestPayload $request_payload): ElasticSearchBodyRequestInterface
    {
        return new ElasticSearchAggregationsQueryRequest([
            'availability' => [
                'terms' => [
                    'field' => 'delay',
                ],
            ],
            'brands' => [
                'composite' => [
                    'size' => 2000,
                    'sources' => [
                        [
                            'id' => [
                                'terms' => [
                                    'field' => 'brand.id',
                                ],
                            ],
                        ],
                        [
                            'name' => [
                                'terms' => [
                                    'field' => 'brand.name.keyword',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'categories' => [
                'composite' => [
                    'size' => 200,
                    'sources' => [
                        [
                            'id' => [
                                'terms' => [
                                    'field' => 'category.id',
                                ],
                            ],
                        ],
                        [
                            'name' => [
                                'terms' => [
                                    'field' => 'category.name.keyword',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'subcategories' => [
                'composite' => [
                    'sources' => [
                        [
                            'id' => [
                                'terms' => [
                                    'field' => 'subcategory.id',
                                ],
                            ],
                        ],
                        [
                            'name' => [
                                'terms' => [
                                    'field' => 'subcategory.name.keyword',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }
}
