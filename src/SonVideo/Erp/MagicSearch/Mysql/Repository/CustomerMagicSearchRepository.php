<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;

final class CustomerMagicSearchRepository extends AbstractLegacyRepository
{
    public const FULL_INDEX_CONDITIONS = 'WHERE TRUE';

    public function fetchCustomersGenerator(string $conditions = self::FULL_INDEX_CONDITIONS): \Generator
    {
        $sql = <<<SQL
        SELECT
          p.id_prospect AS customer_id,
          p.type AS type,
          p.cnt_civilite As civility,
          COALESCE(backOffice.GET_COMPUTED_CUSTOMER_NAME(p.cnt_prenom, p.prenom, p.cnt_nom, p.nom), '') AS computed_name,
          p.cnt_nom AS lastname,
          p.cnt_prenom AS firstname,
          p.cnt_email AS email_address,
          p.cnt_societe AS company_name,
          p.cnt_mobile AS mobile_phone,
          p.cnt_telephone AS phone,
          p.cnt_adresse AS address,
          p.cnt_code_postal AS zip_code,
          UPPER(p.cnt_ville) AS city,
          p.cnt_id_pays AS country_id,
          pa.pays AS country_name,
          pa.code_2_lettres AS country_code,
          p.cnt_type AS customer_type,
          p.blacklist AS is_blacklisted,
          p.envoi_email AS accept_marketing_emails,
          p.date_creation AS created_at,
          p.date_naissance AS birthdate
          FROM
            backOffice.prospect p
              INNER JOIN backOffice.pays pa ON p.cnt_id_pays = pa.id_pays,
            -- outer data for the cron
            (
              SELECT
                backOffice.GET_SYS_VAR_datetime('magic_search.incremental_update.start_at') AS start_at,
                backOffice.GET_SYS_VAR_datetime('magic_search.incremental_update.stop_at')  AS stop_at
            ) tmp
          {conditions}
        ;
        SQL;

        return $this->legacy_pdo->iterateAssociative(
            strtr($sql, [
                '{conditions}' => $conditions,
            ])
        );
    }

    public static function getIncrementalUpdateConditions(): string
    {
        return <<<SQL
        WHERE p.date_creation BETWEEN tmp.start_at AND tmp.stop_at
          OR p.date_modification BETWEEN tmp.start_at AND tmp.stop_at
        SQL;
    }
}
