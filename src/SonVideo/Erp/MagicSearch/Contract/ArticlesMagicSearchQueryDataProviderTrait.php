<?php

namespace SonVideo\Erp\MagicSearch\Contract;

use SonVideo\Erp\MagicSearch\Manager\Index\Config\ArticlesIndexConfig;

trait ArticlesMagicSearchQueryDataProviderTrait
{
    public function getIndexName(): string
    {
        return ArticlesIndexConfig::MAIN_INDEX_NAME;
    }

    public function decorateResults(array $results): array
    {
        $articles = [];
        if (0 !== $results['hits']['total']['value']) {
            foreach ($results['hits']['hits'] as $article) {
                $articles['results'][] = $article['_source'];
            }

            $articles['total'] = $results['hits']['total']['value'];
        }

        return $articles;
    }

    public static function getSourceColumns(): array
    {
        return [
            'article_id',
            'sku',
            'name',
            'computed_name',
            'status',
            'type',
            'brand',
            'group_brand',
            'category',
            'subcategory',
            'article_url',
            'image',
            'prices',
            'unbasketable_reason',
            'stock',
            'delay',
            'related_products',
        ];
    }
}
