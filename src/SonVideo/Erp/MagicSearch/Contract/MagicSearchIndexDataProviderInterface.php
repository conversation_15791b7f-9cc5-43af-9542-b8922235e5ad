<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Contract;

use App\Contract\Collection\CollectableInterface;

interface MagicSearchIndexDataProviderInterface extends CollectableInterface
{
    public function getName(): string;

    public function getIdKey(): string;

    public function getBufferIndexName(): string;

    public function getMainIndexName(): string;

    public function getIndexConfig(): array;

    public function fetchGenerator(): \Generator;

    public function getEntityClassName(): string;
}
