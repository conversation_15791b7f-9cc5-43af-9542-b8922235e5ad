<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Entity;

use App\Entity\AbstractEntity;

/**
 * Class CustomerMagicSearchEntity.
 */
class CustomerMagicSearchEntity extends AbstractEntity
{
    public int $customer_id;

    public string $type;

    public string $civility;

    public string $computed_name;

    public string $lastname;

    public string $firstname;

    public string $email_address;

    public string $company_name;

    public string $mobile_phone;

    public string $phone;

    public ?string $address = null;

    public ?string $zip_code = null;

    public string $city;

    public ?int $country_id = null;

    public ?string $country_name = null;

    public ?string $country_code = null;

    public ?string $customer_type = null;

    public bool $is_blacklisted;

    public bool $accept_marketing_emails;

    public ?string $created_at = null;

    public ?string $birthdate = null;
}
