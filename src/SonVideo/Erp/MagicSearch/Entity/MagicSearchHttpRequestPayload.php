<?php

namespace SonVideo\Erp\MagicSearch\Entity;

use OpenApi\Annotations as OA;

class MagicSearchHttpRequestPayload
{
    public ?string $search_terms = '';

    public string $context;

    public int $size = 15;

    public int $from = 0;

    /**
     * @OA\Property(
     *     type="array",
     *     @OA\Items(type="object")
     * )
     */
    public array $excludes = [];

    /**
     * @OA\Property(
     *     type="array",
     *     @OA\Items(type="object")
     * )
     */
    public array $filters = [];
}
