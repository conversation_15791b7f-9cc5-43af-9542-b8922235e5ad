<?php

namespace SonVideo\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleEansUpdateContextDto;
use SonVideo\Erp\Article\Mysql\Repository\ArticleEanRepository;
use SonVideo\Erp\User\Entity\UserEntity;

class ArticleEanManager
{
    private LegacyPdo $legacy_pdo;

    private ArticleEanRepository $article_ean_repository;

    private SerializerInterface $serializer;

    private QueryBuilder $query_builder;

    private ArticleEventLogger $article_event_logger;

    public function __construct(
        ArticleEanRepository $article_ean_repository,
        LegacyPdo $legacy_pdo,
        SerializerInterface $serializer,
        QueryBuilder $query_builder,
        ArticleEventLogger $article_event_logger
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->article_ean_repository = $article_ean_repository;
        $this->serializer = $serializer;
        $this->query_builder = $query_builder;
        $this->article_event_logger = $article_event_logger;
    }

    public function createDto(int $article_id, array $eans): ArticleEansUpdateContextDto
    {
        $data = [
            'article_id' => $article_id,
            'eans' => $eans,
        ];

        return $this->serializer->denormalize($data, ArticleEansUpdateContextDto::class);
    }

    public function update(ArticleEansUpdateContextDto $dto, UserEntity $user): int
    {
        $article_id = $dto->article_id;
        $data = $this->dtoToArray($dto);

        $changes = $this->computeListChange($article_id, $data);
        try {
            $this->legacy_pdo->beginTransaction();

            $nb_updated = $this->updateEans($article_id, $changes['eans']);

            if ($nb_updated > 0) {
                $this->article_event_logger->logChanges($article_id, 'eans', $changes, $user);
            }

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $nb_updated;
    }

    private function dtoToArray(ArticleEansUpdateContextDto $dto): array
    {
        $data = $this->serializer->normalize($dto);
        unset($data['article_id']);

        return $data;
    }

    /**
     * @param array<string> $new_values
     *
     * @return array{eans: array{added: string[], deleted: string[]}}
     *
     * @throws NotFoundException
     */
    private function computeListChange(int $article_id, array $new_values): array
    {
        $this->query_builder->setWhere(['article_id' => ['_eq' => $article_id]], ArticleEanRepository::COLUMNS_MAPPING);

        $old_raw_values = $this->article_ean_repository->findAllPaginated($this->query_builder)->getResults();
        $old_eans = array_map(static fn ($e): string => $e->ean, $old_raw_values);

        $new_eans = array_map(static fn ($e): string => str_pad($e, 13, '0', STR_PAD_LEFT), $new_values['eans']);

        $added = array_diff($new_eans, $old_eans);
        $deleted = array_diff($old_eans, $new_eans);

        return ['eans' => ['added' => $added, 'deleted' => $deleted]];
    }

    private function updateEans(int $article_id, array $eans): int
    {
        $nb_updated = 0;
        foreach ($eans['added'] as $ean) {
            $nb_updated += $this->article_ean_repository->add($article_id, $ean);
        }
        foreach ($eans['deleted'] as $ean) {
            $nb_updated += $this->article_ean_repository->delete($article_id, $ean);
        }

        return $nb_updated;
    }
}
