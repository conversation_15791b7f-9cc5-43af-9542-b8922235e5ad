<?php

declare(strict_types=1);

namespace SonVideo\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Database\Orm\MysqlErp\Repository\Entity\CompetitorPricing;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\LegacyPdo;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Article\Dto\SalesChannelPriceContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\TriggeredPriceUpdateContextDto;
use SonVideo\Erp\Article\Entity\ArticlePlannedPriceEntity;
use SonVideo\Erp\Article\Entity\ArticleSalesChannelEntity;
use SonVideo\Erp\Article\Entity\PackagedArticleEntity;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleSalesChannelRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleUnconditionalDiscountRepository;
use SonVideo\Erp\Article\Mysql\Repository\PackagedArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Article\Traits\ArticleUpdateSellingPriceTrait;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyEntity;
use SonVideo\Erp\Referential\ArticleUpdateScope;
use SonVideo\Erp\Referential\SalesChannel;
use SonVideo\Erp\SalesChannel\Exception\MarginValidationException;
use SonVideo\Erp\SalesChannel\Mysql\Repository\SalesChannelRepository;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class ArticleSalesChannelManager implements LoggerAwareInterface, SerializerAwareInterface
{
    use ArticleUpdateSellingPriceTrait;
    use LoggerAwareTrait;
    use SerializerAwareTrait;

    private const SELLING_PRICE_FIELD = 'selling_price';
    private const LOG_LABEL = 'label';
    private const LOG_IS_ACTIVE = 'is_active';
    private const LOG_ENTITY_ID = 'sales_channel_id';

    private const LOG_CHANGE_OLD = 'old';
    private const LOG_CHANGE_NEW = 'new';
    private const DEFAULT_MINIMUM_MARGIN_RATE = 0.0;
    public const NO_PROMO_BUDGET_AMOUNT = 0.0;
    public const NO_UNCONDITIONAL_DISCOUNT = 0.0;
    public const ARTICLE_SALES_CHANNEL_NOT_FOUND = 'Article sales channel not found.';
    public const ARTICLE_SALES_CHANNEL_ALREADY_EXISTS = 'Article sales channel already exists.';

    private LegacyPdo $legacy_pdo;

    private ArticleSalesChannelRepository $article_sales_channel_repository;
    private SingleArticleReadRepository $single_article_read_repository;

    private ArticleEventLogger $article_event_logger;

    private CurrentUser $user;
    private SalesChannelRepository $sales_channel_repository;
    private ArticleMarginCalculator $article_margin_calculator;
    private ArticleUnconditionalDiscountRepository $article_unconditional_discount_repository;
    private PackagedArticleRepository $packaged_article_repository;

    public function __construct(
        LegacyPdo $legacy_pdo,
        ArticleSalesChannelRepository $article_sales_channel_repository,
        ArticleEventLogger $article_event_logger,
        ArticleRepository $article_repository,
        SingleArticleReadRepository $single_article_read_repository,
        CurrentUser $user,
        SalesChannelRepository $sales_channel_repository,
        ArticleUnconditionalDiscountRepository $article_unconditional_discount_repository,
        ArticleMarginCalculator $article_margin_calculator,
        PackagedArticleRepository $packaged_article_repository
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->article_sales_channel_repository = $article_sales_channel_repository;
        $this->article_event_logger = $article_event_logger;
        $this->article_repository = $article_repository;
        $this->single_article_read_repository = $single_article_read_repository;
        $this->user = $user;
        $this->sales_channel_repository = $sales_channel_repository;
        $this->article_margin_calculator = $article_margin_calculator;
        $this->article_unconditional_discount_repository = $article_unconditional_discount_repository;
        $this->packaged_article_repository = $packaged_article_repository;
    }

    /**
     * @throws NotFoundException
     * @throws \Exception
     */
    public function createOrEnable(int $article_id, int $sales_channel_id): int
    {
        try {
            $this->enable($article_id, $sales_channel_id);

            return 0;
        } catch (NotFoundException $exception) {
            $this->create($article_id, $sales_channel_id);

            return 1;
        }
    }

    /** @throws NotFoundException
     * @throws \Exception
     */
    public function create(int $article_id, int $sales_channel_id): int
    {
        $sales_channel = $this->fetchSalesChannelIfCanCreate($article_id, $sales_channel_id);

        try {
            $this->legacy_pdo->beginTransaction();

            $inserted = $this->article_sales_channel_repository->insert($article_id, $sales_channel_id);

            if ($inserted > 0) {
                $this->article_event_logger->logCreation(
                    $article_id,
                    ArticleUpdateScope::SALES_CHANNEL_PRODUCT,
                    $this->formatedSalesChannelForLogs($sales_channel),
                    $this->user->entity(),
                    ['sales_channel_id' => $sales_channel['sales_channel_id']]
                );
            }
            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }

        return $inserted;
    }

    /**
     * @throws MarginValidationException
     * @throws InternalServerErrorException
     * @throws NotFoundException|ExceptionInterface
     */
    public function updateSellingPriceWithTaxes(
        int $article_id,
        int $sales_channel_id,
        float $selling_price,
        TriggeredPriceUpdateContextDto $price_update_context = null,
        UserEntity $user = null
    ): int {
        $sales_channel_product = $this->article_sales_channel_repository->findById($article_id, $sales_channel_id);

        if (!$sales_channel_product instanceof ArticleSalesChannelEntity) {
            return 0;
        }

        if (round($sales_channel_product->selling_price, 2) === round($selling_price, 2)) {
            return 0;
        }

        $article = $this->article_repository->getOneById($article_id, [
            'stock',
            'is_package',
            'is_on_sale',
            'sale_selling_price',
        ]);

        if (
            $article['is_on_sale'] &&
            SalesChannel::SON_VIDEO === $sales_channel_id &&
            (float) $article['sale_selling_price'] !== $selling_price
        ) {
            return 0;
        }

        $article_price = $this->single_article_read_repository->getArticlePrice((string) $article_id);

        $price_context = new SalesChannelPriceContextDto();
        $price_context->selling_price = $selling_price;
        $price_context->sales_channel_id = $sales_channel_id;
        $price_context->pvgc = $article_price->pvgc;
        $price_context->stock = (int) $article['stock'];
        $price_context->ecotax = $article_price->ecotax;
        $price_context->sorecop = $article_price->sorecop;
        $price_context->vat_rate = $article_price->vat;
        $price_context->purchase_price_tax_excluded = $article_price->tariff_tax_excluded;
        $price_context->weighted_purchase_price_tax_excluded = $article_price->weighted_cost;
        $price_context->unconditional_discount =
            $article_price->unconditional_discount ?? self::NO_UNCONDITIONAL_DISCOUNT;
        $price_context->promo_budget_amount = $article_price->promo_budget
            ? $article_price->promo_budget['amount']
            : self::NO_PROMO_BUDGET_AMOUNT;
        $price_context->sales_channel_commission_fee = $sales_channel_product->average_commission_rate;

        if (!($user instanceof UserEntity && UserEntity::SYSTEM_ID === $user->id_utilisateur)) {
            $this->checkError(
                $article['is_package'],
                $article_id,
                $selling_price,
                $price_context,
                $sales_channel_product,
                $price_update_context,
                $sales_channel_id
            );
        }

        $already_in_transaction = $this->legacy_pdo->inTransaction();
        try {
            if (!$already_in_transaction) {
                $this->legacy_pdo->beginTransaction();
            }
            $old_selling_price = $sales_channel_product->selling_price;

            $nb_affected = $this->article_sales_channel_repository->updateSellingPriceWithTaxes(
                $article_id,
                $sales_channel_id,
                $selling_price
            );
            $this->logPriceChange(
                $article_id,
                $sales_channel_product,
                $old_selling_price,
                $selling_price,
                $price_update_context,
                $user
            );

            if (SalesChannel::SON_VIDEO === $sales_channel_id) {
                $this->updateArticleSellingPrice($article_id, $old_selling_price, $selling_price, $user);
            }
            if (!$already_in_transaction) {
                $this->legacy_pdo->commit();
            }
        } catch (\Exception $exception) {
            if (!$already_in_transaction) {
                $this->legacy_pdo->rollBack();
            }
            throw $exception;
        }

        return $nb_affected;
    }

    /**
     * @throws MarginValidationException
     * @throws NotFoundException
     * @throws InternalServerErrorException
     * @throws SqlErrorMessageException
     */
    public function updateAllSalesChannelWithPrice(int $article_id, float $new_price): void
    {
        $product_sales_channels = $this->article_sales_channel_repository->findAllByArticleId($article_id);

        $selling_prices = array_unique(array_column($product_sales_channels, 'selling_price'));

        if (1 === count($selling_prices)) {
            foreach ($product_sales_channels as $product_sales_channel) {
                if (false === $product_sales_channel->is_active) {
                    continue;
                }
                if (SalesChannel::SON_VIDEO !== $product_sales_channel->sales_channel_id) {
                    $this->updateSellingPriceWithTaxes(
                        $article_id,
                        $product_sales_channel->sales_channel_id,
                        $new_price
                    );
                }
            }
        }
    }

    /** @throws NotFoundException */
    public function setIsActive(int $article_id, int $sales_channel_id, bool $is_active): int
    {
        if ($is_active) {
            return $this->enable($article_id, $sales_channel_id);
        }

        return $this->disable($article_id, $sales_channel_id);
    }

    /** @throws NotFoundException
     * @throws \Exception
     */
    public function disable(int $article_id, int $sales_channel_id): int
    {
        $sales_channel_product = $this->article_sales_channel_repository->findById($article_id, $sales_channel_id);
        if (!$sales_channel_product instanceof ArticleSalesChannelEntity) {
            throw new NotFoundException(self::ARTICLE_SALES_CHANNEL_NOT_FOUND);
        }
        try {
            $this->legacy_pdo->beginTransaction();
            $disabled = $this->article_sales_channel_repository->disable($article_id, $sales_channel_id);
            if ($disabled > 0) {
                $this->logIsActiveChange($article_id, $sales_channel_product, false);
            }
            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }

        return $disabled;
    }

    /** @throws NotFoundException
     * @throws \Exception
     */
    public function enable(int $article_id, int $sales_channel_id): int
    {
        $sales_channel_product = $this->article_sales_channel_repository->findById(
            $article_id,
            $sales_channel_id,
            false
        );
        if (!$sales_channel_product instanceof ArticleSalesChannelEntity) {
            throw new NotFoundException(self::ARTICLE_SALES_CHANNEL_NOT_FOUND);
        }
        try {
            $this->legacy_pdo->beginTransaction();
            $result_count = $this->article_sales_channel_repository->activate($article_id, $sales_channel_id);
            if ($result_count > 0) {
                $this->logIsActiveChange($article_id, $sales_channel_product, true);
            }
            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }

        return $result_count;
    }

    /** @return array{sales_channel: array{sales_channel_id: mixed, label: mixed}} */
    private function formatedSalesChannelForLogs(array $sales_channel): array
    {
        return [
            self::LOG_ENTITY_ID => $sales_channel['sales_channel_id'],
            self::LOG_LABEL => $sales_channel['label'],
        ];
    }

    /**
     * @throws NotFoundException
     * @throws \Exception
     */
    private function fetchSalesChannelIfCanCreate(int $article_id, int $sales_channel_id): array
    {
        $this->article_repository->getOneById($article_id, ['article_id']);
        $sales_channel = $this->sales_channel_repository->getOneById($sales_channel_id);

        $article_sales_channel_entity = $this->article_sales_channel_repository->findById(
            $article_id,
            $sales_channel_id
        );
        if ($article_sales_channel_entity instanceof ArticleSalesChannelEntity) {
            throw new \Exception(self::ARTICLE_SALES_CHANNEL_ALREADY_EXISTS);
        }

        return $sales_channel;
    }

    /** @throws InternalServerErrorException */
    private function logPriceChange(
        int $article_id,
        ArticleSalesChannelEntity $sales_channel_product,
        float $old_selling_price,
        float $new_selling_price,
        TriggeredPriceUpdateContextDto $price_update_context = null,
        UserEntity $user = null
    ): void {
        if (!$user instanceof UserEntity) {
            $user = $this->user->entity();
        }

        $complementary_meta = [];
        if ($price_update_context instanceof TriggeredPriceUpdateContextDto) {
            if ($price_update_context->pricing_strategy instanceof PricingStrategyEntity) {
                $complementary_meta = array_merge($complementary_meta, [
                    'pricing_strategy' => [
                        'id' => $price_update_context->pricing_strategy->pricing_strategy_id,
                    ],
                ]);
            }

            if ($price_update_context->competitor_pricing instanceof CompetitorPricing) {
                $complementary_meta = array_merge($complementary_meta, [
                    'lowest_competitor' => [
                        'code' => $price_update_context->competitor_pricing->competitor_code,
                        'price' => $price_update_context->competitor_pricing->selling_price_with_taxes,
                    ],
                ]);
            }

            if ($price_update_context->planned_price instanceof ArticlePlannedPriceEntity) {
                $complementary_meta = array_merge($complementary_meta, [
                    'planned_price' => [
                        'id' => $price_update_context->planned_price->article_planned_price_id,
                        'starts_at' => $price_update_context->planned_price->starts_at->format('Y-m-d H:i:s'),
                        'ends_at' => $price_update_context->planned_price->ends_at->format('Y-m-d H:i:s'),
                    ],
                ]);
            }

            if ([] !== $price_update_context->warnings) {
                $complementary_meta = array_merge($complementary_meta, [
                    'warnings' => $price_update_context->warnings,
                ]);
            }
        }

        $this->article_event_logger->logChanges(
            $article_id,
            ArticleUpdateScope::SALES_CHANNEL_PRODUCT_PRICES,
            [
                self::SELLING_PRICE_FIELD => [
                    self::LOG_CHANGE_OLD => $old_selling_price,
                    self::LOG_CHANGE_NEW => $new_selling_price,
                ],
            ],
            $user,
            [self::LOG_ENTITY_ID => $sales_channel_product->sales_channel_id],
            $complementary_meta
        );
    }

    private function logIsActiveChange(
        int $article_id,
        ArticleSalesChannelEntity $sales_channel_product,
        bool $is_active
    ): void {
        $this->article_event_logger->logChanges(
            $article_id,
            ArticleUpdateScope::SALES_CHANNEL_PRODUCT,
            [
                self::LOG_IS_ACTIVE => [self::LOG_CHANGE_OLD => !$is_active, self::LOG_CHANGE_NEW => $is_active],
            ],
            $this->user->entity(),
            [self::LOG_ENTITY_ID => $sales_channel_product->sales_channel_id]
        );
    }

    private function checkError(
        $is_package,
        int $article_id,
        float $selling_price,
        SalesChannelPriceContextDto $margin_computing_context,
        ArticleSalesChannelEntity $sales_channel_product,
        ?TriggeredPriceUpdateContextDto $price_update_context,
        int $sales_channel_id
    ): void {
        if (1 === (int) $is_package) {
            $packaged_articles_total_selling_price = array_reduce(
                $this->packaged_article_repository->findAllByPackageId((string) $article_id),
                static fn ($carry, PackagedArticleEntity $packaged_article): float => $carry +
                    $packaged_article->quantity * $packaged_article->unit_selling_price,
                0.0
            );

            if ($selling_price > $packaged_articles_total_selling_price) {
                throw new MarginValidationException('[key:package_selling_price_too_high] The price must be below the packaged article price');
            }
        }

        $result = $this->article_margin_calculator->computeMarginWithErrorCheck(
            $margin_computing_context,
            $sales_channel_product->minimum_margin_rate ?? self::DEFAULT_MINIMUM_MARGIN_RATE
        );

        if (null != $result->errors) {
            $error_message = $result->errors[0]['message'];
            if (
                $price_update_context instanceof TriggeredPriceUpdateContextDto &&
                $price_update_context->competitor_pricing instanceof CompetitorPricing &&
                SalesChannel::SON_VIDEO_CODE === $price_update_context->competitor_pricing->competitor_code &&
                [] !==
                    array_intersect(array_column($result->errors, 'code'), [
                        ArticleMarginCalculator::MARGIN_TOO_LOW_ERROR_CODE,
                        ArticleMarginCalculator::MARGIN_RATE_TOO_LOW_ERROR_CODE,
                        ArticleMarginCalculator::SELLING_PRICE_ABOVE_PVGC_ERROR_CODE,
                    ])
            ) {
                $price_update_context->warnings[] = sprintf(
                    'UpdateSellingPriceWithTaxes for product %d and sales channel %d : %s',
                    $article_id,
                    $sales_channel_id,
                    $error_message
                );
                $price_update_context->warnings[] = sprintf('%s', json_encode($this->serializer->normalize($result)));
            } else {
                throw new MarginValidationException($error_message);
            }
        }
    }
}
