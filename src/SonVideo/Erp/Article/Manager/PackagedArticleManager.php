<?php

namespace SonVideo\Erp\Article\Manager;

use App\Contract\DataLoaderAwareInterface;
use App\DataLoader\MapToEntityTrait;
use App\Exception\InternalErrorException;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use SonVideo\Erp\Article\Dto\UpdateContext\PackagedArticleContextDto;
use SonVideo\Erp\Article\Entity\PackagedArticleEntity;
use SonVideo\Erp\Article\Manager\Validator\PackageValidator;
use SonVideo\Erp\Article\Mysql\Repository\PackagedArticleRepository;
use SonVideo\Erp\System\Common\CurrentUser;

class PackagedArticleManager implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    private LegacyPdo $legacy_pdo;

    private ArticleEventLogger $article_event_logger;

    private PackagedArticleRepository $packaged_article_repository;

    private CurrentUser $user;

    private PackageValidator $package_validator;

    public function __construct(
        LegacyPdo $legacy_pdo,
        ArticleEventLogger $article_event_logger,
        PackagedArticleRepository $packaged_article_repository,
        CurrentUser $user,
        PackageValidator $package_validator
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->article_event_logger = $article_event_logger;
        $this->packaged_article_repository = $packaged_article_repository;
        $this->user = $user;
        $this->package_validator = $package_validator;
    }

    /** @return array{packaged_article_quantity: array{old: int, new: int}} */
    private function computeChanges(
        PackagedArticleContextDto $dto,
        PackagedArticleEntity $current_packaged_article
    ): array {
        return [
            'packaged_article_quantity' => ['old' => $current_packaged_article->quantity, 'new' => $dto->quantity],
        ];
    }

    /**
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws InternalErrorException
     */
    public function update(PackagedArticleContextDto $dto): int
    {
        $this->package_validator->validateDto($dto);

        $this->package_validator->checkIfCanEditPackagedArticle($dto);

        $current_packaged_article = $this->packaged_article_repository->findById($dto->packaged_article_id);
        $changes = $this->computeChanges($dto, $current_packaged_article);

        try {
            $this->legacy_pdo->beginTransaction();

            $updated = $this->packaged_article_repository->updatePackagedArticle($dto);

            if ($updated > 0) {
                $this->article_event_logger->logChanges(
                    $dto->package_id,
                    'packaged_article',
                    $changes,
                    $this->user->entity(),
                    ['packaged_article' => $current_packaged_article->article_id]
                );
            }
            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }

        return $updated;
    }

    /**
     * @throws NotFoundException
     * @throws InternalServerErrorException
     */
    public function delete(int $packaged_article_id, int $package_id): int
    {
        $packaged_article = $this->packaged_article_repository->findById($packaged_article_id);
        if (!$packaged_article instanceof PackagedArticleEntity) {
            throw new NotFoundException('Packaged article not found.');
        }

        if ($packaged_article->package_id !== $package_id) {
            throw new NotFoundException('Packaged article not found in package.');
        }

        try {
            $this->legacy_pdo->beginTransaction();

            $deleted = $this->packaged_article_repository->delete($packaged_article_id);

            if ($deleted > 0) {
                $this->article_event_logger->logChanges(
                    $package_id,
                    'package',
                    [
                        'packaged_article' => [
                            'deleted' => [
                                [
                                    'article_id' => $packaged_article->article_id,
                                    'quantity' => $packaged_article->quantity,
                                ],
                            ],
                            'added' => [],
                        ],
                    ],
                    $this->user->entity()
                );
            }
            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }

        return $deleted;
    }
}
