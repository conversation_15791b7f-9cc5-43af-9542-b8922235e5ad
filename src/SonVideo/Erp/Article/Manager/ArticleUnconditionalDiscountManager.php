<?php

namespace SonVideo\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Params\FiltersParams;
use App\Sql\Helper\Pager;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Article\Dto\ArticleUnconditionalDiscountDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleUnconditionalDiscountUpdateContextDto;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleUnconditionalDiscountRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\SupplierContract\Mysql\Repository\SupplierContractRepository;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ArticleUnconditionalDiscountManager
{
    private ArticleUnconditionalDiscountRepository $article_unconditional_discount_repository;

    private QueryBuilder $query_builder;

    private ArticleEventLogger $article_event_logger;

    private CurrentUser $current_user;

    private ArticleRepository $article_repository;

    private ValidatorInterface $validator;

    private LegacyPdo $legacy_pdo;

    private SupplierContractRepository $supplier_contract_repository;

    private SerializerInterface $serializer;

    private const ARTICLE_LOGGER_SCOPE = 'unconditional_discount';

    public function __construct(
        ArticleUnconditionalDiscountRepository $article_unconditional_discount_repository,
        QueryBuilder $query_builder,
        ArticleEventLogger $article_event_logger,
        CurrentUser $current_user,
        ArticleRepository $article_repository,
        ValidatorInterface $validator,
        LegacyPdo $legacy_pdo,
        SupplierContractRepository $supplier_contract_repository,
        SerializerInterface $serializer
    ) {
        $this->article_unconditional_discount_repository = $article_unconditional_discount_repository;
        $this->query_builder = $query_builder;
        $this->article_event_logger = $article_event_logger;
        $this->current_user = $current_user;
        $this->article_repository = $article_repository;
        $this->validator = $validator;
        $this->legacy_pdo = $legacy_pdo;
        $this->supplier_contract_repository = $supplier_contract_repository;
        $this->serializer = $serializer;
    }

    public function getFilteredCollection(FiltersParams $params): Pager
    {
        $query_builder = $this->query_builder
            ->setWhere($params->getFilters() ?? [])
            ->setOrderBy($params->getOrderBy(), $params->getOrderDirection())
            ->setPage($params->getPage(), $params->getLimit());

        return $this->article_unconditional_discount_repository->findAllPaginated($query_builder);
    }

    /**
     * @throws InternalErrorException
     * @throws \Exception
     * @throws ExceptionInterface
     */
    public function update(ArticleUnconditionalDiscountUpdateContextDto $dto): void
    {
        $this->article_repository->getOneById($dto->article_id);

        $supplier_contract_amount = $this->supplier_contract_repository->findSupplierContractUnconditionalDiscountAmountIfExistByArticleId(
            $dto->article_id
        );

        if ('' === $dto->amount) {
            if ($supplier_contract_amount) {
                $this->article_unconditional_discount_repository->delete($dto->article_id);

                $dto->amount = $supplier_contract_amount;
                $dto = $this->serializer->denormalize($dto, ArticleUnconditionalDiscountDto::class);
                $changes = $this->computeChanges($dto);

                $this->article_event_logger->logChanges(
                    $dto->article_id,
                    self::ARTICLE_LOGGER_SCOPE,
                    $changes,
                    $this->current_user->entity()
                );

                return;
            }

            $dto->amount = 0;
        }

        $dto = $this->serializer->denormalize($dto, ArticleUnconditionalDiscountDto::class);

        $this->validateDto($dto);

        $changes = $this->computeChanges($dto);

        try {
            $this->legacy_pdo->beginTransaction();
            $updated = $this->article_unconditional_discount_repository->update($dto);

            if ($updated > 0) {
                $this->article_event_logger->logChanges(
                    $dto->article_id,
                    self::ARTICLE_LOGGER_SCOPE,
                    $changes,
                    $this->current_user->entity()
                );
            }

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }
    }

    private function computeChanges(ArticleUnconditionalDiscountDto $dto): array
    {
        $data_old_value = $this->article_unconditional_discount_repository->findOneById($dto->article_id);

        $changes = [];
        $new_value = (int) $dto->amount;
        $old_value = 0 !== $data_old_value->article_id ? $data_old_value->amount : 0;

        if ($old_value !== $new_value) {
            $changes['amount'] = ['old' => $old_value, 'new' => $new_value];
        }

        return $changes;
    }

    /** @throws InternalErrorException */
    public function validateDto(ArticleUnconditionalDiscountDto $dto): void
    {
        $errors = $this->validator->validate($dto);

        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }
    }
}
