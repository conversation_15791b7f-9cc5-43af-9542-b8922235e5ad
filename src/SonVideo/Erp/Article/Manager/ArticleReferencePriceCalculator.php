<?php

namespace SonVideo\Erp\Article\Manager;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Article\Mysql\Repository\ArticlePriceRepository;
use SonVideo\Erp\PromoOffer\Entity\ActiveMarketingOperation;
use SonVideo\Erp\PromoOffer\Manager\PromoOfferManager;

class ArticleReferencePriceCalculator implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private ArticlePriceRepository $price_repository;
    private PromoOfferManager $promo_offer_manager;

    public function __construct(ArticlePriceRepository $price_repository, PromoOfferManager $promo_offer_manager)
    {
        $this->price_repository = $price_repository;
        $this->promo_offer_manager = $promo_offer_manager;
    }

    public function calculateNewReferencePrice(): int
    {
        $this->logger->notice('Find the active marketing operations');
        $products_with_marketing_operation = $this->extactProductsWithPromoOffer(
            $this->promo_offer_manager->getActiveMarketingOperations()
        );
        $this->logger->notice(
            sprintf('Found %s products with active marketing operation', count($products_with_marketing_operation))
        );

        $this->logger->notice('Calculate reference prices');

        return $this->price_repository->processReferencePriceUpdate($products_with_marketing_operation);
    }

    /**
     * Give the start date of marketing operations by product.
     *
     * @param ActiveMarketingOperation[]
     *
     * @return array<int, \DateTime>
     */
    private function extactProductsWithPromoOffer(array $marketing_operations): array
    {
        $products_with_marketing_operation = [];
        /** @var ActiveMarketingOperation $marketing_operation */
        foreach ($marketing_operations as $marketing_operation) {
            foreach ($marketing_operation->computed_article_ids as $product_id) {
                if (!isset($products_with_marketing_operation[$product_id])) {
                    $products_with_marketing_operation[$product_id] = new \DateTime();
                }

                if ($marketing_operation->start_at < $products_with_marketing_operation[$product_id]) {
                    $products_with_marketing_operation[$product_id] = $marketing_operation->start_at;
                }
            }
        }

        return $products_with_marketing_operation;
    }
}
