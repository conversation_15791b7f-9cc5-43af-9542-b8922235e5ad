<?php

namespace SonVideo\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderAwareInterface;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Article\Contract\ArticleRelatedContextDtoInterface;
use SonVideo\Erp\Article\Contract\ArticleUpdateContextDtoInterface;
use SonVideo\Erp\Article\Contract\ArticleUpdateRelatedRepositoryInterface;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleGeneralInformationUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleLogisticHavreUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleLogisticInformationUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticlePricesUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleSellingPlatformsUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleStatusUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleSuppliersUpdateContextDto;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Destock\Contract\DestockUpdateContextDtoInterface;
use SonVideo\Erp\Destock\Dto\UpdateContext\DestockGeneralInformationUpdateContextDto;
use SonVideo\Erp\Destock\Mysql\Repository\DestockRepository;
use SonVideo\Erp\Product\Contract\ProductUpdateContextDtoInterface;
use SonVideo\Erp\Product\Dto\UpdateContext\ProductGeneralInformationUpdateContextDto;
use SonVideo\Erp\Product\Mysql\Repository\ProductV2Repository;
use SonVideo\Erp\Referential\ArticleUpdateScope;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class ArticleManager implements DataLoaderAwareInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;
    use MapToEntityTrait;

    public const UPDATE_SCOPES = [
        ArticleUpdateScope::STATUS => [
            'permission' => UserPermission::ARTICLE_STATUS_WRITE,
            'dto_fqcn' => [ArticleStatusUpdateContextDto::class],
        ],
        ArticleUpdateScope::SUPPLIERS => [
            'permission' => UserPermission::ARTICLE_BUYERS_WRITE,
            'dto_fqcn' => [ArticleSuppliersUpdateContextDto::class],
        ],
        ArticleUpdateScope::PRICES => [
            'permission' => UserPermission::ARTICLE_PRICES_WRITE,
            'dto_fqcn' => [ArticlePricesUpdateContextDto::class],
        ],
        ArticleUpdateScope::GENERAL_INFORMATION => [
            'permission' => UserPermission::ARTICLE_GENERAL_INFORMATION_WRITE,
            'dto_fqcn' => [
                ArticleGeneralInformationUpdateContextDto::class,
                ProductGeneralInformationUpdateContextDto::class,
            ],
        ],
        ArticleUpdateScope::GENERAL_DESTOCK_INFORMATION => [
            'permission' => UserPermission::ARTICLE_DESTOCK_WRITE,
            'dto_fqcn' => [
                ArticleGeneralInformationUpdateContextDto::class,
                ProductGeneralInformationUpdateContextDto::class,
                DestockGeneralInformationUpdateContextDto::class,
            ],
        ],
        ArticleUpdateScope::GENERAL_DESTOCK_PRICES => [
            'permission' => UserPermission::ARTICLE_DESTOCK_WRITE,
            'dto_fqcn' => [ArticlePricesUpdateContextDto::class],
        ],
        ArticleUpdateScope::HAVRE => [
            'permission' => UserPermission::ARTICLE_LOGISTIC_WRITE,
            'dto_fqcn' => [ArticleLogisticHavreUpdateContextDto::class],
        ],
        ArticleUpdateScope::SELLING_PLATFORMS => [
            'permission' => UserPermission::ARTICLE_GENERAL_INFORMATION_WRITE,
            'dto_fqcn' => [ArticleSellingPlatformsUpdateContextDto::class],
        ],
        ArticleUpdateScope::LOGISTIC_INFORMATION => [
            'permission' => UserPermission::ARTICLE_LOGISTIC_WRITE,
            'dto_fqcn' => [ArticleLogisticInformationUpdateContextDto::class],
        ],
    ];

    private LegacyPdo $legacy_pdo;

    private ArticleEventLogger $article_event_logger;

    protected ArticleRepository $article_repository;

    private ProductV2Repository $product_repository;

    private DestockRepository $destock_repository;

    private SerializerInterface $serializer;

    private ArticleSalesChannelManager $article_sales_channel_manager;

    public function __construct(
        LegacyPdo $legacy_pdo,
        ArticleEventLogger $article_event_logger,
        ArticleRepository $article_repository,
        ProductV2Repository $product_repository,
        ArticleSalesChannelManager $article_sales_channel_manager,
        DestockRepository $destock_repository,
        SerializerInterface $serializer
    ) {
        $this->legacy_pdo = $legacy_pdo;
        $this->article_event_logger = $article_event_logger;
        $this->article_repository = $article_repository;
        $this->product_repository = $product_repository;
        $this->destock_repository = $destock_repository;
        $this->serializer = $serializer;
        $this->article_sales_channel_manager = $article_sales_channel_manager;
    }

    /**
     * @return ArticleRelatedContextDtoInterface[]
     *
     * @throws ExceptionInterface
     */
    public function createDtos(int $article_id, string $scope, array $data): array
    {
        $dtos = [];
        $data['article_id'] = $article_id;
        foreach (self::UPDATE_SCOPES[$scope]['dto_fqcn'] as $fqcn) {
            $dtos[] = $this->serializer->denormalize($data, $fqcn);
        }

        return $dtos;
    }

    /**
     * @params ArticleRelatedContextDtoInterface[] $dtos
     *
     * @throws \UnexpectedValueException
     * @throws \Exception
     * @throws NotFoundException
     * @throws ExceptionInterface
     */
    public function update(UserEntity $user, string $scope, array $dtos): int
    {
        $article_id = $dtos[0]->article_id;
        $changes = $this->computeChanges($article_id, $scope, $dtos);

        $nb_updated = 0;
        try {
            $this->legacy_pdo->beginTransaction();

            foreach ($dtos as $dto) {
                $data = $this->dtoToArray($dto);
                $nb_updated += $this->getDtoRepository($dto)->update($article_id, $data);
            }

            if ($nb_updated > 0) {
                if ('status' === $scope) {
                    $updated_article = $this->article_repository->getOneById($article_id, ['status']);
                    $changes['status']['new'] = $updated_article['status'];
                }
                $this->article_event_logger->logChanges($article_id, $scope, $changes, $user);
            }

            //  WARNING: This is temporary until downstream feeds are implemented and prices edit for sales channel moved to ERP ?
            //            if (ArticleUpdateScope::PRICES === $scope && array_key_exists('selling_price', $changes)) {
            //                $new_price = $changes['selling_price']['new'];
            //                $this->article_sales_channel_manager->updateAllSalesChannelWithPrice($article_id, $new_price);
            //            }

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $nb_updated;
    }

    /**
     * @params ArticleRelatedContextDtoInterface[] $dtos
     *
     * @throws ExceptionInterface
     * @throws NotFoundException
     */
    private function computeChanges(int $article_id, string $scope, array $dtos): array
    {
        $new_values = [];
        foreach ($dtos as $dto) {
            $new_values[] = $this->dtoToArray($dto);
        }
        $new_values = array_merge([], ...$new_values);

        $old_values = [];
        $old_dtos = $this->createDtos($article_id, $scope, $this->getCurrentValues($dtos));
        foreach ($old_dtos as $old_dto) {
            $old_values[] = $this->dtoToArray($old_dto);
        }
        $old_values = array_merge([], ...$old_values);

        $changes = [];
        foreach ($new_values as $column => $new_value) {
            $old_value = $old_values[$column];
            if ($old_value !== $new_value) {
                $changes[$column] = ['old' => $old_value, 'new' => $new_value];
            }
        }

        return $changes;
    }

    /**
     * @params ArticleRelatedContextDtoInterface[] $dtos
     *
     * @throws \Exception
     * @throws ExceptionInterface
     * @throws NotFoundException
     */
    private function getCurrentValues(array $dtos): array
    {
        $current_values = [];
        foreach ($dtos as $dto) {
            $data = $this->dtoToArray($dto);
            $current_values[] = $this->getDtoRepository($dto)->getOneById($dto->article_id, array_keys($data));
        }

        return array_merge([], ...$current_values);
    }

    /** @throws ExceptionInterface */
    private function dtoToArray(ArticleRelatedContextDtoInterface $dto): array
    {
        $data = $this->serializer->normalize($dto);
        unset($data['article_id']);

        return $data;
    }

    public static function getScopePermission(string $scope): ?string
    {
        if (!array_key_exists($scope, self::UPDATE_SCOPES)) {
            return null;
        }

        return self::UPDATE_SCOPES[$scope]['permission'];
    }

    /** @throws \Exception */
    private function getDtoRepository(ArticleRelatedContextDtoInterface $dto): ArticleUpdateRelatedRepositoryInterface
    {
        if ($dto instanceof ArticleUpdateContextDtoInterface) {
            return $this->article_repository;
        }
        if ($dto instanceof ProductUpdateContextDtoInterface) {
            return $this->product_repository;
        }
        if ($dto instanceof DestockUpdateContextDtoInterface) {
            return $this->destock_repository;
        }
        throw new \Exception(sprintf('No repository found for dto %s', get_class($dto)));
    }
}
