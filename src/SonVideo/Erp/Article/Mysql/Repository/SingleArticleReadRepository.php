<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Helper\MysqlEnumValuesExtractorTrait;
use SonVideo\Erp\Article\Entity\ArticlePriceEntity;
use SonVideo\Erp\Article\Entity\ArticleSummaryEntity;
use SonVideo\Erp\Article\Entity\ArticleV2Entity;
use SonVideo\Erp\Referential\ArticleStatus;
use SonVideo\Erp\Referential\PricingStrategyStatus;
use SonVideo\Erp\Referential\Product;

class SingleArticleReadRepository implements LegacyPdoAwareInterface, SerializerAwareInterface
{
    use LegacyPdoAwareTrait;
    use SerializerAwareTrait;
    use MysqlEnumValuesExtractorTrait;

    /**
     * @throws SqlErrorMessageException
     * @throws NotFoundException
     */
    public function isPackage(int $article_id): bool
    {
        $sql = <<<MYSQL
        SELECT a.compose = 1 AS is_package
        FROM backOffice.article a
        WHERE a.id_produit = $article_id
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchValue($sql, ['article_id' => $article_id]);
        if (0 === $this->legacy_readonly_pdo->foundRows()) {
            throw new NotFoundException(sprintf('Article not found with id "%d".', $article_id));
        }

        return (bool) $result;
    }

    /** @throws SqlErrorMessageException */
    public function isDestock(int $article_id): bool
    {
        $sql = <<<MYSQL
        SELECT EXISTS(SELECT 1 FROM backOffice.article a WHERE a.id_produit = :article_id AND a.stock_a_id_produit IS NOT NULL);
        MYSQL;

        $is_destock = $this->legacy_readonly_pdo->fetchValue($sql, ['article_id' => $article_id]);

        return (bool) $is_destock;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function getOneByIdOrSku(string $id_or_sku): ArticleV2Entity
    {
        $sql = <<<MYSQL
        SELECT
            a.id_produit AS article_id,
            p.reference AS sku,
            a.date_creation AS created_at,
            a.modif_date AS updated_at,
            IF(a.compose = 1, bpavc.status , a.status) AS status,
            bc.unbasketable_reason,
            a.V_quantite_stock AS stock,
            IF(a.compose = 1, backOffice.PDT_CMP_V_delai_lvr(a.id_produit), a.V_delai_lvr) AS delay,
            bc.article_url AS article_url,
            a.compose = 1 AS is_package,
            backOffice.PDT_ART_est_solde(a.id_produit) AS is_on_sale,
            a.modele AS name,
            a.description_courte AS short_description,
            a.description_panier AS basket_description,
            a.description_marketplace AS marketplace_description,
            a.date_embargo AS embargo_date,
            a.garantie_constructeur AS manufacturer_warranty_years,
            backOffice.PDT_ART_grt2(a.id_produit) AS has_warranty_extension_2_years,
            backOffice.PDT_ART_grt5(a.id_produit) AS has_warranty_extension_5_years,
            backOffice.PDT_ART_px_grt5(a.id_produit) AS warranty_extension_5_years_price,
            a.vendu_par AS packages,
            a.stock_a_id_produit IS NOT NULL AS is_destock,
            backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)) AS image,
            -- Selling platform
            a.comparateur AS comparator,
            -- Logistic
            JSON_OBJECT(
              'code_128', backOffice.PDT_code128C(p.id_produit),
              'weight', a.poids,
              'weight_tmp', a.poids_tmp,
              'number_of_packages', a.nombre_colis,
              'customs_code',IF(
                a.code_douanier IS NULL OR a.code_douanier = '',
                IF(cts.code_douanier IS NULL OR cts.code_douanier = '', ctc.code_douanier, cts.code_douanier),
                a.code_douanier
              ),
              'customs_code_origin', IF(
                a.code_douanier IS NULL OR a.code_douanier = '',
                IF(cts.code_douanier IS NULL OR cts.code_douanier = '', 'category', 'subcategory'),
                'article'
              ),
              'ecotax_code',IF(a.code_ecotaxe IS NULL OR a.code_ecotaxe = '', cts.code_ecotaxe, a.code_ecotaxe),
              'ecotax_code_origin', IF(a.code_ecotaxe IS NULL OR a.code_ecotaxe = '', 'subcategory', 'article'),
              'package_unit', a.conditionnement,
              'is_packaged', a.vente_lot,
              'source_country_id', a.id_pays_origine,
              'source_country_name', srcctry.pays,
              'rotations', JSON_OBJECT(
                '7_days',  a.rotation_7_jours,
                '30_days', a.rotation_30_jours,
                '90_days', a.rotation_90_jours
                )
              ) AS logistic,
            -- Supplier
            JSON_OBJECT(
              'supplier_id', a.id_fournisseur,
              'name', f.fournisseur,
              'supplier_reference', a.reference_fournisseur,
              'supplier_model', a.modele_constructeur,
              'mininum_order_quantity', a.mininum_order_quantity
              ) AS supplier,
            -- Subcategory
            JSON_OBJECT(
              'subcategory_id', p.id_souscategorie,
              'name', cts.souscategorie
              ) AS subcategory,
            -- Brand
            JSON_OBJECT(
              'brand_id', a.id_marque,
              'name', m.marque
              ) AS brand,
            -- Color
            JSON_OBJECT(
              'color_id', a.id_couleur,
              'code', c.code,
              'name', c.couleur,
              'image_url', c.url_image
              ) AS color,
            -- Havre
            JSON_OBJECT(
              'sku', a.reference_havre,
              'package_unit', a.cdt_havre,
              'quantity', a.quantite_havre,
              'is_active', a.stock_havre
              ) AS havre,
            -- Destock condition
            IF(
              ad.article_id IS NOT NULL,
              JSON_OBJECT(
                'internal_comment', ad.internal_comment,
                'public_description', ad.public_description,
                'state', ad.state,
                'origin', ad.origin,
                'defects', COALESCE(ad.defects, ''),
                'assignment', ad.assignment
              ),
              NULL
            )
            AS destock_condition
            FROM
            backOffice.article a
              LEFT JOIN backOffice.produit p ON a.id_produit = p.id_produit
              LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
              LEFT JOIN backOffice.couleur c ON a.id_couleur = c.id_couleur
              LEFT JOIN backOffice.CTG_TXN_souscategorie cts ON p.id_souscategorie = cts.id
              LEFT JOIN backOffice.CTG_TXN_categorie ctc ON ctc.id_categorie = p.V_id_categorie
              LEFT JOIN backOffice.fournisseur f ON a.id_fournisseur = f.id_fournisseur
              LEFT JOIN backOffice.pays srcctry ON a.id_pays_origine = srcctry.id_pays
              LEFT JOIN backOffice.batch_catalog bc ON a.id_produit = bc.article_id
              LEFT JOIN backOffice.BO_PDT_ART_V_compose bpavc ON a.id_produit = bpavc.id
              -- destock
              LEFT JOIN backOffice.article_destock ad ON a.id_produit = ad.article_id
              -- replaced by
              LEFT JOIN backOffice.article_replacement arnext ON arnext.id_replaced = a.id_produit
              LEFT JOIN backOffice.batch_catalog bcarnext ON arnext.id_replaced_by = bcarnext.article_id
              LEFT JOIN backOffice.produit parnext ON bcarnext.article_id = parnext.id_produit
              LEFT JOIN backOffice.article aarnext ON parnext.id_produit = aarnext.id_produit
              -- replaces
              LEFT JOIN backOffice.article_replacement arprev ON arprev.id_replaced_by = a.id_produit
              LEFT JOIN backOffice.batch_catalog bcarprev ON arprev.id_replaced = bcarprev.article_id
              LEFT JOIN backOffice.produit parprev ON bcarprev.article_id = parprev.id_produit
              LEFT JOIN backOffice.article aarprev ON parprev.id_produit = aarprev.id_produit
                     WHERE
              {condition} = :id_or_sku
          GROUP BY a.id_produit;
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchOne(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
        if (false === $result) {
            throw new NotFoundException(sprintf('Article not found with id or sku "%s".', $id_or_sku));
        }
        // MySQL JSON_ARRAYAGG does not support DISTINCT or GROUP BY
        // We fetch collection each in its own query
        $result['declinations'] = $this->loadDeclinations($id_or_sku) ?? '[]';
        $result['last_comment'] = $this->loadLastComment($id_or_sku);
        $result['eans'] = $this->loadEans($id_or_sku);
        $result['last_log_date'] = $this->loadLastLogDate($id_or_sku);
        $result['related_packages'] = $this->loadRelatedPackages($id_or_sku) ?? '[]';
        $result['replacements'] = $this->loadReplacements($id_or_sku);
        $result['initial_article'] = $this->loadInitialArticle($id_or_sku);
        $result['sales_channels'] = $this->loadSalesChannels($id_or_sku);
        $result['prices'] = $this->getArticlePrice($id_or_sku);

        /** @var ArticleV2Entity $article */
        $article = $this->serializer->denormalize($result, ArticleV2Entity::class);

        $destock_initial_id = $article->is_destock ? $article->initial_article['article_id'] : $article->article_id;
        $article->destocks = $this->loadDestocks($destock_initial_id) ?? [];

        $article->prices->selling_price_history = $this->getPriceHistory($article->article_id) ?? [];
        $article->prices->cost_adjustments = $this->getCostAdjustments($article->article_id) ?? [];

        if (isset($article->destock_condition['defects'])) {
            $article->destock_condition['defects'] = array_filter(explode(',', $article->destock_condition['defects']));
        }

        return $article;
    }

    private function loadDeclinations(string $id_or_sku): ?string
    {
        $sql = <<<MYSQL
        SELECT
        JSON_ARRAYAGG(
          JSON_OBJECT(
            'article_id', pdecl.id_produit,
            'sku', pdecl.reference,
            'short_description', adecl.description_courte,
            'image',
            backOffice.FORMAT_image_path_without_cdn(COALESCE(bcdecl.media_300_square_uri, bcdecl.media_largest_uri)),
            'status', adecl.status,
            'stock', adecl.V_quantite_stock,
            'delay', adecl.V_delai_lvr,
            'selling_price', adecl.prix_vente
            )
          ) AS declinations
        FROM
          backOffice.article a
            LEFT JOIN backOffice.produit p ON a.id_produit = p.id_produit
            LEFT JOIN backOffice.batch_catalog bc ON a.id_produit = bc.article_id
            LEFT JOIN backOffice.batch_catalog bcdecl ON bcdecl.common_content_id = bc.common_content_id AND bc.article_id <> bcdecl.article_id
            LEFT JOIN backOffice.produit pdecl ON bcdecl.article_id = pdecl.id_produit AND pdecl.reference NOT LIKE 'DESTOCK%'
            LEFT JOIN backOffice.article adecl ON pdecl.id_produit = adecl.id_produit
        WHERE
            {condition} = :id_or_sku
            AND pdecl.id_produit IS NOT NULL
        MYSQL;

        return $this->legacy_readonly_pdo->fetchValue(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
    }

    private function loadDestocks(int $article_id): ?array
    {
        $sql = <<<MYSQL
        SELECT
            pdest.id_produit AS article_id,
            pdest.reference AS sku,
            adest.description_courte AS short_description,
            adest.status AS status,
            backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)) AS image,
            adest.V_delai_lvr AS delay,
            adest.prix_vente AS selling_price
        FROM
          backOffice.article a
            LEFT JOIN backOffice.article adest ON adest.stock_a_id_produit = a.id_produit AND adest.V_quantite_stock != 0
            LEFT JOIN backOffice.produit pdest ON adest.id_produit = pdest.id_produit
            LEFT JOIN backOffice.batch_catalog bc ON adest.id_produit = bc.article_id
        WHERE
            a.id_produit = :article_id
            AND adest.id_produit IS NOT NULL
        MYSQL;

        $results = $this->legacy_readonly_pdo->fetchAll($sql, ['article_id' => $article_id]);

        return array_map(static function (array $result) {
            $result['article_id'] = (int) $result['article_id'];
            $result['delay'] = null === $result['delay'] ? null : (int) $result['delay'];

            return $result;
        }, $results);
    }

    private function loadInitialArticle(string $id_or_sku): ?string
    {
        $sql = <<<MYSQL
        SELECT
            IF(a.stock_a_id_produit IS NOT NULL,
            JSON_OBJECT(
                    'article_id', pdest.id_produit,
                    'sku', pdest.reference,
                    'short_description', adest.description_courte,
                    'status', adest.status,
                    'image', backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)),
                    'delay', adest.V_delai_lvr,
                    'selling_price', adest.prix_vente
            ) , NULL) AS row
        FROM
            backOffice.article a
                LEFT JOIN backOffice.produit p ON a.id_produit = p.id_produit
                LEFT JOIN backOffice.produit pdest ON a.stock_a_id_produit = pdest.id_produit
                LEFT JOIN backOffice.article adest ON pdest.id_produit = adest.id_produit
                LEFT JOIN backOffice.batch_catalog bc ON adest.id_produit = bc.article_id
        WHERE {condition} = :id_or_sku
        MYSQL;

        return $this->legacy_readonly_pdo->fetchValue(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
    }

    /** @throws SqlErrorMessageException */
    private function loadLastComment(string $id_or_sku): ?string
    {
        $sql = <<<MYSQL
        SELECT
        IF(c.id IS NOT NULL,
            JSON_OBJECT(
               'comment_id', c.id,
               'when', c.date_commentaire,
               'created_by', backOffice.GET_COMPUTED_USER_NAME_BY_ID(c.user_id),
               'content', c.commentaire
           ), NULL) AS last_comment
        FROM backOffice.article a
         INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
         LEFT JOIN backOffice.BO_PDT_ART_commentaire c ON a.id_produit = c.id_produit AND c.type = 'general' AND c.is_active > 0
        WHERE {condition} = :id_or_sku
        ORDER BY c.date_commentaire DESC
        LIMIT 1
        MYSQL;

        return $this->legacy_readonly_pdo->fetchValue(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
    }

    private function loadEans(string $id_or_sku): array
    {
        $sql = <<<MYSQL
        SELECT bcpae.ean
        FROM backOffice.article a
        INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
        INNER JOIN backOffice.BO_CTG_PDT_ART_ean bcpae ON a.id_produit = bcpae.BO_CTG_PDT_ART_article_id
        WHERE {condition} = :id_or_sku
        MYSQL;

        return $this->legacy_readonly_pdo->fetchCol(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
    }

    /** @throws SqlErrorMessageException */
    private function loadLastLogDate(string $id_or_sku): ?string
    {
        $sql = <<<MYSQL
        SELECT max(se.created_at) AS last_log_date
        FROM backOffice.article a
        INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
        LEFT JOIN backOffice.system_event se ON se.main_id = a.id_produit AND name LIKE 'article.%'
        WHERE {condition} = :id_or_sku
        GROUP BY a.id_produit
        MYSQL;

        return $this->legacy_readonly_pdo->fetchValue(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
    }

    private function loadRelatedPackages(string $id_or_sku): ?array
    {
        $sql = <<<MYSQL
        SELECT
            pc.id_compose AS package_id,
            pdt.reference AS sku,
            ac.description_courte AS short_description,
            backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)) AS image,
            ac.status,
            ac.V_quantite_stock AS stock,
            ac.prix_vente AS selling_price,
            backOffice.PDT_CMP_V_delai_lvr(ac.id_produit) AS delay
        FROM
            backOffice.produit p
                LEFT JOIN backOffice.produit_compose pc ON p.id_produit = pc.id_produit
                LEFT JOIN backOffice.produit pdt ON pc.id_compose = pdt.id_produit
                LEFT JOIN backOffice.article ac ON pc.id_compose = ac.id_produit
                LEFT JOIN backOffice.batch_catalog bc ON pc.id_compose = bc.article_id
        WHERE {condition} = :id_or_sku AND
              pc.id_produit IS NOT NULL
        ORDER BY
            CASE ac.status
                WHEN 'oui' THEN 1
                WHEN 'todo' THEN 2
                WHEN 'last' THEN 3
                ELSE 4
                END,
            ac.prix_vente DESC;
        MYSQL;

        $results = $this->legacy_readonly_pdo->fetchAll(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );

        return array_map(static function (array $result) {
            $result['package_id'] = (int) $result['package_id'];
            $result['selling_price'] = (float) $result['selling_price'];
            $result['stock'] = (int) $result['stock'];
            $result['delay'] = null === $result['delay'] ? null : (int) $result['delay'];

            return $result;
        }, $results);
    }

    /** @throws SqlErrorMessageException */
    private function loadReplacements(string $id_or_sku): ?string
    {
        $sql = <<<MYSQL
        SELECT
        -- Replacements
        JSON_OBJECT(
          'replaced_by', IF(parnext.id_produit IS NOT NULL, JSON_OBJECT(
            'article_id', parnext.id_produit,
            'sku', parnext.reference,
            'short_description', aarnext.description_courte,
            'image',
            backOffice.FORMAT_image_path_without_cdn(COALESCE(bcarnext.media_300_square_uri, bcarnext.media_largest_uri)),
            'status', aarnext.status,
            'stock', aarnext.V_quantite_stock,
            'delay', aarnext.V_delai_lvr,
            'selling_price', aarnext.prix_vente
            ), NULL),
          'replaces', IF(parprev.id_produit IS NOT NULL,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'article_id', parprev.id_produit,
                'sku', parprev.reference,
                'short_description', aarprev.description_courte,
                'image',
                backOffice.FORMAT_image_path_without_cdn(COALESCE(bcarprev.media_300_square_uri, bcarprev.media_largest_uri)),
                'status', aarprev.status,
                'stock', aarprev.V_quantite_stock,
                'delay', aarprev.V_delai_lvr,
                'selling_price', aarprev.prix_vente
              )
          ), JSON_ARRAY())
        ) AS replacements
        FROM
          backOffice.article a
          INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
          -- replaced by
          LEFT JOIN backOffice.article_replacement arnext ON arnext.id_replaced = a.id_produit
          LEFT JOIN backOffice.produit parnext ON arnext.id_replaced_by = parnext.id_produit
          LEFT JOIN backOffice.batch_catalog bcarnext ON arnext.id_replaced_by = bcarnext.article_id
          LEFT JOIN backOffice.article aarnext ON parnext.id_produit = aarnext.id_produit
          -- replaces
          LEFT JOIN backOffice.article_replacement arprev ON arprev.id_replaced_by = a.id_produit
          LEFT JOIN backOffice.produit parprev ON arprev.id_replaced = parprev.id_produit
          LEFT JOIN backOffice.batch_catalog bcarprev ON arprev.id_replaced = bcarprev.article_id
          LEFT JOIN backOffice.article aarprev ON parprev.id_produit = aarprev.id_produit
        WHERE {condition} = :id_or_sku
        MYSQL;

        return $this->legacy_readonly_pdo->fetchValue(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku]
        );
    }

    private function loadSalesChannels(string $id_or_sku): ?array
    {
        $sql = <<<MYSQL
        SELECT
                scp.sales_channel_id,
                sc.label as sales_channel,
                backOffice.can_sell_on_sales_channel(scp.product_id, scp.sales_channel_id) as can_sell_on_sales_channel,
                COALESCE(sc.average_commission_rate, 0) as average_commission_rate,
                COALESCE(sc.minimum_margin_rate, 0) as minimum_margin_rate,
                scp.selling_price,
                GROUP_CONCAT(ps.pricing_strategy_id) as pricing_strategy_ids
        FROM backOffice.produit p
         INNER JOIN backOffice.article a ON a.id_produit = p.id_produit
         INNER JOIN backOffice.sales_channel_product scp ON a.id_produit = scp.product_id
         INNER JOIN backOffice.sales_channel sc ON scp.sales_channel_id = sc.id
         LEFT JOIN backOffice.pricing_strategy_product psp ON p.id_produit = psp.product_id
         LEFT JOIN backOffice.pricing_strategy_sales_channel pssc ON pssc.sales_channel_id = sc.id AND psp.pricing_strategy_id = pssc.pricing_strategy_id
         LEFT JOIN backOffice.pricing_strategy ps ON pssc.pricing_strategy_id = ps.pricing_strategy_id AND ps.activation_status = :activated_status
        WHERE {condition} = :id_or_sku
        AND scp.is_active = 1
        GROUP BY sc.id
        ORDER BY sc.display_order, sc.label
        MYSQL;

        $results = $this->legacy_readonly_pdo->fetchAll(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
            ]),
            ['id_or_sku' => $id_or_sku, 'activated_status' => PricingStrategyStatus::ACTIVATED]
        );

        return array_map(static function (array $result) {
            $result['sales_channel_id'] = (int) $result['sales_channel_id'];
            $result['average_commission_rate'] = (float) $result['average_commission_rate'];
            $result['can_sell_on_sales_channel'] = (bool) $result['can_sell_on_sales_channel'];
            $result['minimum_margin_rate'] = (float) $result['minimum_margin_rate'];
            $result['selling_price'] = (float) $result['selling_price'];
            $result['pricing_strategy_ids'] = empty($result['pricing_strategy_ids'])
                ? []
                : array_map('intval', explode(',', $result['pricing_strategy_ids']));

            return $result;
        }, $results);
    }

    public function getArticlePrice(string $id_or_sku): ArticlePriceEntity
    {
        $sql = <<<MYSQL
        SELECT
          a.prix_vente AS selling_price,
          a.prix_vente / (1 + {vat}) AS selling_price_tax_excluded,
          p.V_marge AS margin_tax_excluded,
          p.V_taux_marque AS margin_rate,
          a.prix_vente_initial AS initial_selling_price,
          a.prix_vente_generalement_constate AS pvgc,
          a.prix_ecotaxe AS ecotax,
          a.prix_sorecop AS sorecop,
          a.prix_achat_net AS purchase_tax_excluded,
          a.prix_achat_pondere AS weighted_cost,
          backOffice.PDT_ART_px_vte_intragroupe(a.id_produit, 0.03) AS intragroup,
          a.prix_revendeur AS reseller_price,
          a.prix_achat_tarif AS tariff_tax_excluded,
          a.prix_achat_tarif * (1 + {vat}) AS tariff_tax_included,
          IF(apb.article_id IS NOT NULL,
             JSON_OBJECT(
               'amount', apb.amount,
               'start_at', apb.start_at,
               'end_at', apb.end_at
             ),
             NULL
          ) AS promo_budget,
          COALESCE(backOffice.PDT_ART_unconditional_discount(a.id_produit), 0) AS unconditional_discount,
          {vat} AS vat
          FROM
            backOffice.article a
              INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
              LEFT JOIN backOffice.article_promo_budget apb
              ON a.id_produit = apb.article_id AND NOW() BETWEEN apb.start_at AND apb.end_at
              LEFT JOIN backOffice.article_unconditional_discount aud ON a.id_produit = aud.article_id
          WHERE
            {condition} = :id_or_sku
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchOne(
            strtr($sql, [
                '{condition}' => $this->getCondition($id_or_sku),
                '{vat}' => Product::VAT_BASE,
            ]),
            ['id_or_sku' => $id_or_sku]
        );

        /** @var ArticlePriceEntity $result */
        $result = $this->serializer->denormalize($result, ArticlePriceEntity::class);

        return $result;
    }

    public function getPriceHistory(int $article_id): ?array
    {
        // see PDT_ART_launched_at_date and PDT_ART_launched_at_price functions
        $sql = <<<'MYSQL'
            SELECT
                DATE_FORMAT(MIN(mv.date_creation), '%Y-%m-%d') AS started_at,
                pbl.prix_vente AS selling_price,
                NULL AS declination_id
            FROM
                backOffice.mouvement_stock mv
                INNER JOIN backOffice.produit_bon_livraison pbl ON mv.id_produit = pbl.id_produit AND mv.id_bon_livraison = pbl.id_bon_livraison
                INNER JOIN backOffice.bon_livraison bl ON pbl.id_bon_livraison = bl.id_bon_livraison AND bl.id_transfert IS NULL
            WHERE
                mv.type =  'sortie'
                AND pbl.id_produit = :article_id
                AND pbl.prix_vente > 0
            GROUP BY pbl.prix_vente
            UNION
            SELECT
                DATE_FORMAT(MIN(mv.date_creation), '%Y-%m-%d') AS started_at,
                pbl.prix_vente AS selling_price,
                p.id_produit AS declination_id
            FROM
                backOffice.batch_catalog bc
                INNER JOIN backOffice.batch_catalog bc_decli ON bc.common_content_id = bc_decli.common_content_id AND bc_decli.article_id != bc.article_id
                INNER JOIN backOffice.produit p ON bc_decli.article_id = p.id_produit
                INNER JOIN backOffice.mouvement_stock mv ON bc_decli.article_id = mv.id_produit
                INNER JOIN backOffice.produit_bon_livraison pbl ON mv.id_produit = pbl.id_produit AND mv.id_bon_livraison = pbl.id_bon_livraison
                INNER JOIN backOffice.bon_livraison bl ON pbl.id_bon_livraison = bl.id_bon_livraison AND bl.id_transfert IS NULL
            WHERE
                mv.type =  'sortie'
                AND p.reference NOT LIKE 'DESTOCK%'
                AND pbl.prix_vente > 0
                AND bc.article_id = :article_id
            GROUP BY pbl.prix_vente
        MYSQL;

        $results = $this->legacy_readonly_pdo->fetchAll($sql, ['article_id' => $article_id]);

        return array_map(static function (array $result) {
            $result['selling_price'] = (float) $result['selling_price'];
            $result['declination_id'] = null === $result['declination_id'] ? null : (int) $result['declination_id'];

            return $result;
        }, $results);
    }

    private function getCostAdjustments(int $article_id): array
    {
        $sql = <<<MYSQL
        SELECT
            id,
            type,
            amount,
            created_at,
            updated_at,
            JSON_OBJECT(
              'id', created_by,
              'fullname', backOffice.GET_COMPUTED_USER_NAME_BY_ID(created_by)
              ) AS created_by,
            COALESCE(meta, JSON_OBJECT()) AS meta
        FROM
          backOffice.BO_STK_weighted_cost_adjustment
        WHERE
            article_id = :article_id
        MYSQL;

        $results = $this->legacy_readonly_pdo->fetchAll($sql, ['article_id' => $article_id]);

        return array_map(static function (array $result) {
            $result['id'] = (int) $result['id'];
            $result['amount'] = (float) $result['amount'];
            $result['meta'] = json_decode($result['meta'], null, 512, JSON_THROW_ON_ERROR);
            $result['created_by'] = json_decode($result['created_by'], null, 512, JSON_THROW_ON_ERROR);

            return $result;
        }, $results);
    }

    public function getLinkedPackagedArticlesSkus(int $article_id): array
    {
        $sql = <<<MYSQL
            SELECT p.reference, c.status
            FROM backOffice.compose c
              INNER JOIN backOffice.produit p
              INNER JOIN backOffice.produit_compose pc
            WHERE p.id_produit = c.id_produit
              AND p.id_produit = pc.id_compose
              AND pc.id_produit = :article_id
            ORDER BY FIELD(c.status, :status_yapu, :status_last, :status_oui) DESC
        MYSQL;

        return $this->legacy_readonly_pdo->fetchCol($sql, [
            'article_id' => $article_id,
            'status_yapu' => ArticleStatus::YAPU,
            'status_last' => ArticleStatus::LAST,
            'status_oui' => ArticleStatus::OUI,
        ]);
    }

    /** @return array<int> */
    public function getOngoingOrderIds(int $article_id): array
    {
        $sql = <<<MYSQL
          SELECT c.id_commande
          FROM backOffice.produit_commande pc
            INNER JOIN backOffice.commande c
          WHERE FIND_IN_SET('lvr_attente', c.V_statut_traitement) > 0
            AND pc.id_commande = c.id_commande AND pc.id_produit = :article_id
            AND c.creation_origine NOT IN ('ecranlounge.com', 'cilo.dk')
          GROUP BY pc.id_commande;
        MYSQL;

        $order_ids = $this->legacy_readonly_pdo->fetchCol($sql, ['article_id' => $article_id]);

        return array_map(static fn (string $order_id): int => (int) $order_id, $order_ids);
    }

    public function getSummaryById(int $article_id): ArticleSummaryEntity
    {
        $sql = <<<MYSQL
        SELECT
            a.id_produit AS article_id,
            p.reference AS sku,
            a.status AS status,
            a.description_panier AS basket_description
        FROM
            backOffice.article a
            LEFT JOIN backOffice.produit p ON a.id_produit = p.id_produit
        WHERE
            a.id_produit = :article_id
        MYSQL;

        $result = $this->legacy_readonly_pdo->fetchOne($sql, ['article_id' => $article_id]);

        return $this->serializer->denormalize($result, ArticleSummaryEntity::class);
    }

    /** @return array<string> */
    public function findActiveArticleSkus(): array
    {
        $sql = <<<SQL
        SELECT
          p.reference AS sku
        FROM
          backOffice.produit p
          INNER JOIN backOffice.article a ON p.id_produit = a.id_produit
        WHERE a.status in (:status_oui, :status_last);
        SQL;

        return array_column(
            $this->legacy_pdo->fetchObjects($sql, [
                'status_oui' => ArticleStatus::OUI,
                'status_last' => ArticleStatus::LAST,
            ]),
            'sku'
        );
    }

    private function getCondition(string $id_or_sku): string
    {
        return ctype_digit($id_or_sku) ? 'p.id_produit' : 'p.reference';
    }
}
