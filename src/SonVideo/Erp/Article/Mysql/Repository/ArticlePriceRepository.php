<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;

class ArticlePriceRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    /** @param array<int, \DateTime> $product_with_marketing_operations */
    public function processReferencePriceUpdate(array $product_with_marketing_operations = []): int
    {
        $this->createMarketingOperationTable($product_with_marketing_operations);

        $sql = <<<SQL
        UPDATE
            backOffice.sales_channel_product scp
        INNER JOIN (
            SELECT
                p.product_id,
                p.sales_channel_id,
                MIN(ph.selling_price) AS reference_price
            FROM (
                SELECT
                    p.product_id,
                    p.sales_channel_id,
                    p.search_period_start,
                    p.search_period_end,
                    c.current_price_id,
                    MAX(ph.sales_channel_price_history_id) AS active_price_id_at_search_start
                FROM (
                    SELECT
                        p.product_id,
                        p.sales_channel_id,
                        COALESCE(operation.start_at, NOW()) - INTERVAL 30 DAY AS search_period_start,
                        COALESCE(operation.start_at, NOW()) AS search_period_end
                    FROM backOffice.sales_channel_product p
                    LEFT JOIN tmp_marketing_operation operation
                      ON p.product_id = operation.product_id
                        AND p.sales_channel_id = operation.sales_channel_id
                ) p
                -- active price
                INNER JOIN (
                    SELECT
                        ph.product_id,
                        ph.sales_channel_id,
                        MAX(ph.sales_channel_price_history_id) AS current_price_id
                    FROM backOffice.sales_channel_price_history ph
                    GROUP BY ph.product_id, ph.sales_channel_id
                ) c ON p.product_id = c.product_id
                  AND p.sales_channel_id = c.sales_channel_id
                -- active price when period start
                LEFT JOIN backOffice.sales_channel_price_history ph
                    ON p.product_id = ph.product_id
                    AND p.sales_channel_id = ph.sales_channel_id
                    AND ph.activated_at < p.search_period_start
                GROUP BY p.product_id, p.sales_channel_id
            ) p
            INNER JOIN backOffice.sales_channel_price_history ph
                ON p.product_id = ph.product_id
                  AND p.sales_channel_id = ph.sales_channel_id
            WHERE (
                ph.activated_at >= p.search_period_start
                AND ph.activated_at < p.search_period_end
                AND p.current_price_id != ph.sales_channel_price_history_id -- ignore current price
            ) OR ph.sales_channel_price_history_id = p.active_price_id_at_search_start -- active price when period start
            GROUP BY p.product_id, p.sales_channel_id
        ) ph ON scp.product_id = ph.product_id
          AND scp.sales_channel_id = ph.sales_channel_id
        INNER JOIN backOffice.produit p ON scp.product_id = p.id_produit
        SET
            scp.reference_price = ph.reference_price,
            p.derniere_actualisation = IF(scp.reference_price != ph.reference_price, NOW(), p.derniere_actualisation)

        SQL;

        $this->legacy_pdo->beginTransaction();
        try {
            $lines_affected = $this->legacy_pdo->fetchAffected($sql);
            $this->legacy_pdo->commit();

            // 2 tables updated
            return $lines_affected / 2;
        } catch (\Exception $e) {
            $this->legacy_pdo->rollBack();
            throw $e;
        }
    }

    /** @param array<int, \DateTime> $product_with_marketing_operations */
    private function createMarketingOperationTable(array $product_with_marketing_operations = []): void
    {
        $this->legacy_pdo->fetchAffected(
            <<<'SQL'
            CREATE TEMPORARY TABLE backOffice.tmp_marketing_operation (
                `product_id` INT NOT NULL,
                `start_at` DATETIME NOT NULL,
                `sales_channel_id` INT NOT NULL,
                PRIMARY KEY (`product_id`)
            )
            SQL
        );

        $chunks = array_chunk($product_with_marketing_operations, 1000, true);
        while ($chunk = array_shift($chunks)) {
            $params = [];
            $values = [];

            $i = 0;
            foreach ($chunk as $product_id => $start_at) {
                $values[] = '(:product_id_' . $i . ', :start_at_' . $i . ', :sales_channel_id_' . $i . ')';
                $params['start_at_' . $i] = $start_at->format('Y-m-d H:i:s');
                $params['product_id_' . $i] = $product_id;
                $params['sales_channel_id_' . $i] = 1;
                ++$i;
            }

            $sql = strtr(
                <<<SQL
                INSERT INTO backOffice.tmp_marketing_operation(product_id, start_at, sales_channel_id) VALUES
                {values}
                SQL
                ,
                ['{values}' => implode(',' . PHP_EOL, $values)]
            );

            $this->legacy_pdo->fetchAffected($sql, $params);
        }
    }
}
