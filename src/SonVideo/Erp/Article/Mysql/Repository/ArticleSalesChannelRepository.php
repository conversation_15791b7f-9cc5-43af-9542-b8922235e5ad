<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Article\Entity\ArticleSalesChannelEntity;

class ArticleSalesChannelRepository implements LegacyPdoAwareInterface, LoggerAwareInterface, SerializerAwareInterface
{
    use LegacyPdoAwareTrait;
    use LoggerAwareTrait;
    use SerializerAwareTrait;

    /** @return ArticleSalesChannelEntity[] */
    public function findAllByArticleId(int $article_id): array
    {
        $sql = <<<SQL
            {base_sql}
            WHERE id = :article_comment_id
            AND scp.is_active
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        $rows = $this->legacy_readonly_pdo->fetchAll($sql, ['article_id' => $article_id]);

        return [] !== $rows ? $this->serializer->denormalize($rows, ArticleSalesChannelEntity::class . '[]') : [];
    }

    public function findById(int $article_id, int $sales_channel_id, bool $is_active = true): ?ArticleSalesChannelEntity
    {
        $sql = <<<SQL
            {base_sql}
            WHERE scp.product_id = :article_id
            AND scp.sales_channel_id = :sales_channel_id
            AND scp.is_active = :is_active
            ORDER BY sc.display_order, sc.label
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        $row = $this->legacy_readonly_pdo->fetchOne($sql, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
            'is_active' => $is_active,
        ]);

        return $row ? $this->serializer->denormalize($row, ArticleSalesChannelEntity::class) : null;
    }

    public function updateSellingPriceHistory(int $article_id, int $sales_channel_id, float $selling_price): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.sales_channel_price_history (sales_channel_id, product_id, selling_price, activated_at)
        VALUES (:sales_channel_id, :article_id, :selling_price, NOW());
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
            'selling_price' => $selling_price,
        ]);
    }

    public function updateSellingPriceWithTaxes(int $article_id, int $sales_channel_id, float $selling_price): int
    {
        $sql = <<<SQL
        UPDATE backOffice.sales_channel_product
        SET selling_price = :selling_price
        WHERE product_id = :article_id
        AND sales_channel_id = :sales_channel_id;
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
            'selling_price' => $selling_price,
        ]);
    }

    public function activate(int $article_id, int $sales_channel_id): int
    {
        $sql = <<<SQL
        UPDATE backOffice.sales_channel_product
        SET is_active = 1,
            activated_at = NOW()
        WHERE product_id = :article_id
        AND sales_channel_id = :sales_channel_id;
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
        ]);
    }

    public function disable(int $article_id, int $sales_channel_id): int
    {
        $sql = <<<SQL
        UPDATE backOffice.sales_channel_product
        SET is_active = 0,
            activated_at = null,
            updated_at = NOW()
        WHERE product_id = :article_id
        AND sales_channel_id = :sales_channel_id;
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
        ]);
    }

    public function insert(int $article_id, int $sales_channel_id): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.sales_channel_product (
                                                      sales_channel_id,
                                                      product_id,
                                                      reference_price,
                                                      is_active,
                                                      selling_price)
            SELECT
              :sales_channel_id AS sale_channel_id,
              a.id_produit AS product_id,
              NULL AS reference_price,
              1 AS is_active,
              COALESCE(scp.selling_price, a.prix_vente) As selling_price
              FROM
                backOffice.article a
                  LEFT JOIN backOffice.sales_channel_product scp
                    ON scp.product_id = a.id_produit
                    AND scp.sales_channel_id = 1
              WHERE a.id_produit = :article_id
            ON DUPLICATE KEY UPDATE is_active=1
            ;
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
        ]);
    }

    private function getBaseSql(): string
    {
        return <<<SQL
        SELECT
            scp.sales_channel_id,
            scp.product_id,
            scp.reference_price,
            COALESCE(scp.selling_price, 0) as selling_price,
            scp.is_active,
            sc.label as sales_channel_label,
            COALESCE(sc.average_commission_rate, 0.0) as average_commission_rate,
            COALESCE(sc.minimum_margin_rate, 0.0) as minimum_margin_rate,
            sc.minimum_selling_price,
            sc.maximum_selling_price,
            sc.maximum_selling_price,
            scp.created_at,
            scp.updated_at
        FROM backOffice.sales_channel_product scp
        LEFT JOIN  backOffice.sales_channel sc ON sc.id = scp.sales_channel_id
        SQL;
    }
}
