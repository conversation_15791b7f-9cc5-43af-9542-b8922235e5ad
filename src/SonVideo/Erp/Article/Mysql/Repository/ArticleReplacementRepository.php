<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use SonVideo\Erp\Article\Contract\ArticleReplacementCommandInterface;
use SonVideo\Erp\Article\Dto\ArticleReplacementCommandResultDto;

class ArticleReplacementRepository implements LegacyPdoAwareInterface, ArticleReplacementCommandInterface
{
    use LegacyPdoAwareTrait;

    private SerializerInterface $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    public function save(string $replaced_id_or_sku, string $replaced_by_id_or_sku): ArticleReplacementCommandResultDto
    {
        $result = $this->legacy_pdo->fetchValue('CALL backOffice.ARTICLE_REPLACEMENT_save(:replaced_by, :replaced)', [
            'replaced_by' => $replaced_by_id_or_sku,
            'replaced' => $replaced_id_or_sku,
        ]);

        return $this->serializer->deserialize($result, ArticleReplacementCommandResultDto::class, 'json');
    }

    public function remove(string $replaced_id_or_sku): ArticleReplacementCommandResultDto
    {
        $result = $this->legacy_pdo->fetchValue('CALL backOffice.ARTICLE_REPLACEMENT_delete(:replaced)', [
            'replaced' => $replaced_id_or_sku,
        ]);

        return $this->serializer->deserialize($result, ArticleReplacementCommandResultDto::class, 'json');
    }
}
