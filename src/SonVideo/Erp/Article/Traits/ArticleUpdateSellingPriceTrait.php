<?php

namespace SonVideo\Erp\Article\Traits;

use App\Exception\InternalServerErrorException;
use SonVideo\Erp\Article\Manager\ArticleEventLogger;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Referential\ArticleUpdateScope;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\User\Entity\UserEntity;

const SELLING_PRICE_COLUMN = 'selling_price';

trait ArticleUpdateSellingPriceTrait
{
    private CurrentUser $user;
    private ArticleRepository $article_repository;
    private ArticleEventLogger $article_event_logger;

    /** @throws InternalServerErrorException */
    public function updateArticleSellingPrice(
        int $article_id,
        float $old_selling_price,
        float $new_selling_price,
        UserEntity $user = null,
        bool $enable_log = true
    ): void {
        if (!$user instanceof UserEntity) {
            $user = $this->user->entity();
        }
        $nb_updated = $this->article_repository->update($article_id, [SELLING_PRICE_COLUMN => $new_selling_price]);
        if ($nb_updated > 0 && $enable_log) {
            $changes = [SELLING_PRICE_COLUMN => ['old' => $old_selling_price, 'new' => $new_selling_price]];
            $this->article_event_logger->logChanges($article_id, ArticleUpdateScope::SELLING_PRICE, $changes, $user);
        }
    }

    public function setUser(CurrentUser $user): void
    {
        $this->user = $user;
    }

    public function setArticleRepository(ArticleRepository $article_repository): void
    {
        $this->article_repository = $article_repository;
    }

    public function setArticleEventLogger(ArticleEventLogger $article_event_logger): void
    {
        $this->article_event_logger = $article_event_logger;
    }
}
