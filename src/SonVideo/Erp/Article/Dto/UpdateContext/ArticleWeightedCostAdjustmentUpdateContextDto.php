<?php

namespace SonVideo\Erp\Article\Dto\UpdateContext;

use App\DataLoader\Type\JsonType;
use OpenApi\Annotations as OA;

final class ArticleWeightedCostAdjustmentUpdateContextDto
{
    /** @OA\Property(description="Id de l'ajustement auquel appliquer la modification") */
    public int $id;

    /** @OA\Property(description="Id article lié à l'ajustement") */
    public int $article_id;

    /**
     * @OA\Property(description="Meta données", type="JsonType")
     *
     * @var array|JsonType
     */
    public array $meta;
}
