<?php

namespace SonVideo\Erp\Article\Dto\UpdateContext;

use App\Validator\Constraint as CustomAssert;
use SonVideo\Erp\Article\Contract\ArticleUpdateContextDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticleStatusUpdateContextDto implements ArticleUpdateContextDtoInterface
{
    public int $article_id;

    /**
     * @Assert\NotBlank()
     * @CustomAssert\ArticleStatus()
     */
    public string $status;
}
