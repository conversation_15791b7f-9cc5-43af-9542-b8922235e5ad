<?php

namespace SonVideo\Erp\Article\Dto\UpdateContext;

use App\DataLoader\Type\JsonType;
use App\Validator\Constraint as CustomAssert;
use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticlePlannedPriceUpdateContextDto
{
    /** @OA\Property(description="Id du prix de vente programmé auquel appliquer la modification") */
    public int $article_planned_price_id;

    /** @OA\Property(description="Id article lié à la plannifaction du prix de vente") */
    public int $article_id;

    /**
     * @OA\Property(description="Prix de vente", type="float")
     *
     * @Assert\Positive()
     * @CustomAssert\IsBelowPackagedArticles()
     * @CustomAssert\IsBelowArticlePVGC()
     */
    public float $selling_price;

    /**
     * @OA\Property(description="Prix de vente à la fin de la période")
     *
     * @Assert\Positive()
     * @CustomAssert\IsBelowPackagedArticles()
     * @CustomAssert\IsBelowArticlePVGC()
     */
    public float $exit_selling_price;

    /**
     * @OA\Property(description="Date de début de la période.")
     * @Assert\LessThan(propertyPath="ends_at", message="[key:starts_at_must_be_before_ends_at] The end date must be greater than the start date.")
     * @Assert\DateTime()
     */
    public \DateTimeInterface $starts_at;

    /**
     * @OA\Property(description="Date de fin de la période.")
     * @Assert\GreaterThan(propertyPath="starts_at", message="[key:starts_at_must_be_before_ends_at] The end date must be greater than the start date.")
     * @Assert\DateTime()
     */
    public \DateTimeInterface $ends_at;

    /** @OA\Property(description="Ids des canaux de vente où va s'appliquer le prix programmé") */
    /** @var string[]|JsonType */
    public array $sales_channel_ids = [];

    /**
     * @OA\Property(description="Date d'application.")
     * @Assert\DateTime()
     */
    public ?\DateTimeInterface $applied_at = null;
}
