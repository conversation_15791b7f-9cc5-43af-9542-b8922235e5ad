<?php

namespace SonVideo\Erp\Article\Dto;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticleUnconditionalDiscountDto
{
    /** @OA\Property(description="Id article de la remise inconditionnelle") */
    public int $article_id;

    /**
     * @OA\Property(description="Montant en pourcentage de la remise inconditionnelle")
     * @Assert\PositiveOrZero(message="[key:amount_must_be_positive] Amount must be positive : {{ value }}")
     * @Assert\LessThan(value = 100, message="[key:amount_to_high] Amount to high : {{ value }}%")
     */
    public float $amount;
}
