<?php

namespace SonVideo\Erp\Article\Dto\CreationContext;

use App\DataLoader\Type\JsonType;
use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticleWeightedCostAdjustmentCreationContextDto
{
    /** @OA\Property(description="Id article auquel appliquer la modification") */
    public int $article_id;

    /**
     * @OA\Property(description="Type de la modification du prix pondéré")
     *
     * @Assert\NotBlank()
     * @Assert\Choice({"devaluation", "supplier_credit_note"}, message="{{ value }} does not exists, available keys are : {{ choices }}")
     */
    public string $type;

    /**
     * @OA\Property(description="Montant de la remise ou du nouveau prix pondéré")
     *
     * @Assert\Positive()
     */
    public float $amount;

    /**
     * @OA\Property(description="Meta données", type="JsonType")
     *
     * @var array|JsonType
     */
    public array $meta = [];

    /** @OA\Property(description="Id utilisateur") */
    public int $created_by;
}
