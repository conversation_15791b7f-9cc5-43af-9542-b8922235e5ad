<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Carrier\Entity\ParcelTrackingEventEntity;

/**
 * Class ParcelTrackingReadRepository.
 */
class ParcelTrackingReadRepository extends AbstractLegacyRepository
{
    /** getAllAwaitingDeliveryUpdates */
    public function getAllAwaitingDeliveryUpdates(): array
    {
        $sql = <<<SQL
        SELECT
          bl.id_bon_livraison      AS delivery_note_id,
          c.id_colis               AS parcel_id,
          c.no_colis               AS tracking_number,
          cs.statut                AS delivery_status,
          cs.statut_date           AS status_date,
          c.transporteur = 'HAVRE' AS is_sent_from_havre,
          btpl.id                  AS shipment_method_id,
          btpl.code_produit        AS shipment_method_code,
          t.id_transporteur        AS carrier_id,
          t.code                   AS carrier_code,
          COALESCE(
            date(cs.created_at),
            date(now() - INTERVAL 1 DAY)
            )                      AS created_at,
          (
            SELECT max(ls.statut_date)
              FROM
                backOffice.colis_statut ls
              WHERE ls.id_colis = c.id_colis
            )                      AS last_status_date,
          (
            SELECT
              exists(
                SELECT ds.id_statut
                  FROM
                    backOffice.colis_statut ds
                  WHERE ds.statut IN ('livre', 'livre_retard')
                    AND ds.id_colis = c.id_colis)
            )                      AS is_delivered
          FROM
            backOffice.bon_livraison                            bl
              INNER JOIN backOffice.colis                       c ON bl.id_bon_livraison = c.id_bon_livraison
              LEFT JOIN  backOffice.colis_statut                cs ON c.id_colis = cs.id_colis
              INNER JOIN backOffice.BO_TPT_PDT_liste btpl ON backOffice.shipment_method_havre_to_champigny(bl.id_pdt_transporteur) = btpl.id
              INNER JOIN backOffice.transporteur     t ON btpl.transporteur_id = t.id_transporteur
          WHERE bl.date_validation BETWEEN NOW() - INTERVAL 31 DAY AND NOW()
          GROUP BY tracking_number
          HAVING (
              status_date = last_status_date
              OR status_date IS NULL
              OR last_status_date <> DATE(now())
            )
             AND (
              delivery_status NOT IN ('livre', 'casse', 'livre_retard')
              OR delivery_status IS NULL
            )
             AND is_delivered = 0
             AND created_at <> DATE(now())
          ORDER BY bl.date_validation DESC;
        SQL;

        return $this->legacy_pdo->fetchObjects($sql);
    }

    /** statusExists */
    public function statusExists(ParcelTrackingEventEntity $parcel_tracking_event_entity): bool
    {
        $data = $parcel_tracking_event_entity->toArray();
        $data['status_date'] = $parcel_tracking_event_entity->status_date->format('Y-m-d');

        $sql = <<<SQL
        SELECT
          EXISTS(
            SELECT id_statut
              FROM backOffice.colis_statut
              WHERE id_colis = :parcel_id
                AND statut = :status
                AND description = :description
            ) AS status_exist
        ;
        SQL;

        return '1' === $this->legacy_pdo->fetchOne($sql, $data)['status_exist'];
    }

    public function getColisIdFromColisNumber(string $no_colis): ?string
    {
        $sql = <<<SQL
        SELECT c.id_colis
        FROM colis c
        WHERE c.no_colis = :no_colis
        SQL;

        return $this->legacy_pdo->fetchValue($sql, ['no_colis' => $no_colis]);
    }
}
