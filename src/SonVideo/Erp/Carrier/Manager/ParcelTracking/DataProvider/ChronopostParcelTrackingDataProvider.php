<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\ParcelTracking\DataProvider;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use SonVideo\Erp\Carrier\Contract\ChronopostAwareTrait;
use SonVideo\Erp\Carrier\Contract\ClientAwareInterface;
use SonVideo\Erp\Carrier\Contract\ClientAwareTrait;
use SonVideo\Erp\Carrier\Contract\ParcelTrackingRetrieverInterface;
use SonVideo\Erp\Carrier\Entity\ParcelTrackingEventEntity;
use SonVideo\Erp\Carrier\Manager\ChronopostApiParameters;
use SonVideo\Erp\Carrier\ValueObject\RetrievedParcelTracking;

class ChronopostParcelTrackingDataProvider implements ParcelTrackingRetrieverInterface, DataLoaderAwareInterface, ClientAwareInterface
{
    use ChronopostAwareTrait;
    use DataLoaderAwareTrait;
    use ClientAwareTrait;

    private ChronopostApiParameters $chronopost_api_parameters;

    /** EnvoiDuNetStickerGenerator constructor. */
    public function __construct(ChronopostApiParameters $chronopost_api_parameters)
    {
        $this->chronopost_api_parameters = $chronopost_api_parameters;
    }

    /** {@inheritDoc} */
    public function retrieve($parcel): RetrievedParcelTracking
    {
        try {
            $result = $this->client
                ->init(
                    sprintf(
                        '%s%s',
                        $this->chronopost_api_parameters->getBaseUrl(),
                        ChronopostApiParameters::PARCEL_TRACKING_ENDPOINT_WDSL
                    )
                )
                ->trackSkybillV2([
                    'language' => 'fr_FR',
                    'skybillNumber' => $parcel->tracking_number,
                ]);

            if ($result->return->errorCode > 0) {
                throw new \Exception(sprintf('(%s) %s', $result->return->errorCode, $result->return->errorMessage));
            }

            // Still useful for debugging purpose
            $this->client->logRequestAndResponse();

            $retrieved = new RetrievedParcelTracking();

            // Chronopost did not add any event yet
            if (!isset($result->return->listEventInfoComp->events)) {
                return $retrieved;
            }

            // SOAP XML sucks:
            // - If there is only 1 result, the events key is not a collection, it just contains the key of the only event available
            // - otoh, if there are more than one event, then the result is an array collection with numerical indices
            // Code duplication/complexity FTW \o/
            if (isset($result->return->listEventInfoComp->events->eventDate)) {
                return $this->collect($result->return->listEventInfoComp->events, $parcel, $retrieved);
            }

            foreach ($result->return->listEventInfoComp->events as $event) {
                $this->collect($event, $parcel, $retrieved);
            }

            return $retrieved;
        } catch (\Exception $exception) {
            $this->client->logRequestAndResponse('error');

            throw $exception;
        }
    }

    protected function collect(
        $event,
        $parcel,
        RetrievedParcelTracking $retrieved_parcel_tracking
    ): RetrievedParcelTracking {
        // Only collect event more recent than last update
        if (strtotime($event->eventDate) >= strtotime($parcel->last_status_date)) {
            // Format description
            $description_prefix =
                strlen(trim((string) $event->zipCode)) > 0
                    ? trim(sprintf('%s %s', (string) $event->zipCode, (string) $event->officeLabel))
                    : '';

            $description =
                strlen($description_prefix) > 0
                    ? sprintf('%s - %s', $description_prefix, (string) $event->eventLabel)
                    : (string) $event->eventLabel;

            $retrieved_parcel_tracking->addEvent(
                $this->data_loader->hydrate(
                    [
                        'parcel_id' => $parcel->parcel_id,
                        'status' => 'D' === trim((string) $event->code) ? 'livre' : 'en cours',
                        'status_date' => (new \DateTime((string) $event->eventDate))->format('Y-m-d H:i:s'),
                        'description' => $description,
                    ],
                    ParcelTrackingEventEntity::class
                )
            );
        }

        return $retrieved_parcel_tracking;
    }
}
