<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager;

use Psr\Log\LoggerInterface;

/**
 * This holds credentials for Mondial Relay SOAP API.
 *
 * @see Documentation: https://www.mondialrelay.fr/media/122867/solution-web-service-v57.pdf
 */
class MondialRelayApiParameters
{
    public const STICKER_ENDPOINT = 'WSI2_CreationEtiquette';

    private string $mondial_relay_ws_url;

    private string $mondial_relay_ws_shop_identifier;

    private string $mondial_relay_ws_private_key;

    private LoggerInterface $logger;

    public function __construct(
        string $mondial_relay_ws_url,
        string $mondial_relay_ws_shop_identifier,
        string $mondial_relay_ws_private_key,
        LoggerInterface $logger
    ) {
        $this->mondial_relay_ws_url = $mondial_relay_ws_url;
        $this->mondial_relay_ws_shop_identifier = $mondial_relay_ws_shop_identifier;
        $this->mondial_relay_ws_private_key = $mondial_relay_ws_private_key;
        $this->logger = $logger;
    }

    public function getUrl(): string
    {
        return $this->mondial_relay_ws_url;
    }

    public function applyCredentialsOn(array $params): array
    {
        // Parameters order is important, Enseigne must be in the first place
        $params = array_merge(['Enseigne' => $this->mondial_relay_ws_shop_identifier], $params);

        $this->logger->debug('RAW PARAMS', $params);

        $concatenated = sprintf('%s%s', implode('', $params), $this->mondial_relay_ws_private_key);

        $this->logger->debug('CONCATENATED PARAMS', ['concatenated' => $concatenated]);

        $params['Security'] = strtoupper(md5($concatenated));

        return $params;
    }
}
