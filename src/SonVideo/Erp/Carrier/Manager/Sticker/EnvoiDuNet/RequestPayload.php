<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\Sticker\EnvoiDuNet;

use Adbar\Dot;
use SonVideo\Erp\Carrier\Contract\RequestPayloadInterface;
use SonVideo\Erp\Carrier\Entity\ShipmentEntity;
use SonVideo\Erp\Carrier\Manager\Sticker\PayloadFormatter;

/**
 * @see https://api.envoidunet.com/doc.html#op.idp157888
 */
class RequestPayload implements RequestPayloadInterface
{
    protected \stdClass $output;

    protected Dot $source_data;

    /** @var array */
    protected $products = [];

    /** StickerFormatter constructor. */
    public function __construct(ShipmentEntity $shipment)
    {
        $this->source_data = new Dot($shipment->toArray());
        $this->output = new \stdClass();
    }

    /** {@inheritDoc} */
    public function make(): array
    {
        $this->output->reference = $this->source_data['information.delivery_note_id'];
        $this->output->value = $this->source_data['information.total_delivery_fee_and_tax_excluded'];

        $this->output->label_type = 'PDF';
        $this->output->format = 'A6';
        $this->output->pod = true;

        $this->setCarrier()
            ->setFrom()
            ->setTo()
            ->setPackages();

        return PayloadFormatter::make($this->output, PayloadRules::CONFIG);
    }

    /**
     * setFrom.
     *
     * @return $this
     */
    protected function setFrom(): self
    {
        $from = new \stdClass();

        $from->company = $this->source_data['sender.company_name'];
        $from->firstname = $this->source_data['sender.name'];
        $from->address1 = $this->source_data['sender.address_line1'];
        $from->postcode = $this->source_data['sender.zip_code'];
        $from->city = $this->source_data['sender.city'];
        $from->country = $this->source_data['sender.country_code'];
        $from->email = $this->source_data['sender.email'];
        $from->phone = $this->source_data['sender.phone'];

        $this->output->from = $from;

        return $this;
    }

    /**
     * setTo.
     *
     * @return $this
     */
    protected function setTo(): self
    {
        $to = new \stdClass();
        $to->company = $this->source_data['recipient.company_name'];
        $to->firstname = $this->source_data['recipient.first_name'];
        $to->lastname = $this->source_data['recipient.last_name'];
        $to->address1 = $this->source_data['recipient.address_line1'];
        $to->address2 = $this->source_data['recipient.address_line2'];
        $to->address3 = $this->source_data['recipient.address_line3'];
        $to->postcode = $this->source_data['recipient.zip_code'];
        $to->city = $this->source_data['recipient.city'];
        $to->country = $this->source_data['recipient.country_code'];
        $to->email = $this->source_data['recipient.email'];
        $to->phone = $this->source_data['recipient.mobile'];

        if ('LU' === $this->source_data['recipient.country_code']) {
            $to->postcode = str_replace(['L-', 'l-', 'L', 'l'], '', $to->postcode);
        }

        $this->output->to = $to;

        return $this;
    }

    /**
     * setCarrier.
     *
     * @return $this
     */
    protected function setCarrier(): self
    {
        switch ($this->source_data['information.shipment_method_code']) {
            case 'I1':
                $carrier = 'dhl';
                break;
            case 'F1':
                $carrier = 'dhl_france';
                break;
            // On 08/11/2018 the API returned: "Ce transporteur n'est pas activé sur votre compte"
            // I'll leave uncommented in order to have the error sent back to client when/if it is activated
            case 'FE':
                $carrier = 'fedexeco';
                break;
            case '13':
                $carrier = 'chronopost';
                break;
            case 'RL':
            case 'XL':
            case 'XX':
                $carrier = 'mondialrelay';
                if (null === $this->source_data['recipient.relay_id']) {
                    throw new \InvalidArgumentException(sprintf('Relay ID must be provided with Mondial Relay shipment method: %s', $this->source_data['information.shipment_method_code']));
                }

                $this->output->relay_id = $this->source_data['recipient.relay_id'];
                break;
            default:
                throw new \InvalidArgumentException(sprintf('Shipment method code "%s" is invalid', $this->source_data['information.shipment_method_code']));
        }

        $this->output->carrier = $carrier;

        return $this;
    }

    /**
     * setPackages.
     *
     * @return $this
     */
    protected function setPackages(): self
    {
        $packages = [];
        foreach ($this->source_data['parcels'] as $parcel) {
            $package_products = [];
            foreach ($parcel['products'] as $product) {
                $item = new \stdClass();

                $item->name = $product['description'];
                $item->price = $product['price_net_excluding_tax'];
                $item->qty = $product['quantity'];
                $item->weight = number_format($product['weight'], 2);

                $item->hscode = $product['customs_code'];
                $item->country_orig = $product['origin_country_code'];

                $package_products[] = $item;
            }

            $package = new \stdClass();
            $package->quantity = 1;
            $package->weight = number_format($parcel['weight'], 2);
            $package->products = $package_products;

            $packages[] = $package;
        }

        if (true === $this->source_data['information.use_insurance']) {
            $packages[0]->insurance =
                $this->source_data['information.expedition_insurance_value'] ??
                $this->source_data['information.total_delivery_fee_and_tax_excluded'];
        }

        $this->output->packages = $packages;

        return $this;
    }
}
