<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\Sticker\Chronopost;

final class PayloadRules
{
    public const CONFIG = [
        'headerValue.accountNumber' => ['required'],

        'shipperValue.shipperAdress1' => ['default' => '', 'max_length' => 100],
        'shipperValue.shipperAdress2' => ['optional', 'max_length' => 100],
        'shipperValue.shipperCity' => ['default' => '', 'max_length' => 50],
        'shipperValue.shipperCivility' => [
            'default' => 'M',
            'one_of' => ['E', 'L', 'M'],
        ],
        'shipperValue.shipperContactName' => [
            'default' => '',
            'max_length' => 100,
        ],
        'shipperValue.shipperCountry' => [
            'default' => 'FR',
            'allowed_fixed_lengths' => [2],
        ],
        'shipperValue.shipperCountryName' => ['default' => ''],
        'shipperValue.shipperEmail' => ['default' => '', 'max_length' => 80],
        'shipperValue.shipperMobilePhone' => ['optional', 'max_length' => 17],
        'shipperValue.shipperName' => ['default' => '', 'max_length' => 100],
        'shipperValue.shipperName2' => ['optional', 'max_length' => 100],
        'shipperValue.shipperPhone' => ['numeric', 'default' => '', 'max_length' => 17],
        'shipperValue.shipperPreAlert' => ['default' => 0, 'one_of' => [0, 11]],
        'shipperValue.shipperZipCode' => ['default' => '', 'max_length' => 9],

        'customerValue.customerAdress1' => [
            'default' => '',
            'max_length' => 38,
        ],
        'customerValue.customerAdress2' => ['optional', 'max_length' => 38],
        'customerValue.customerCity' => ['default' => '', 'max_length' => 50],
        'customerValue.customerCivility' => [
            'default' => 'M',
            'one_of' => ['E', 'L', 'M'],
        ],
        'customerValue.customerContactName' => [
            'default' => '',
            'max_length' => 100,
        ],
        'customerValue.customerCountry' => [
            'default' => 'FR',
            'allowed_fixed_lengths' => [2],
        ],
        'customerValue.customerCountryName' => ['default' => ''],
        'customerValue.customerEmail' => ['default' => '', 'max_length' => 80],
        'customerValue.customerMobilePhone' => ['optional', 'max_length' => 17],
        'customerValue.customerName' => ['default' => '', 'max_length' => 100],
        'customerValue.customerName2' => ['optional', 'max_length' => 100],
        'customerValue.customerPhone' => ['numeric', 'default' => '', 'max_length' => 50],
        'customerValue.customerPreAlert' => [
            'default' => 0,
            'one_of' => [0, 1],
        ],
        'customerValue.customerZipCode' => ['default' => '', 'max_length' => 9],
        'customerValue.printAsSender' => [
            'default' => 'N',
            'one_of' => ['Y', 'N'],
        ],

        'recipientValue.recipientAdress1' => [
            'default' => '',
            'max_length' => 38,
        ],
        'recipientValue.recipientAdress2' => ['optional', 'max_length' => 38],
        'recipientValue.recipientCity' => ['default' => '', 'max_length' => 50],
        'recipientValue.recipientContactName' => [
            'default' => '',
            'max_length_per_word' => 28,
            'max_length' => 100,
        ],
        'recipientValue.recipientCountry' => [
            'default' => 'FR',
            'allowed_fixed_lengths' => [2],
        ],
        'recipientValue.recipientCountryName' => ['default' => ''],
        'recipientValue.recipientEmail' => [
            'default' => '',
            'max_length' => 80,
        ],
        'recipientValue.recipientMobilePhone' => ['optional', 'max_length' => 17],
        'recipientValue.recipientName' => [
            'default' => '',
            'max_length_per_word' => 25,
            'max_length' => 100,
        ],
        'recipientValue.recipientPhone' => [
            'default' => '0111111111',
            'max_length' => 17,
        ],
        'recipientValue.recipientPreAlert' => [
            'default' => 0,
            'one_of' => [0, 22],
        ],
        'recipientValue.recipientZipCode' => [
            'default' => '',
            'max_length' => 9,
        ],
        'recipientValue.recipientName2' => [
            'default' => '',
            'max_length_per_word' => 25,
            'max_length' => 100,
        ],

        'refValue.shipperRef' => ['default' => '', 'max_length' => 35],
        'refValue.customerSkybillNumber' => [
            'default' => '',
            'max_length' => 15,
        ],
        'refValue.idRelais' => ['default' => '', 'max_length' => 5],
        'refValue.recipientRef' => ['default' => '', 'max_length' => 35],

        'skybillValue.productCode' => ['default' => '', 'max_length' => 2],
        'skybillValue.shipDate' => ['required'],
        'skybillValue.shipHour' => ['default' => '', 'max_length' => 2],
        'skybillValue.weight' => ['float'],
        'skybillValue.content1' => ['default' => '', 'max_length' => 45],
        'skybillValue.weightUnit' => ['default' => 'KGM', 'required'],
        'skybillValue.service' => [
            'default' => 0,
            'allowed_fixed_lengths' => [3, 1],
        ],
        'skybillValue.objectType' => [
            'default' => 'MAR',
            'one_of' => ['DOC', 'MAR'],
        ],
        'skybillValue.as' => [
            'default' => '',
            'one_of' => ['', 'A02'],
        ],

        'skybillParamsValue.duplicata' => [
            'default' => 'N',
            'one_of' => ['Y', 'N'],
        ],
        'skybillParamsValue.mode' => ['default' => 'PDF'],
        'skybillParamsValue.withReservation' => ['integer'],

        'password' => ['default' => '', 'max_length' => 6],
        'modeRetour' => ['required'],
        'numberOfParcel' => ['integer'],
        'version' => ['integer', 'max_length' => 1],
        'multiParcel' => ['default' => '', 'max_length' => 1],
    ];
}
