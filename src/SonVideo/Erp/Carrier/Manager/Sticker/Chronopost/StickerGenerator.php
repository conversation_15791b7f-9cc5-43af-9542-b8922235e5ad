<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\Sticker\Chronopost;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Carrier\Client\ChronopostClient;
use SonVideo\Erp\Carrier\Contract\CarrierInterface;
use SonVideo\Erp\Carrier\Contract\ChronopostAwareTrait;
use SonVideo\Erp\Carrier\Contract\ClientAwareInterface;
use SonVideo\Erp\Carrier\Contract\ClientAwareTrait;
use SonVideo\Erp\Carrier\Contract\StickerGeneratorInterface;
use SonVideo\Erp\Carrier\Entity\ShipmentEntity;
use SonVideo\Erp\Carrier\Manager\ChronopostApiParameters;
use SonVideo\Erp\Carrier\ValueObject\GeneratedSticker;

class StickerGenerator implements CarrierInterface, StickerGeneratorInterface, LoggerAwareInterface, ClientAwareInterface
{
    use ChronopostAwareTrait;
    use ClientAwareTrait;
    use LoggerAwareTrait;

    /** @var ChronopostClient */
    protected $client;

    private ChronopostApiParameters $chronopost_api_parameters;

    protected GeneratedSticker $generated_sticker;

    /** EnvoiDuNetStickerGenerator constructor. */
    public function __construct(ChronopostApiParameters $chronopost_api_parameters, GeneratedSticker $generated_sticker)
    {
        $this->generated_sticker = $generated_sticker;
        $this->chronopost_api_parameters = $chronopost_api_parameters;
    }

    /** {@inheritDoc} */
    public function generate(ShipmentEntity $shipment): GeneratedSticker
    {
        $this->logger->info('CLIENT CREDENTIALS', [$this->chronopost_api_parameters->getAccount()]);

        // Prepare data for API call
        $request_payload = (new RequestPayload($shipment, $this->chronopost_api_parameters->getAccount()))->make();
        $this->logger->info('REQUEST PAYLOAD', $request_payload);

        try {
            $url_end_point = sprintf(
                '%s%s',
                $this->chronopost_api_parameters->getBaseUrl(),
                ChronopostApiParameters::STICKER_GENERATOR_ENDPOINT_WDSL
            );

            // generate the sticker on chronopost webservice
            $result = $this->client->init($url_end_point)->shippingV6($request_payload);

            if ($result->return->errorCode > 0) {
                throw new \Exception(sprintf('An error occurred: (%s) %s', $result->return->errorCode, $result->return->errorMessage));
            }

            // Still useful for debugging purpose
            $this->client->logRequestAndResponse();

            $this->generated_sticker
                ->createOrUpdate($this->getName($shipment, null), $result->return->skybill)
                // Format tracking numbers (must be set for each parcel)
                ->attachTrackingNumbers($shipment->parcels, [(string) $result->return->skybillNumber]);
        } catch (\Exception $exception) {
            if (null !== $this->client) {
                $this->client->logRequestAndResponse('error');
            }

            throw $exception;
        }

        return $this->generated_sticker;
    }

    /** {@inheritDoc} */
    public function getName(ShipmentEntity $shipment, ?string $suffix): string
    {
        return $shipment->information->delivery_note_id;
    }
}
