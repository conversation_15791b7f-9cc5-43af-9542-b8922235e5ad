<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\Sticker;

use SonVideo\Erp\Carrier\Entity\ShipmentEntity;
use Stringy\Stringy;

class Utility
{
    private const SENDER_FORMATTING_RULES = [
        'upper' => ['name'],
        'sanitize' => ['address_line1', 'address_line2'],
        'phone' => ['phone'],
        'city' => ['city'],
    ];

    private const RECIPIENT_FORMATTING_RULES = [
        'upper' => ['name'],
        'sanitize' => [
            'first_name',
            'last_name',
            'address_line1',
            'address_line2',
            'address_line3',
            'address_line4',
            'company_name',
            'relay_name',
            'relay_address',
        ],
        'phone' => ['phone', 'mobile'],
        'city' => ['city', 'relay_city'],
    ];

    /** format */
    public static function format(ShipmentEntity $shipment): ShipmentEntity
    {
        $data = $shipment->toArray();

        $sender = self::applyFormattingRules($data['sender'], self::SENDER_FORMATTING_RULES);
        $recipient = self::applyFormattingRules($data['recipient'], self::RECIPIENT_FORMATTING_RULES);

        // Reassign only the formatted keys
        $shipment->fromArray(['sender' => $sender, 'recipient' => $recipient]);

        return $shipment;
    }

    /**
     * applyFormattingRules.
     *
     * @param $items
     */
    public static function applyFormattingRules(array $items, array $rule): array
    {
        foreach ($items as $key => $item) {
            if (null === $item) {
                continue;
            }

            if (in_array($key, $rule['upper'])) {
                $items[$key] = self::upperCase($item);
            }

            if (in_array($key, $rule['sanitize'])) {
                $items[$key] = self::sanitize($item);
            }

            if (in_array($key, $rule['phone'])) {
                $items[$key] = self::formatPhone($item);
            }

            if (in_array($key, $rule['city'])) {
                $items[$key] = self::sanitizeCity($item);
            }
        }

        return $items;
    }

    /**
     * upperCase.
     *
     * @throws \InvalidArgumentException
     */
    public static function upperCase(?string $str, string $encoding = null): string
    {
        $str_upper = Stringy::create($str, $encoding)
            ->toAscii(false)
            ->toUpperCase();

        return (string) $str_upper;
    }

    /**
     * sanitize.
     *
     * @throws \RuntimeException
     */
    public static function sanitize(?string $str): string
    {
        // Replace all hyphens by spaces
        $str = str_replace('-', ' ', $str);

        // Replace all consecutive spaces by 1 space
        $str = trim(preg_replace('/\s\s+/', ' ', $str));

        $formatted = \transliterator_transliterate(
            'Any-Latin; Latin-ASCII; [:Nonspacing Mark:] Remove; [:Punctuation:] Remove; Upper();',
            $str
        );

        if (false === $formatted) {
            throw new \RuntimeException('Could not sanitize : ' . $str);
        }

        return trim($formatted);
    }

    /**
     * formatCity.
     *
     * @throws \InvalidArgumentException
     */
    public static function sanitizeCity(?string $city): string
    {
        $formatted = self::sanitize($city);

        // Replace (double)quotes, commas by space
        $formatted = str_replace(['"', "'", ',', '-', '.'], ' ', $formatted);

        // Replace some expressions by others
        $formatted = str_replace(['SAINT ', 'SAINTE '], ['ST ', 'STE '], $formatted);

        // Remove some expressions
        $formatted = str_replace('CEDEX', '', $formatted);

        return $formatted;
    }

    public static function formatPhone(?string $str): string
    {
        return str_replace(' ', '', $str);
    }
}
