<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\Sticker;

use Adbar\Dot;

class PayloadFormatter
{
    /**
     * format.
     *
     * @param $request_payload
     */
    public static function make($request_payload, array $config): array
    {
        $payload = new Dot(
            json_decode(json_encode($request_payload, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR)
        );

        foreach ($config as $key => $rules) {
            foreach ($rules as $name => $value) {
                if ('optional' === $value && !$payload->has($key)) {
                    break;
                }

                if ('optional' === $value && self::isEmpty($payload[$key])) {
                    unset($payload[$key]);
                    break;
                }

                if ('required' === $value && !$payload->has($key)) {
                    $payload[$key] = '';

                    continue;
                }

                if ('default' === $name && (!$payload->has($key) || self::isEmpty($payload[$key]))) {
                    if (!$payload->has($key) || self::isEmpty($payload[$key])) {
                        $payload[$key] = $value;

                        continue;
                    }

                    if (isset($rules['one_of']) && !in_array($payload[$key], $rules['one_of'])) {
                        $payload[$key] = $value;

                        continue;
                    }

                    if (
                        isset($rules['allowed_fixed_lengths']) &&
                        !in_array(strlen($payload[$key]), $rules['allowed_fixed_lengths'])
                    ) {
                        $payload[$key] = $value;

                        continue;
                    }
                }

                if ('max_length' === $name) {
                    $payload[$key] = substr($payload[$key], 0, $value);

                    continue;
                }

                if ('numeric' === $value) {
                    $payload[$key] = (int) $payload[$key];

                    continue;
                }

                if ('float' === $value) {
                    $payload[$key] = (float) $payload[$key];

                    continue;
                }

                if ('max_length_per_word' === $name) {
                    preg_match_all('/(?<words>\S+)/mi', $payload[$key], $matches);

                    $formatted = [];
                    foreach ($matches['words'] as $word) {
                        $formatted[] = substr($word, 0, $value);
                    }

                    $payload[$key] = implode(' ', $formatted);
                }
            }
        }

        return $payload->all();
    }

    public static function isEmpty($var): bool
    {
        return empty($var) && '0' !== (string) $var;
    }
}
