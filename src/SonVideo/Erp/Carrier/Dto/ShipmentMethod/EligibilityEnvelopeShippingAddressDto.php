<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Dto\ShipmentMethod;

use App\Validator\Constraint as CustomAssert;
use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

class EligibilityEnvelopeShippingAddressDto
{
    /**
     * @OA\Property(example="M.")
     *
     * @Assert\NotBlank()
     * @Assert\Choice({"M.", "Mme"})
     */
    public string $title;

    /**
     * @OA\Property(example="Gerard")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     */
    public string $firstname;

    /**
     * @OA\Property(example="Manvussa")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     */
    public string $lastname;

    /**
     * @OA\Property(example="**********")
     *
     * @Assert\Type("string")
     */
    public string $cellphone;

    /**
     * @OA\Property(example="Nantes")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     */
    public string $city;

    /**
     * @OA\Property(example="44100")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     */
    public string $postal_code;

    /**
     * @OA\Property(example="38 rue de la ville en bois")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     */
    public string $address;

    /**
     * ISO 3166-1 alpha-2 codes are two-letter country codes defined in ISO 3166-1.
     *
     * @OA\Property(example="FR")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     * @CustomAssert\CountryCode()
     */
    public string $country_code;
}
