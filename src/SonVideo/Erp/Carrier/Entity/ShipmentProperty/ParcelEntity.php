<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Entity\ShipmentProperty;

use App\Entity\AbstractEntity;

class ParcelEntity extends AbstractEntity
{
    public int $parcel_id;

    public int $number;

    public ?string $tracking_number = null;

    public string $status;

    public float $weight;

    public ?float $updated_weight = null;

    /** @var ParcelProductEntity[] */
    public array $products = [];
}
