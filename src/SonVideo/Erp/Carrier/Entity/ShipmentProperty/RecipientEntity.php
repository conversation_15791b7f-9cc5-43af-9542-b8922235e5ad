<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Entity\ShipmentProperty;

use App\Entity\AbstractEntity;
use Symfony\Component\Validator\Constraints as Assert;

class RecipientEntity extends AbstractEntity
{
    public string $civility;

    /** @Assert\NotBlank(message="Le prénom est manquant") */
    public string $first_name;

    /** @Assert\NotBlank(message="Le nom est manquant") */
    public string $last_name;

    /** @Assert\NotBlank(message="L'adresse est manquante") */
    public ?string $address_line1 = null;

    public ?string $address_line2 = null;

    public ?string $address_line3 = null;

    public ?string $address_line4 = null;

    /** @Assert\NotBlank(message="Le code postal est manquant") */
    public string $zip_code;

    public ?string $region_code = null;

    /** @Assert\NotBlank(message="La ville est manquante") */
    public string $city;

    public string $country;

    /** @Assert\NotBlank(message="Le pays est manquant") */
    public string $country_name;

    public string $country_code;

    public ?string $phone = null;

    /** @Assert\NotBlank(message="Le numéro de mobile est manquant") */
    public ?string $mobile = null;

    /** @Assert\NotBlank(message="L'adresse email est manquante") */
    public ?string $email = null;

    public ?string $company_name = null;

    public ?bool $use_relay = null;

    public ?string $relay_id = null;

    public ?string $relay_name = null;

    public ?string $relay_type = null;

    public ?string $relay_address = null;

    public ?string $relay_postal_code = null;

    public ?string $relay_city = null;

    public ?string $relay_country = null;

    public ?string $relay_distribution_sort = null;

    public ?string $relay_batch = null;

    public ?string $relay_area = null;

    public string $shipment_method_code;

    public ?string $time_slot_start_date = null;

    public ?string $time_slot_end_date = null;

    public ?string $service_code = null;

    public ?string $time_slot_tariff_level = null;
}
