<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity;

use App\DataLoader\Type\CommaSeparatedListType;
use App\Entity\AbstractEntity;

/**
 * Class LocationProductEntity.
 */
class LocationProductEntity extends AbstractEntity
{
    public int $product_id;

    public string $sku;

    public string $brand;

    public string $model;

    public bool $countable_manually;

    /** @var CommaSeparatedListType|null */
    public ?array $eans = [];

    public int $package_number;
}
