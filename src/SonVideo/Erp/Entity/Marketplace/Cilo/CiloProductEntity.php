<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity\Marketplace\Cilo;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

class CiloProductEntity extends AbstractEntity
{
    public string $sku;

    public float $prix;

    public float $prix_cilo_ht;

    public float $prix_svd_ttc;

    public float $frais_port;

    public string $modele;

    public ?string $ean13 = null;

    public string $marque;

    public string $description;

    public string $disponibilite;

    public int $quantite;

    public \DateTimeInterface $date_creation;

    public float $prix_lancement;

    public string $domaine;

    public string $categorie;

    public string $souscategorie;

    public string $url_site;

    public ?string $url_image = null;

    public ?float $taux_marque = null;

    public int $souscategorie_id;

    public int $categorie_id;

    public int $domaine_id;

    public string $status;

    public int $garantie;

    public float $poids;

    public float $ecotaxe;

    public ?string $url_image_big = null;

    /** @var array|JsonType|null */
    public ?array $medias_largests_url = null;
}
