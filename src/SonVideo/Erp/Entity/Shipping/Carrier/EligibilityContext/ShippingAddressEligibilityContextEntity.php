<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext;

use App\Entity\AbstractEntity;

/**
 * Class ShippingAddressEligibilityContextEntity.
 */
class ShippingAddressEligibilityContextEntity extends AbstractEntity
{
    public string $title;

    public string $firstname;

    public string $lastname;

    public string $cellphone;

    public string $city;

    public string $postal_code;

    public string $address;

    public string $country_code;
}
