<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity;

use App\Adapter\Serializer\Type\JsonDenormalizableInterface;
use App\Entity\AbstractEntity;

class WarehouseUserEntity extends AbstractEntity implements JsonDenormalizableInterface
{
    public int $user_id;

    public string $username;

    public ?string $email = null;

    public string $full_name;

    public string $barcode;

    public int $warehouse_id;
}
