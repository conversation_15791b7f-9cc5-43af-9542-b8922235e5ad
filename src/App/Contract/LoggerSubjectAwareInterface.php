<?php

namespace App\Contract;

interface LoggerSubjectAwareInterface
{
    /** Attach an observer */
    public function attach(LoggerObserverAwareInterface $observer): void;

    /** Detach an observer */
    public function detach(LoggerObserverAwareInterface $observer): void;

    /** Notify observers for a new log message */
    public function notify($level, string $message, array $context = []): void;
}
