<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Contract;

/**
 * Interface QueryConditionInterface.
 */
interface QueryConditionInterface extends WhereQueryBuilderAwareInterface
{
    /** getKeys */
    public function getKeys(): array;

    /** getKeyword */
    public function getKeyword(string $key): string;

    /** handle */
    public function handle(string $key, array $condition): string;
}
