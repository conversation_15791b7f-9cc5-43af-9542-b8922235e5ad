<?php
/*
 * This file is part of carrier package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Formatter\Http;

/**
 * Class JSendFormatter.
 *
 * Format response following the JSend format.
 *
 * @see     http://labs.omniti.com/labs/jsend
 *
 * <AUTHOR> <<EMAIL>>
 */
class JSendFormatter
{
    /**
     * success.
     *
     * @param mixed $data
     *
     * @return array{status: string, data: mixed}
     */
    public static function success($data): array
    {
        return [
            'status' => 'success',
            'data' => $data,
        ];
    }

    /**
     * fail.
     *
     * @param mixed $data
     *
     * @return array{status: string, data: mixed}
     */
    public static function fail($data): array
    {
        return [
            'status' => 'fail',
            'data' => $data,
        ];
    }

    /**
     * error.
     *
     * @param mixed $message
     *
     * @return array{status: string, message: mixed, code: int, data: mixed[]}
     */
    public static function error($message, int $code = 0, array $data = []): array
    {
        return ['status' => 'error', 'message' => $message, 'code' => $code, 'data' => $data];
    }

    /**
     * convertNumeric.
     *
     * Recursively convert string numeric data to float values in the $data.
     *
     * @param mixed $data
     *
     * @return mixed Same structure as input, with int or float instead of numeric strings
     */
    public static function convertNumeric($data)
    {
        if (is_array($data)) {
            return array_map('self::convertNumeric', $data);
        }

        if (is_object($data)) {
            $result = (array) $data;

            return array_map('self::convertNumeric', $result);
        }

        if (is_string($data) && is_numeric($data)) {
            return (float) $data;
        }

        return $data;
    }
}
