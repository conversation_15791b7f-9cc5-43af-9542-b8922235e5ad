<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Eav;

use App\Command\MonitorizedCommand;
use App\Database\PgErpServer\EavSchema\ProductValueModel;
use SonVideo\Eav\Repository\SubcategoryEavReadRepository;
use SonVideo\Eav\Webhook\AbstractEavWebhookHandler;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Synapps\Client\RpcClientAwareInterface;
use SonVideo\Synapps\Client\RpcClientAwareTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class SyncArticlesAttributesCommand.
 */
class SyncArticlesAttributesCommand extends MonitorizedCommand implements RpcClientAwareInterface
{
    use RpcClientAwareTrait;

    public const CHECK_UUID = '9f781f90-d71a-4591-9be9-6b9094fb766a';

    protected static $defaultName = 'eav:sync-articles-attributes';

    protected const CHUNK_SIZE = 500;

    protected ProductValueModel $product_value_model;

    protected SubcategoryEavReadRepository $subcategory_eav_read_repository;

    private SingleArticleReadRepository $single_article_read_repository;

    /** SyncArticlesAttributesCommand constructor. */
    public function __construct(
        ProductValueModel $product_value_model,
        SubcategoryEavReadRepository $subcategory_eav_read_repository,
        SingleArticleReadRepository $single_article_read_repository,
        string $server_env,
        string $name = null
    ) {
        $this->product_value_model = $product_value_model;
        $this->subcategory_eav_read_repository = $subcategory_eav_read_repository;
        $this->single_article_read_repository = $single_article_read_repository;
        parent::__construct($server_env, $name);
    }

    protected function configure()
    {
        $this->setDescription('Synchronize articles eav_attributes to CMS. Only published by default.')
            ->addOption(
                'skus',
                null,
                InputOption::VALUE_OPTIONAL,
                'Synchronize only the provided skus. Example: SKU1,SKU2,SKU3. By default, unpublished skus will be ignored! Add "--all" to bypass.'
            )
            ->addOption('all', null, InputOption::VALUE_NONE, 'Synchronize all articles that have attributes defined.')
            ->addOption(
                'batch',
                null,
                InputOption::VALUE_OPTIONAL,
                'Number of products to process in a row. Use this option to decrease server load if needed.',
                self::CHUNK_SIZE
            )
            ->addOption(
                'wait',
                null,
                InputOption::VALUE_OPTIONAL,
                'Number of seconds to wait between batches. Use this option to decrease server load if needed.',
                0
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');

            $time_start = time();
            $output->writeln('Job starts...');

            $selected_skus = $input->getOption('skus');
            $skus = $selected_skus
                ? preg_split('/,/', $selected_skus)
                : array_column($this->product_value_model->findDistinctSkus(), 'sku');

            $use_all = $input->getOption('all');
            if (!$use_all) {
                $published_skus = $this->single_article_read_repository->findActiveArticleSkus();
                $skus = array_intersect($skus, $published_skus);
            }

            $chunks = array_chunk($skus, $input->getOption('batch'));

            $output->writeln(
                sprintf('%d products to process, in %d chunks', is_countable($skus) ? count($skus) : 0, count($chunks))
            );

            $sleep_time = $input->getOption('wait');
            array_walk($chunks, function ($chunk, $index) use ($sleep_time, $output): void {
                $last = end($chunk);
                $first = reset($chunk);
                $output->writeln(sprintf('Process chunk %d: [%s … %s]', $index, $first, $last));
                $articles = $this->product_value_model->getAttributesBySkus($chunk);
                $response = $this->rpc_client->call('bo-cms', AbstractEavWebhookHandler::RPC_UPDATE_ATTRIBUTES_METHOD, [
                    $articles,
                ]);
                $output->writeln(sprintf('Result: %s', json_encode($response['result'], JSON_THROW_ON_ERROR)));
                sleep($sleep_time);
            });

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Throwable $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
