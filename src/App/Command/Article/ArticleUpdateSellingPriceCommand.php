<?php

namespace App\Command\Article;

use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ArticleUpdateSellingPriceCommand extends Command
{
    public const CHECK_UUID = 'e86bc472-8f2b-4c09-88d0-aa13f8d197e6';
    protected static $defaultName = 'article:update-selling-price';
    private ArticleSalesChannelManager $article_sales_channel_manager;

    public function __construct(
        string $server_env,
        ArticleSalesChannelManager $article_sales_channel_manager,
        string $name = null
    ) {
        parent::__construct($server_env, $name);
        $this->article_sales_channel_manager = $article_sales_channel_manager;
    }

    protected function configure(): void
    {
        $this->setDescription('Modifies the selling price of the article')
            ->addArgument('article_id', InputArgument::REQUIRED, 'Article ID')
            ->addArgument('sales_channel_id', InputArgument::REQUIRED, 'Sales channel ID')
            ->addArgument('selling_price', InputArgument::REQUIRED, 'Selling price, all tax included');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $time_start = time();
        $article_id = (int) $input->getArgument('article_id');
        $sales_channel_id = (int) $input->getArgument('sales_channel_id');
        $selling_price = (float) $input->getArgument('selling_price');
        $this->article_sales_channel_manager->updateSellingPriceWithTaxes(
            $article_id,
            $sales_channel_id,
            $selling_price
        );
        $output->writeln(sprintf(' done in %d second(s).', time() - $time_start));

        return 0;
    }
}
