<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Report;

use App\Command\IncreaseMemoryTrait;
use App\Command\MonitorizedCommand;
use SonVideo\Erp\Export\Statistic\Manager\CustomerOrdersToCsvExporter;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ExportCustomerOrdersCommand extends MonitorizedCommand
{
    use IncreaseMemoryTrait;

    protected static $defaultName = 'report:customer-order:export-customer-orders';

    public const CHECK_UUID = 'dce7a8cf-3675-46f9-bdd4-51c0389c0eae';

    private CustomerOrdersToCsvExporter $customer_orders_to_csv_exporter;

    /** @param string|null $name */
    public function __construct(
        CustomerOrdersToCsvExporter $customer_orders_to_csv_exporter,
        string $server_env,
        string $name = null
    ) {
        $this->customer_orders_to_csv_exporter = $customer_orders_to_csv_exporter;
        parent::__construct($server_env, $name);
    }

    /** {@inheritDoc} */
    protected function configure(): void
    {
        $this->setDescription('Export customer orders for statistics.');
    }

    /** {@inheritDoc} */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $this->customer_orders_to_csv_exporter->export();

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->logger->debug(sprintf('<comment>Peak memory usage: %s</comment>', memory_get_peak_usage()));

            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
