<?php

namespace App\Command\Report;

use App\Command\IncreaseMemoryTrait;
use App\Command\MonitorizedCommand;
use SonVideo\Erp\Export\Statistic\Manager\SellInToCsvExporter;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ExportSellInCommand extends MonitorizedCommand
{
    use IncreaseMemoryTrait;

    protected static $defaultName = 'report:export-sell-in';

    public const CHECK_UUID = '0e7183fe-619d-45fe-b2a3-c78e8f863109';

    private SellInToCsvExporter $sell_in_to_csv_exporter;

    /** @param string|null $name */
    public function __construct(SellInToCsvExporter $sell_in_to_csv_exporter, string $server_env, string $name = null)
    {
        $this->sell_in_to_csv_exporter = $sell_in_to_csv_exporter;
        parent::__construct($server_env, $name);
    }

    /** {@inheritDoc} */
    protected function configure(): void
    {
        $this->setDescription('sell-in statistics.');
    }

    /** {@inheritDoc} */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $this->sell_in_to_csv_exporter->export();

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->logger->debug(sprintf('<comment>Peak memory usage: %s</comment>', memory_get_peak_usage()));

            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
