<?php

namespace App\Command\GroupDigital;

use App\Command\MonitorizedCommand;
use SonVideo\Erp\Export\GroupDigital\Manager\GroupDigitalRefsFromApiImporter;
use SonVideo\Erp\Export\GroupDigital\Manager\GroupDigitalRefsFromCsvImporter;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ImportRefsCommand extends MonitorizedCommand
{
    protected static $defaultName = 'group-digital:import-refs';

    public const CHECK_UUID = '64c9f3f9-c4d1-4959-b5c7-21d746635b89';

    private GroupDigitalRefsFromCsvImporter $group_digital_refs_from_csv_importer;

    private GroupDigitalRefsFromApiImporter $group_digital_refs_from_api_importer;

    public function __construct(
        GroupDigitalRefsFromCsvImporter $group_digital_refs_from_csv_importer,
        GroupDigitalRefsFromApiImporter $group_digital_refs_from_api_importer,
        string $server_env,
        string $name = null
    ) {
        $this->group_digital_refs_from_csv_importer = $group_digital_refs_from_csv_importer;
        $this->group_digital_refs_from_api_importer = $group_digital_refs_from_api_importer;
        parent::__construct($server_env, $name);
    }

    /** {@inheritDoc} */
    protected function configure(): void
    {
        $this->setDescription('Import Group Digital refs.')->addArgument(
            'src_file',
            InputArgument::OPTIONAL,
            'Source CSV file.'
        );
    }

    /** {@inheritDoc} */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $src_file = $input->getArgument('src_file');
            if (null === $src_file) {
                $output->writeln('Importing Group Digital references from API');
                $this->group_digital_refs_from_api_importer->import();
            } else {
                $output->writeln(sprintf('Importing Group Digital references from %s', $src_file));
                $this->group_digital_refs_from_csv_importer->import($src_file);
            }

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->logger->debug(sprintf('<comment>Peak memory usage: %s</comment>', memory_get_peak_usage()));

            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
