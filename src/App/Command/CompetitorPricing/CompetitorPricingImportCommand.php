<?php

namespace App\Command\CompetitorPricing;

use App\Sentry\SentryCronJobMonitor;
use SonVideo\Erp\Competitor\Manager\CompetitorPricingImporter;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class CompetitorPricingImportCommand extends Command
{
    protected static $defaultName = 'competitor-pricing:import';

    private SentryCronJobMonitor $sentry_cron_job_monitor;
    private CompetitorPricingImporter $competitor_pricing_importer;

    public function __construct(
        SentryCronJobMonitor $sentry_cron_job_monitor,
        CompetitorPricingImporter $competitor_pricing_importer,
        string $name = null
    ) {
        parent::__construct($name);
        $this->sentry_cron_job_monitor = $sentry_cron_job_monitor;
        $this->competitor_pricing_importer = $competitor_pricing_importer;
    }

    protected function configure(): void
    {
        $this->setDescription('Batch import products from wiser.')->addOption(
            'mode',
            null,
            InputOption::VALUE_REQUIRED,
            'While testing wiser new flux, there two mode available: SVD and EASYLOUNGE.',
            CompetitorPricingImporter::MODE_SVD
        );
    }

    /** @throws \Exception */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $time_start = time();

        $monitor_name = strtolower(sprintf('competitor-pricing-import-%s', $input->getOption('mode')));
        $output->writeln(sprintf('Job starts... (%s)', $monitor_name));
        $this->sentry_cron_job_monitor->start($monitor_name);

        try {
            $output->writeln('Starting process...');

            $this->competitor_pricing_importer->import($input->getOption('mode'));

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

            $this->sentry_cron_job_monitor->end();
        } catch (\Exception $exception) {
            $this->sentry_cron_job_monitor->fail();

            throw $exception;
        }

        return 0;
    }
}
