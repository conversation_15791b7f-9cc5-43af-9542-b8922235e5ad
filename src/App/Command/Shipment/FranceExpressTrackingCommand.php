<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Shipment;

use App\Command\MonitorizedCommand;
use SonVideo\Erp\Client\CarrierAWSClient;
use SonVideo\Erp\Repository\Shipping\ShipmentTrackingReadRepository;
use SonVideo\Erp\Repository\Shipping\ShipmentTrackingWriteRepository;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class FranceExpressTrackingCommand.
 */
class FranceExpressTrackingCommand extends MonitorizedCommand
{
    protected static $defaultName = 'shipment:france-express-tracking';

    private const CHECK_UUID = 'c97e25e8-8014-4b89-8106-9bf2f42ad3ed';

    private ShipmentTrackingReadRepository $shipment_tracking_read_repository;
    private ShipmentTrackingWriteRepository $shipment_tracking_write_repository;
    private CarrierAWSClient $carrier_aws_client;

    /** FranceExpressTrackingCommand constructor. */
    public function __construct(
        ShipmentTrackingReadRepository $shipment_tracking_read_repository,
        ShipmentTrackingWriteRepository $shipment_tracking_write_repository,
        CarrierAWSClient $carrier_aws_client,
        string $server_env,
        string $name = null
    ) {
        parent::__construct($server_env, $name);
        $this->shipment_tracking_read_repository = $shipment_tracking_read_repository;
        $this->shipment_tracking_write_repository = $shipment_tracking_write_repository;
        $this->carrier_aws_client = $carrier_aws_client;
    }

    /** configure */
    protected function configure()
    {
        $this->setDescription('Search and import of France Express tracking number');
    }

    /**
     * execute.
     *
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $output->writeln('Fetch data from database...');
            $parcels = $this->shipment_tracking_read_repository->getUndeliveredFranceExpressParcels();

            foreach ($parcels as $parcel) {
                try {
                    $query_string = [
                        'tracking_number' => $parcel->parcel_number,
                        'carrier' => 'FREXP',
                        'account' => $parcel->sent_from,
                    ];

                    $this->carrier_aws_client->ping();

                    $response = $this->carrier_aws_client->get('/api/v1/shipment/parcel-tracking', $query_string);

                    $decoded = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

                    if (!empty($decoded['data']['status_details'][0]['tracking_number'])) {
                        $this->shipment_tracking_write_repository->updateParcelFranceExpressTracking(
                            $parcel->parcel_id,
                            $decoded['data']['status_details'][0]['tracking_number']
                        );
                    }
                } catch (\Exception $exception) {
                    $this->logger->info(
                        '[FRANCE EXPRESS TACKING] An error occurred while importing the tracking number.'
                    );

                    $output->writeln(
                        sprintf(
                            'Failed to import tracking number %s. Exception: %s',
                            $parcel->parcel_number,
                            $exception->getMessage()
                        )
                    );
                    $output->writeln('Continue...');
                }
            }
            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
