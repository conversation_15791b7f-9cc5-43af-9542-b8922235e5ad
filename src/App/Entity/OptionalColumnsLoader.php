<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Entity;

class OptionalColumnsLoader
{
    private array $original_collection = [];

    private array $optional_columns = [];

    private array $pivot_ids = [];

    private ?string $pivot_column_name = null;

    private array $collected_optional_columns = [];

    /** @return $this */
    public function addColumn(OptionalColumnDefinition $optional_column): self
    {
        $this->optional_columns[$optional_column->getName()] = $optional_column;

        return $this;
    }

    /** @param string|null $pivot_column_name */
    public function load(array $original_collection, string $pivot_column_name, array $optional_column_keys = []): array
    {
        // Nothing to expand data into
        if ([] === $original_collection) {
            return [];
        }

        $collection = $this->preparePivot($original_collection, $pivot_column_name);

        // Fetch optional columns
        foreach ($optional_column_keys as $optional_column_key) {
            if (!isset($this->optional_columns[$optional_column_key])) {
                throw new \InvalidArgumentException(sprintf('Optional column with "%s" does not exists, Available keys are : "%s".', $optional_column_key, implode('", "', array_keys($this->optional_columns))));
            }

            $this->collectOptionalColumn($this->optional_columns[$optional_column_key]);
        }

        // Merge collection with collected optional columns
        foreach ($collection as $pivot => $data) {
            $collection[$pivot] = array_merge($data, $this->collected_optional_columns[$pivot]);
        }

        return array_values($collection);
    }

    protected function preparePivot(array $original_collection, string $pivot_column_name): array
    {
        $collection_with_pivot = [];
        $this->pivot_ids = array_column($original_collection, $pivot_column_name);
        $this->pivot_column_name = $pivot_column_name;

        foreach ($original_collection as $item) {
            $collection_with_pivot[$item[$pivot_column_name]] = $item;
            $this->collected_optional_columns[$item[$pivot_column_name]] = [];
        }

        $this->original_collection = $collection_with_pivot;

        return $collection_with_pivot;
    }

    /** @return $this */
    protected function collectOptionalColumn(OptionalColumnDefinition $optional_column_definition): self
    {
        if ($optional_column_definition->hasInternalPivot()) {
            return $this->collectOptionalColumnWithInternalPivot($optional_column_definition);
        }

        $results = call_user_func_array(
            $optional_column_definition->getCallback(),
            array_merge([$this->pivot_ids], $optional_column_definition->getAdditionalParams())
        );

        foreach (array_keys($this->collected_optional_columns) as $pivot) {
            $mapped_value =
                OptionalColumnDefinition::OBJECT === $optional_column_definition->getType()
                    ? $results[$pivot] ?? []
                    : array_values(
                        array_filter(
                            $results,
                            fn ($data): bool => $data[$this->pivot_column_name] == $pivot // do not ue triple equality, we don't know if the data has been casted properly here
                        )
                    );

            $this->collected_optional_columns[$pivot] = array_merge($this->collected_optional_columns[$pivot], [
                $optional_column_definition->getName() => $mapped_value,
            ]);
        }

        return $this;
    }

    /** @return $this */
    protected function collectOptionalColumnWithInternalPivot(
        OptionalColumnDefinition $optional_column_definition
    ): self {
        // Extract pivot using the key specified in the optional column definition
        $internal_pivot_name = $optional_column_definition->getInternalPivot();
        $internal_pivot_ids = array_unique(array_column($this->original_collection, $internal_pivot_name));

        $results = call_user_func_array($optional_column_definition->getCallback(), [
            $internal_pivot_ids,
            $optional_column_definition->getAdditionalParams(),
        ]);

        // iterate over the original collection which contains all the keys
        foreach ($this->original_collection as $global_pivot => $value) {
            $current_pivot = $value[$internal_pivot_name];
            $mapped_value = array_values(
                array_filter(
                    $results,
                    static fn ($data): bool => $data[$internal_pivot_name] == $current_pivot // do not ue triple equality, we don't know if the data has been casted properly here
                )
            );

            if (OptionalColumnDefinition::OBJECT === $optional_column_definition->getType()) {
                $mapped_value = $mapped_value[0] ?? [];
            }

            $this->collected_optional_columns[$global_pivot] = array_merge(
                $this->collected_optional_columns[$global_pivot],
                [$optional_column_definition->getName() => $mapped_value]
            );
        }

        return $this;
    }
}
