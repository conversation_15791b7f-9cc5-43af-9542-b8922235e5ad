<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sql\Helper;

/**
 * Utility class to ease creation and manipulation of where clauses for MySql.
 *
 * @deprecated Use the QueryBuilder instead
 */
class Where
{
    public const NULL_VALUE = '_null_';
    public const NOT_NULL_VALUE = '_not_null_';

    protected $sql = '';
    protected $params = [];

    /**
     * __toString.
     *
     * @return string
     */
    public function __toString()
    {
        return $this->getSql();
    }

    /**
     * addCondition.
     *
     * @param string $column     condition's left part = column name in the database to filter
     * @param string $key        query parameter name in the resulting query
     * @param string $value      query parameter value
     * @param string $comparator conditions's comparator
     * @param string $condition  Template of the condition's right part. Can use ":key" as placeholder.
     */
    public function addCondition(
        string $column,
        string $key,
        string $value,
        string $comparator = '=',
        string $condition = ':key'
    ): self {
        if (self::NULL_VALUE === $value) {
            $this->sql .= sprintf('AND %s IS NULL ', $column);

            return $this;
        }

        if (self::NOT_NULL_VALUE === $value) {
            $this->sql .= sprintf('AND %s IS NOT NULL ', $column);

            return $this;
        }

        $condition = strtr($condition, [':key' => ':' . $key]);
        $this->sql .= sprintf('AND %s %s %s ', $column, $comparator, $condition);
        $this->params[$key] = $value;

        return $this;
    }

    /**
     * addInCondition.
     *
     * @param mixed $values
     */
    public function addInCondition(string $column, string $key_prefix, array $values): self
    {
        $keys = [];

        foreach ($values as $i => $value) {
            $key = sprintf('%s__%s', $key_prefix, $i);
            $keys[] = $key;
            $this->params[$key] = $value;
        }

        $in_clause = implode(',', array_map(fn ($key): string => ':' . $key, $keys));

        $this->sql .= sprintf('AND %s IN (%s) ', $column, $in_clause);

        return $this;
    }

    /** @return array */
    public function getParams()
    {
        return $this->params;
    }

    public function getSql(): string
    {
        return $this->sql ? 'WHERE true ' . $this->sql : '';
    }
}
