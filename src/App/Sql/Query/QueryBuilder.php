<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sql\Query;

use App\Sql\Query\Where\WhereQueryBuilder;

/**
 * Class QueryBuilder.
 */
class QueryBuilder
{
    public const WHERE_FALSE = ['FALSE'];

    private WhereQueryBuilder $where_query_builder;

    /** @var array */
    private $where = [];

    private int $offset = 1;

    private int $limit = 50;

    private string $order_by = '';

    private array $included_dependencies = [];

    private array $columns_mapping = [];

    /** QueryBuilder constructor. */
    public function __construct(WhereQueryBuilder $where_query_builder)
    {
        $this->where_query_builder = $where_query_builder;
    }

    /**
     * Set filters affected on the query.
     *
     * @param array $columns_mapping @deprecated
     */
    public function setWhere(array $where = [], array $columns_mapping = []): self
    {
        $this->where = $where;

        if ([] !== $columns_mapping) {
            $this->setColumnsMapping($columns_mapping);
        }

        return $this;
    }

    /** Set columns mapping */
    public function setColumnsMapping(array $columns_mapping): self
    {
        $this->columns_mapping = $columns_mapping;

        return $this;
    }

    /** Set the page number and the limit of the query */
    public function setPage(int $offset = 1, int $limit = 50): QueryBuilder
    {
        $this->offset = $offset;
        $this->limit = $limit;

        return $this;
    }

    /**
     * retrieve and compute order_by out of params' order_by and order_direction keys
     *   - order_by must be an empty string
     *   - or, if order_direction is used, order_by must be a word
     *   - or order_by must complain to a mysql "order by" clause
     *     = is a word (\w+) which may be followed by a space and keyword asc or desc (( asc| desc)?),
     *       prepended by the same pattern 0 to many times, separated by comma.
     */
    protected function computeOrderByFrom(array $params): string
    {
        // retrieve order_direction
        $order_direction = $params['sort_direction'] ?? ($params['order_direction'] ?? null);

        if (null !== $order_direction && 1 !== preg_match('/^(asc|desc)$/i', $order_direction)) {
            throw new \InvalidArgumentException('when used, order_direction must be either "asc" or "desc"');
        }

        // retrieve order_by
        $order_by = trim($params['sort_by'] ?? '');

        // merge order_by and order_direction if used
        if (null !== $order_direction) {
            $order_by .= ' ' . $order_direction;
        }

        return $order_by;
    }

    /** setOrderBy */
    public function setOrderBy(string $sort_by = '', string $sort_direction = null): QueryBuilder
    {
        $order_by = $this->computeOrderByFrom(['sort_by' => $sort_by, 'sort_direction' => $sort_direction]);
        $this->order_by = '' !== $order_by ? 'ORDER BY ' . $order_by : '';

        return $this;
    }

    /** setOrderByCriterion */
    public function setOrderByCriterion(array $criterion): QueryBuilder
    {
        $sort_by = [];
        foreach ($criterion as $criteria) {
            $order_by = $this->computeOrderByFrom($criteria);

            if ('' !== $order_by && 1 !== preg_match('/^(\w+( asc| desc)?,)*(\w+( asc| desc)?){1}$/i', $order_by)) {
                throw new \InvalidArgumentException(sprintf('order_by "%s" is not a valid sql string.', $order_by));
            }

            if (strlen($order_by) > 0) {
                $sort_by[] = $order_by;
            }
        }

        if ([] !== $sort_by) {
            $this->order_by = sprintf('ORDER BY %s', implode(' ,', $sort_by));
        }

        return $this;
    }

    public function setIncludedDependencies(array $included_dependencies): QueryBuilder
    {
        $this->included_dependencies = $included_dependencies;

        return $this;
    }

    /** getWhere */
    public function getWhere(): string
    {
        if (self::WHERE_FALSE === $this->where) {
            return 'FALSE';
        }

        $where = $this->where_query_builder->setColumnsMapping($this->columns_mapping)->build($this->where);

        return '' === $where ? 'TRUE' : $where;
    }

    /** getWhereParameters */
    public function getWhereParameters(): array
    {
        return $this->where_query_builder->getBindedParameters();
    }

    /** getOffset */
    public function getOffset(): int
    {
        return $this->offset;
    }

    /** getLimit */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /** getOrderBy */
    public function getOrderBy(): string
    {
        return $this->order_by;
    }

    /** @return 'true'|'false' */
    public function hasIncludedDependency(string $key): string
    {
        return in_array($key, $this->included_dependencies, true) ? 'TRUE' : 'FALSE';
    }

    /** reset */
    public function reset(): QueryBuilder
    {
        $this->where = '';
        $this->order_by = '';
        $this->where_query_builder->reset();
        $this->included_dependencies = [];
        $this->columns_mapping = [];

        return $this;
    }
}
