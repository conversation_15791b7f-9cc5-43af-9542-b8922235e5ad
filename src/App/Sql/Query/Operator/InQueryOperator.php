<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sql\Query\Operator;

/**
 * Class InQueryOperator.
 */
class InQueryOperator extends AbstractQueryOperator
{
    private const KEYS = [
        '_in' => 'IN',
        '_nin' => 'NOT IN',
    ];

    /** getKeys */
    public function getKeys(): array
    {
        return array_keys(self::KEYS);
    }

    /** {@inheritDoc} */
    public function handle($key, $operator, $value, $parameter_name = null): string
    {
        $this->checkIfValueCanBeUsed($value);

        if (!$parameter_name) {
            $parameter_name = $key;
        }

        $placeholders = [];
        foreach ($value as $item) {
            $placeholder = $this->where_query_builder->addBindParameter($parameter_name, $item);
            $placeholders[] = sprintf(':%s', $placeholder);
        }

        return sprintf(
            '%s %s (%s)',
            $this->applyColumnMapping($key),
            self::KEYS[$operator],
            implode(', ', $placeholders)
        );
    }

    /**
     * checkIfValueCanBeUsed.
     *
     * @param $value
     */
    protected function checkIfValueCanBeUsed($value): bool
    {
        if (!is_array($value)) {
            throw new \InvalidArgumentException('The IN query operator only accepts an array as value.');
        }

        return true;
    }
}
