<?php

namespace App\Sql\Query\Operator;

class ColumnComparisonQueryOperator extends AbstractQueryOperator
{
    /**
     * This compare a column to another one and thus should not be used as a regular comparison operator
     * The "c" stands for column
     * The value should be another column and there is no PDO binding as the value is another column
     * If you need regular comparison operator please use the ArithmeticQueryOperator.
     */
    public const KEYS = [
        '_ceq' => '=',
        '_cneq' => '!=',
        '_cgt' => '>',
        '_cgte' => '>=',
        '_clt' => '<',
        '_clte' => '<=',
    ];

    /** {@inheritDoc} */
    public function getKeys(): array
    {
        return array_keys(self::KEYS);
    }

    /** {@inheritDoc} */
    public function handle($key, $operator, $value, $parameter_name = null): string
    {
        // The is no need to use a `checkIfValueCanBeUsed` method as the value is a column mapping
        return sprintf(
            '%s %s %s',
            $this->applyColumnMapping($key),
            self::KEYS[$operator],
            $this->applyColumnMapping($value)
        );
    }
}
