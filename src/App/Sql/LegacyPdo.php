<?php

namespace App\Sql;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\EntityInterface;
use App\DataLoader\MapToEntityTrait;
use App\Entity\AbstractEntity;
use App\Exception\InternalErrorException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Helper\Pager;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Exception as DriverException;
use Doctrine\DBAL\Driver\PDO\Connection as PDOConnection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Statement;
use SonVideo\Erp\Referential\InternalError;

class LegacyPdo implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    private const DEFAULT_TYPES = [
        'boolean' => ParameterType::BOOLEAN,
        'NULL' => ParameterType::NULL,
        'integer' => ParameterType::INTEGER,
    ];

    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /** @throws Exception */
    public function fetchValueFromOutputParameter(
        string $sql,
        array $params = [],
        array $types = [],
        string $output_parameter = 'result'
    ): ?string {
        $this->connection->executeQuery(sprintf('SET @%s = NULL;', $output_parameter));
        $this->connection->executeQuery($sql, $params, $this->mapTypes($types, $params));

        return $this->fetchValue(sprintf('SELECT @%s AS result', $output_parameter));
    }

    /**
     * Fetches the very first value (i.e., first column of the first row).
     *
     * @throws Exception
     *
     * @see Connection::fetchOne()
     * @todo: rename
     */
    public function fetchValue(string $statement, array $params = [], array $types = []): ?string
    {
        $value = $this->connection->fetchOne($statement, $params, $this->mapTypes($types, $params));

        return false === $value ? null : $this->checkForFormattedResponse($value);
    }

    /**
     * @return array<string, mixed>|false false is returned if no rows are found
     *
     * @see Connection::fetchAssociative()
     * @todo: rename
     */
    public function fetchOne(string $statement, array $params = [], array $types = [])
    {
        return $this->connection->fetchAssociative($statement, $params, $this->mapTypes($types, $params));
    }

    /** @return array<int, object> */
    public function fetchObjects(string $sql, array $params = [], array $types = []): array
    {
        return array_map(
            static fn ($item): \stdClass => (object) $item,
            $this->fetchAll($sql, $params, $this->mapTypes($types, $params))
        );
    }

    /**
     * @return array<int,array<string,mixed>>
     *
     * @see Connection::fetchAllAssociative()
     * @todo: rename
     */
    public function fetchAll(string $statement, array $params = [], array $types = []): array
    {
        return $this->connection->fetchAllAssociative($statement, $params, $this->mapTypes($types, $params));
    }

    /**
     * Return the result as an associative array, indexed by the first column, which should be unique.
     * Similar to DBAL's fetchAllAssociativeIndexed except that the first column is preserved.
     *
     * @return array<mixed,array<string,mixed>>
     */
    public function fetchAssoc(string $statement, array $values = [], array $types = []): array
    {
        $data = [];

        foreach ($this->fetchAll($statement, $values, $types) as $row) {
            $data[current($row)] = $row;
        }

        return $data;
    }

    /**
     * @return array<int,mixed>
     *
     * @throws Exception
     *
     * @see Connection::fetchFirstColumn()
     * @todo: rename
     */
    public function fetchCol(string $statement, array $params = [], array $types = []): array
    {
        return $this->connection->fetchFirstColumn($statement, $params, $this->mapTypes($types, $params));
    }

    /**
     * @throws Exception
     *
     * @see Connection::executeStatement()
     * @todo: rename
     */
    public function fetchAffected(string $statement, array $params = [], array $types = []): int
    {
        return (int) $this->connection->executeStatement($statement, $params, $this->mapTypes($types, $params));
    }

    /**
     * @return \Traversable<int,array<string,mixed>>
     *
     * @see Connection::iterateAssociative()
     */
    public function iterateAssociative(string $statement, array $params = [], array $types = []): \Traversable
    {
        return $this->connection->iterateAssociative($statement, $params, $this->mapTypes($types, $params));
    }

    /**
     * @throws Exception
     *
     * @see Connection::prepare()
     */
    public function prepare($query): Statement
    {
        return $this->connection->prepare($query);
    }

    /**
     * Queries with multiple result set - Mainly for stored procedures.
     *
     * @return array<?string>
     *
     * @throws Exception|DriverException
     */
    public function fetchCollection(string $sql, array $params = [], array $types = []): array
    {
        $stmt = $this->connection->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value, $types[$key] ?? ($this->getType($value) ?? ParameterType::STRING));
        }
        $stmt->executeQuery($params);

        $raw_stmt = $stmt->getWrappedStatement();
        $attributes = [];
        do {
            $attributes[] = $this->check($raw_stmt->fetchAllAssociative());
        } while ($raw_stmt->nextRowset());

        return $attributes;
    }

    /**
     * Check current row set for errors but do return the row anyway
     * otherwise it will throw an Exception cause the cursor is not closed
     * We'll eventually fetch the error message later.
     *
     * @throws Exception
     */
    private function check($row)
    {
        if (!is_object($row[0] ?? [])) {
            return $row;
        }

        return property_exists($row[0] ?? [], 'resultat') ? $this->checkForFormattedResponse($row) : $row;
    }

    /**
     * Parse the legacy stored procedure return to get success or failure message.
     *
     * @throws \Exception
     */
    public function checkForFormattedResponse(?string $row): ?string
    {
        if (null === $row) {
            return null;
        }

        $pattern = '#^<(success|failure|error_message)>(.*)</(\1)>$#i';

        $matches = [];

        if (preg_match($pattern, $row, $matches)) {
            // When using dedicated error message (FORMAT_error_message)
            if ('error_message' === $matches[1]) {
                throw new SqlErrorMessageException($matches[2]);
            }

            // We don't want a silent error by default
            if ('failure' === $matches[1]) {
                $error_message = $this->getErrorMEssage($matches[2]);
                $error_pattern = '(warning|integrite|exception)i';

                // Default error that we NEVER want to show in the UI
                if (preg_match($error_pattern, $error_message)) {
                    throw new \Exception($this->getErrorMEssage($error_message), $this->getWrappedPdoConnection()->errorCode());
                }

                // Otherwise, use the internal error message
                throw new InternalErrorException(InternalError::GENERIC, new Exception($error_message));
            }

            $row = $matches[2];
        }

        return $row;
    }

    /**
     * Return the message as it is if the error can not be hinted from the database
     * otherwise use SHOW WARNINGS on the db to get a precise error message.
     */
    private function getErrorMessage(string $message): string
    {
        if (!preg_match('/Probleme : integrite/', $message)) {
            return $message;
        }

        $result = $this->fetchOne('SHOW WARNINGS');

        return is_array($result) ? json_encode($result, JSON_THROW_ON_ERROR) : $message;
    }

    /**
     * Returns the number of last query's results.
     * If you want to avoid the LIMIT, use "SQL_CALC_FOUND_ROWS" keyword in the previous query.
     *
     * @throws Exception
     */
    public function foundRows(): int
    {
        $sql = <<<SQL
        SELECT FOUND_ROWS() AS total_items;
        SQL;

        return (int) $this->connection->fetchOne($sql);
    }

    /**  Do paginateArray and convert entries as object. */
    public function paginateObjects(int $page, int $limit, string $sql, array $params): Pager
    {
        return $this->paginate($page, $limit, $sql, $params, 'fetchObjects');
    }

    /**
     * Do a fetchAssoc encapsulated in a Pager.
     *
     * This method is useful when you want to work with the pager alongside entities
     * (which work with arrays and not with object|stdClass)
     *
     * fetchAssoc use the first columns as key, hence duplicates are not allowed
     * If you need arrays without the association, use the paginateArray method
     */
    public function paginateAssoc(int $page, int $limit, string $sql, array $params): Pager
    {
        return $this->paginate($page, $limit, $sql, $params, 'fetchAssoc');
    }

    /**
     * Do a fetchAll encapsulated in a Pager.
     *
     * This method is useful when you want to work with the pager alongside entities
     * (which work with arrays and not with object|stdClass)
     */
    public function paginateArray(int $page, int $limit, string $sql, array $params): Pager
    {
        return $this->paginate($page, $limit, $sql, $params, 'fetchAll');
    }

    /**
     * Do a fetch all and map result in entity class.
     *
     * @template T of AbstractEntity
     *
     * @param class-string<T> $entity_class
     *
     * @return Pager<T>
     */
    public function paginateEntity(int $page, int $limit, string $sql, array $params, string $entity_class): Pager
    {
        $pager = $this->paginateArray($page, $limit, $sql, $params);

        return $pager->setResults($this->mapToEntitiesData($pager->getResults(), $entity_class));
    }

    /**
     * Do a fetch on a query which return one line.
     *
     * @template T of EntityInterface
     *
     * @param class-string<T> $entity_class
     *
     * @return ?T
     * */
    public function fetchOneEntity(string $sql, array $params, string $entity_class): ?EntityInterface
    {
        if ($result = $this->fetchOne($sql, $params)) {
            return $this->hydrateEntity($result, $entity_class);
        }

        return null;
    }

    /**
     * Do a fetch on a query which return multiple lines.
     *
     * @template T of EntityInterface
     *
     * @param class-string<T> $entity_class
     *
     * @return array<T>
     */
    public function fetchAllEntities(string $sql, array $params, string $entity_class): array
    {
        if ($result = $this->fetchAll($sql, $params)) {
            return $this->mapToEntities($result, $entity_class);
        }

        return [];
    }

    private function paginate(int $page, int $limit, string $sql, array $params, string $fetch_method): Pager
    {
        $pager = new Pager($page, $limit);

        $sql .= <<<SQL
         LIMIT {limit} OFFSET {offset}
        SQL;
        $sql = strtr($sql, [
            '{limit}' => $pager->getLimit(),
            '{offset}' => $pager->getOffset(),
        ]);

        return $pager->setResults($this->{$fetch_method}($sql, $params))->setTotal($this->foundRows());
    }

    /**
     * Create a transaction or a savepoint.
     *
     * @return true as of dbal ^2.13
     *
     * @see Connection::beginTransaction()
     */
    public function beginTransaction(): bool
    {
        return $this->connection->beginTransaction();
    }

    /**
     *  Commit a transaction or release a savepoint.
     *
     * @return bool success
     *
     * @see Connection::commit()
     */
    public function commit(): bool
    {
        return $this->connection->commit();
    }

    /**
     * Rollback a transaction or a savepoint.
     *
     * @see Connection::rollback()
     */
    public function rollback(): void
    {
        // as of dbal ^2.13, rollback() always return true
        $this->connection->rollback();
    }

    /**
     * @see Connection::lastInsertId()
     *
     * @return false|string
     */
    public function lastInsertId()
    {
        return $this->connection->lastInsertId();
    }

    private function getWrappedPdoConnection(): PDOConnection
    {
        return $this->connection->getWrappedConnection();
    }

    private function mapTypes(array $types, array $parameters): array
    {
        foreach ($parameters as $column => $value) {
            if (isset($types[$column])) {
                continue;
            }

            $type = $this->getType($value);
            if (null !== $type) {
                $types[$column] = $type;
            }
        }

        return $types;
    }

    /** @param mixed $value */
    private function getType($value): ?int
    {
        if (isset(self::DEFAULT_TYPES[gettype($value)])) {
            return self::DEFAULT_TYPES[gettype($value)];
        }

        if (is_array($value) && [] !== $value) {
            // only consider the first element for performance reasons.
            if (is_int(current($value))) {
                return Connection::PARAM_INT_ARRAY;
            }

            return Connection::PARAM_STR_ARRAY;
        }

        return null;
    }

    /** @see Connection::isTransactionActive() */
    public function inTransaction(): bool
    {
        return $this->connection->isTransactionActive();
    }
}
