<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sql;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;

/**
 * Class AbstractLegacyRepository.
 */
abstract class AbstractLegacyRepository implements DataLoaderAwareInterface
{
    use DataLoaderAwareTrait;

    protected LegacyPdo $legacy_pdo;

    public function __construct(LegacyPdo $legacy_pdo)
    {
        $this->legacy_pdo = $legacy_pdo;
    }

    /**
     * Check if $order_by is a valid sql for order by clause and return the full sql query part.
     *
     * @throws \InvalidArgumentException
     */
    protected function getOrderByClause(string $order_by): string
    {
        $order_by = trim($order_by);
        if ('' !== $order_by && 1 !== preg_match('/^(\w+( asc| desc)?,)*(\w+( asc| desc)?){1}$/i', $order_by)) {
            throw new \InvalidArgumentException(sprintf('order_by "%s" is not a valid sql string.', $order_by));
        }

        return '' !== $order_by ? 'ORDER BY ' . $order_by : '';
    }
}
