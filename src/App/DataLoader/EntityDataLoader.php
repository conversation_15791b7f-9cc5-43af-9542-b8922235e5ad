<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\DataLoader;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderInterface;
use App\Contract\EntityInterface;
use Symfony\Component\Serializer\Exception\ExceptionInterface as ExceptionInterfaceAlias;

/**
 * Class EntityDataLoader.
 *
 * @deprecated Use directly the symfony serializer instead
 */
class EntityDataLoader implements DataLoaderInterface
{
    protected SerializerInterface $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    /**
     * @template T of EntityInterface
     *
     * @param class-string<T> $entity
     *
     * @return T
     *
     * @throws ExceptionInterfaceAlias
     */
    public function hydrate($data, string $entity, $format = null, array $context = []): EntityInterface
    {
        /**
         * @var $class EntityInterface
         */
        $class = $this->serializer->denormalize($data, $entity, $format, $context);

        if (false === $class->hasDataLoader()) {
            $class->setDataLoader($this);
        }

        return $class;
    }

    /**
     * toArray.
     *
     * @throws ExceptionInterfaceAlias
     */
    public function toArray(EntityInterface $entity): array
    {
        return $this->serializer->normalize($entity);
    }
}
