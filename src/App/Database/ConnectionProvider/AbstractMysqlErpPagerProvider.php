<?php

namespace App\Database\ConnectionProvider;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder as OldQueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Orm\DataMapper;
use SonVideo\Orm\Entity\PaginatedCollectionRequest;
use SonVideo\Orm\Query;
use SonVideo\Orm\QueryBuilder;

abstract class AbstractMysqlErpPagerProvider extends AbstractMysqlErpRepository implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    public function __construct(MysqlErpConnectionProvider $connection_provider, DataMapper $data_mapper)
    {
        parent::__construct($connection_provider, $data_mapper);
    }

    public static function wrapSubQuery(string $subquery, string $columns = '*'): string
    {
        // The whole query wrapped in a sub query is required
        // since the order by clauses work with the aliases in column mapping
        // plus it also performs better when the result set is big
        $sql = <<<'MYSQL'
        SELECT SQL_CALC_FOUND_ROWS {columns}
        FROM
            (
                {subquery}
            ) tmp
            {order_by_clause}
            {pager_clause}
        MYSQL;

        return strtr($sql, [
            '{columns}' => $columns,
            '{subquery}' => $subquery,
        ]);
    }

    /** You can use your own yours, but you'll need to process them before passing the SQL string to this method */
    public function paginateWithModel(
        string $sql,
        PaginatedCollectionRequest $paginated_collection_request,
        string $model_name,
        ?string $target_entity_name = null
    ): Pager {
        $query_builder = (new QueryBuilder())->withPaginatedCollection($paginated_collection_request)->withSql($sql);
        $pager = new Pager($paginated_collection_request->getPage(), $paginated_collection_request->getLimit());
        $query = Query::select($model_name, $query_builder);

        $results = $this->getConnection()->fetchAllAssociative($query->toSql(), $query->getParams());
        if ($target_entity_name) {
            $results = $this->serializer->denormalize($results, $target_entity_name . '[]');
        }

        return $pager->setResults($results)->setTotal($this->foundRows());
    }

    /**
     * Supported placeholders are :
     * {conditions}
     * {order_by}
     * {limit}
     * {offset}.
     *
     * You can use your own yours, but you'll need to process them before passing the SQL string to this method
     */
    public function paginateWithOldQueryBuilder(
        string $sql,
        OldQueryBuilder $query_builder,
        ?string $target_entity_name = null
    ): Pager {
        $pager = new Pager($query_builder->getOffset(), $query_builder->getLimit());

        $sql .= <<<'MYSQL'
         LIMIT {limit} OFFSET {offset}
        MYSQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by_clause}' => $query_builder->getOrderBy(),
            '{limit}' => $pager->getLimit(),
            '{offset}' => $pager->getOffset(),
            '{pager_clause}' => '',
        ]);

        $results = $this->getConnection()->fetchAllAssociative($sql, $query_builder->getWhereParameters());
        if ($target_entity_name) {
            $results = $this->serializer->denormalize($results, $target_entity_name . '[]');
        }

        return $pager->setResults($results)->setTotal($this->foundRows());
    }

    /**
     * Returns the number of last query's results.
     * If you want to avoid the LIMIT, use "SQL_CALC_FOUND_ROWS" keyword in the previous query.
     *
     * @throws Exception
     */
    public function foundRows(): int
    {
        $sql = <<<SQL
        SELECT FOUND_ROWS() AS total_items;
        SQL;

        return (int) $this->getConnection()->fetchOne($sql);
    }
}
