<?php

namespace App\Database;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use Doctrine\DBAL\Exception;

class MysqlRoutine
{
    public const TYPE_NOT_FOUND = 'not_found';
    public const TYPE_READABLE_MESSAGE = 'error_message';
    public const PATTERN = '#<(?<type>error_message|not_found)>(?<message>.*)</(\1)>#i';

    public static function extractMessageFrom(string $raw_message): string
    {
        if (preg_match(self::PATTERN, $raw_message, $matches)) {
            return $matches['message'];
        }

        return $raw_message;
    }

    public static function type($raw_message): ?string
    {
        if (preg_match(self::PATTERN, $raw_message, $matches)) {
            return strtolower($matches['type']);
        }

        return null;
    }

    /** @throws NotFoundException|SqlErrorMessageException|\RuntimeException */
    public static function handleException(Exception $exception): void
    {
        $type = self::type($exception->getMessage());
        $message = self::extractMessageFrom($exception->getMessage());

        if (self::TYPE_NOT_FOUND === $type) {
            throw new NotFoundException($message, $exception->getCode(), $exception);
        }

        if (self::TYPE_READABLE_MESSAGE === $type) {
            throw new SqlErrorMessageException($message, $exception->getCode(), $exception);
        }

        throw new \RuntimeException($message, $exception->getCode(), $exception);
    }
}
