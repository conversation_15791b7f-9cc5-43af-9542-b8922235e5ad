<?php

namespace App\Database\PgErpServer\EavSchema;

use App\Database\PgErpServer\EavSchema\AutoStructure\ProductValue as ProductValueStructure;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

/**
 * ProductValueModel.
 *
 * Model class for table product_value.
 *
 * @see Model
 */
class ProductValueModel extends Model
{
    use WriteQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new ProductValueStructure();
        $this->flexible_entity_class = '\\' . ProductValue::class;
    }

    /**
     * Returns products with their eav attributes projection.
     *
     * @return mixed
     */
    public function getAttributesBySkus(array $skus)
    {
        $sql = <<<SQL
        WITH
            parameters AS (
                SELECT unnest(ARRAY ['{skus}']::citext[]) AS sku
            ),
            prepared_product AS (
                SELECT
                    pv.sku,
                    a.attribute_id,
                    sa.display_order,
                    (JSONB_AGG(DISTINCT TO_JSONB(a) - 'i18n' - 'name')) -> 0                              AS attribute,
                    JSONB_AGG(TO_JSONB(av) - 'i18n' - 'attribute_id' ORDER BY av.display_order, av.value) AS values
                FROM parameters p
                    INNER JOIN eav.product_value pv ON pv.sku = p.sku
                    INNER JOIN eav.attribute_value av ON av.attribute_value_id = pv.attribute_value_id
                    INNER JOIN eav.attribute a ON a.attribute_id = av.attribute_id
                    LEFT JOIN eav.product_subcategory ps ON ps.sku = pv.sku
                    LEFT JOIN eav.subcategory_attribute sa
                        ON a.attribute_id = sa.attribute_id AND sa.subcategory_id = ps.subcategory_id
                GROUP BY pv.sku, a.attribute_id, sa.display_order
            )
        SELECT
            sku,
            JSONB_AGG(
                JSONB_SET(attribute, '{values}', values)
                ORDER BY display_order, attribute->'meta'->>'label'
            ) AS eav_attributes
        FROM prepared_product pp
        GROUP BY sku;
        SQL;
        $sql = strtr($sql, [
            '{skus}' => implode('\',\'', $skus),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql)
            ->extract();
    }

    /** Retrieve distinct skus that are linked to an attribute, with its subcategory_id */
    public function findSkusByAttributeId(int $attribute_id): array
    {
        $sql = <<<SQL
        SELECT DISTINCT pv.sku, ps.subcategory_id
        FROM eav.product_value pv
            INNER JOIN eav.attribute_value av ON av.attribute_value_id = pv.attribute_value_id
            INNER JOIN eav.product_subcategory ps ON ps.sku = pv.sku
        WHERE attribute_id = $*
        SQL;

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$attribute_id])
            ->extract();
    }

    /**
     * Retrieve distinct skus.
     *
     * @return mixed
     */
    public function findDistinctSkus()
    {
        $sql = <<<SQL
        SELECT DISTINCT sku
        FROM eav.product_value
        ORDER BY sku
        SQL;

        return $this->getSession()
            ->getQueryManager()
            ->query($sql)
            ->extract();
    }

    public function findAttributeValuesForSku(string $sku, int $attribute_id): array
    {
        $sql = <<<SQL
        SELECT
          pv.sku,
          a.name,
          a.meta,
          a.definition,
          av.attribute_value_id,
          av.attribute_id,
          av.value,
          av.meta AS value_meta
          FROM eav.product_value           pv
            INNER JOIN eav.attribute_value av ON av.attribute_value_id = pv.attribute_value_id
            INNER JOIN eav.attribute a ON a.attribute_id = av.attribute_id
          WHERE
            {conditions}
        ;
        SQL;
        $conditions = Where::create('pv.sku = $* AND av.attribute_id = $*', [$sku, $attribute_id]);

        $sql = strtr($sql, [
            '{conditions}' => $conditions,
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, $conditions->getValues())
            ->extract();
    }
}
