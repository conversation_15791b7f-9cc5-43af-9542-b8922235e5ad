<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgErpServer\EavSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * AttributeValue.
 *
 * Structure class for relation eav.attribute_value.
 * List of all possible values for attributes.
 *
 * attribute_value_id:
 * attribute_value_id is the unique identifier.
 * attribute_id:
 * attribute value attached to an attribute id.
 * value:
 * the value of the attribute.
 * meta:
 * contextual information on the valued attribute.
 * i18n:
 * translation of the meta.
 *
 * @see RowStructure
 */
class AttributeValue extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('eav.attribute_value')
            ->setPrimaryKey(['attribute_value_id'])
            ->addField('attribute_value_id', 'int4')
            ->addField('attribute_id', 'int4')
            ->addField('value', 'public.citext')
            ->addField('meta', 'jsonb')
            ->addField('i18n', 'jsonb')
            ->addField('display_order', 'numeric');
    }
}
