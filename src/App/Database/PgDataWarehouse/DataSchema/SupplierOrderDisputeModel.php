<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\SupplierOrderDispute as SupplierOrderDisputeStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;
use PommProject\ModelManager\Model\Model;

/**
 * SupplierOrderDisputeModel.
 *
 * Model class for table supplier_order_dispute.
 *
 * @see Model
 */
class SupplierOrderDisputeModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new SupplierOrderDisputeStructure();
        $this->flexible_entity_class = SupplierOrderDispute::class;
    }
}
