<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\CustomerOrderInitialLine as CustomerOrderInitialLineStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;
use PommProject\ModelManager\Model\Model;

/**
 * CustomerOrderInitialLineModel.
 *
 * Model class for table customer_order_initial_line.
 *
 * @see Model
 */
class CustomerOrderInitialLineModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new CustomerOrderInitialLineStructure();
        $this->flexible_entity_class = CustomerOrderInitialLine::class;
    }

    public function getExportOrderedProductsFromSite(int $days_since): \Generator
    {
        $sql = <<<'SQL'
        WITH
          min_customer_order_line_status AS (
            SELECT col.customer_order_id, MIN(ros.id_order_status) AS id_order_status
              FROM data.customer_order_line col
                LEFT JOIN data.ref_order_status ros ON col.order_status = ros.order_status
              WHERE col.quantity > 0 AND col.order_status <> 'Supprimée'
              GROUP BY col.customer_order_id
            ),
          customer_order_status AS (
            SELECT mcols.customer_order_id, ros.order_status
              FROM min_customer_order_line_status mcols
                LEFT JOIN data.ref_order_status ros ON mcols.id_order_status = ros.id_order_status
            )
        SELECT
          c.created_at::DATE::TEXT AS date,
          EXTRACT('Year' FROM c.created_at) AS year,
          EXTRACT('Month' FROM c.created_at) AS month,
          c.customer_order_id,
          COALESCE(cos.order_status, 'Annulée') AS order_status,
          il.reference,
          il.product_type,
          il.brand,
          il.domain,
          il.category,
          il.subcategory,
          il.supplier,
          c.origin,
          c.country,
          c.postal_code,
          il.quantity,
          il.total_purchase_cost,
          il.total_gross_excl_tax,
          il.total_discount_excl_tax,
          il.total_net_excl_tax,
          il.total_guarantee_excl_tax,
          il.total_margin,
          il.model,
          c.is_btob::INT,
          c.is_internal::INT,
          c.source,
          c.source_group,
          c.sales_location,
          c.created_by,
          CASE WHEN SUBSTRING(il.reference FOR 7) = 'DESTOCK' THEN 1 ELSE 0 END AS is_destock,
          il.promo_code
          FROM data.customer_order_initial_line il
            INNER JOIN data.customer_order c ON
              c.customer_order_id = il.customer_order_id
            LEFT JOIN customer_order_status cos
                ON cos.customer_order_id = il.customer_order_id
          WHERE c.created_at::DATE < CAST(NOW() AS DATE)
             AND c.created_at::DATE >= CAST(NOW() - '%s DAYS'::INTERVAL AS DATE)
            AND c.origin = 'son-video.com'
          ORDER BY c.created_at ASC
        SQL;

        $collection = $this->getSession()
            ->getQueryManager()
            ->query(sprintf($sql, $days_since));

        foreach ($collection as $item) {
            yield $item;
        }
    }
}
