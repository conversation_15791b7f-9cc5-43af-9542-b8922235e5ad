<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\CustomerOrderLine;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\UnavailableProduct as UnavailableProductStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;
use App\Sql\Query\QueryBuilder;
use PommProject\ModelManager\Model\CollectionIterator;

/**
 * DataParameterModel.
 *
 * Model class for table data_parameter.
 *
 * @see Model
 */
class UnavailableProductModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    private const COLUMNS_MAPPING = [
        'date' => 'p.date',
    ];

    public function __construct()
    {
        $this->structure = new UnavailableProductStructure();
        $this->flexible_entity_class = UnavailableProduct::class;
    }

    /**
     * find stats from unavailable products.
     *
     * @return array{date:string,total_quantity:int,tunover:float,sku_number:int}[]
     */
    public function findAllUnavailableProductFiltered(QueryBuilder $query_builder): array
    {
        $sql = <<<'SQL'
          SELECT
            p.date,
            SUM(p.quantity) as total_quantity,
            ROUND(SUM(l.total_net_excl_tax / l.quantity * p.quantity),2) as turnover,
            COUNT(DISTINCT l.reference) as sku_number
          FROM {table} p
          INNER JOIN {customer_order_line} l ON p.customer_order_line_id = l.customer_order_line_id
          WHERE {conditions}
          GROUP BY date
        SQL;

        $query_builder->setColumnsMapping(self::COLUMNS_MAPPING);

        $sql = strtr($sql, [
            '{table}' => $this->getStructure()->getRelation(),
            '{customer_order_line}' => (new CustomerOrderLine())->getRelation(),
            '{conditions}' => preg_replace('#(:[\w]+)#', '$*', $query_builder->getWhere()),
        ]);

        /** @var CollectionIterator $collection */
        $collection = $this->getSession()
            ->getQueryManager()
            ->query($sql, array_values($query_builder->getWhereParameters()));

        $result = [];
        foreach ($collection->extract() as $row) {
            $row['date'] = $row['date']->format('Y-m-d');
            $result[] = $row;
        }

        return $result;
    }
}
