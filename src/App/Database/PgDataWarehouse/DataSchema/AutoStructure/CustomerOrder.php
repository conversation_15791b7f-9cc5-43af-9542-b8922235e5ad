<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgDataWarehouse\DataSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * CustomerOrder.
 *
 * Structure class for relation data.customer_order.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 *
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 * @see RowStructure
 */
class CustomerOrder extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('data.customer_order')
            ->setPrimaryKey(['customer_order_id'])
            ->addField('customer_order_id', 'int4')
            ->addField('created_at', 'timestamp')
            ->addField('created_by', 'varchar')
            ->addField('origin', 'varchar')
            ->addField('source', 'varchar')
            ->addField('sales_location', 'varchar')
            ->addField('postal_code', 'varchar')
            ->addField('country', 'varchar')
            ->addField('is_btob', 'bool')
            ->addField('is_internal', 'bool')
            ->addField('last_modified_at', 'timestamp')
            ->addField('customer_id', 'int4')
            ->addField('quote_id', 'int4')
            ->addField('source_group', 'varchar')
            ->addField('customer_age', 'int4')
            ->addField('customer_gender', 'bpchar')
            ->addField('customer_since', 'timestamp')
            ->addField('customer_from', 'varchar')
            ->addField('shipping_is_billing', 'bool')
            ->addField('shipping_cost', 'numeric')
            ->addField('carrier_selected', 'varchar')
            ->addField('payment_method', 'varchar')
            ->addField('payment_created_at', 'timestamp')
            ->addField('payment_accepted_at', 'timestamp')
            ->addField('promo_code', 'varchar')
            ->addField('customer_hash', 'bpchar')
            ->addField('status_name', 'varchar');
    }
}
