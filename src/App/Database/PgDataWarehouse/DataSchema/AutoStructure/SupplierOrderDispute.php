<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgDataWarehouse\DataSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * SupplierOrderDispute.
 *
 * Structure class for relation data.supplier_order_dispute.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 *
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 * @see RowStructure
 */
class SupplierOrderDispute extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('data.supplier_order_dispute')
            ->setPrimaryKey(['date'])
            ->addField('date', 'date')
            ->addField('nb_supplier_order', 'int4')
            ->addField('nb_supplier_order_dispute', 'int4');
    }
}
