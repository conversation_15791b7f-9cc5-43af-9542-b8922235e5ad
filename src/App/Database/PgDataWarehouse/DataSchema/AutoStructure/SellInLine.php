<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgDataWarehouse\DataSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * SellInLine.
 *
 * Structure class for relation data.sell_in_line.
 * Weight in grams
 *
 * weight:
 * Weight in grams
 *
 * @see RowStructure
 */
class SellInLine extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('data.sell_in_line')
            ->setPrimaryKey(['sell_in_line_id'])
            ->addField('sell_in_line_id', 'int4')
            ->addField('quantity', 'int4')
            ->addField('purchase_cost', 'numeric')
            ->addField('ordering_at', 'timestamp')
            ->addField('delivering_at', 'timestamp')
            ->addField('ean_code', 'varchar')
            ->addField('reference', 'varchar')
            ->addField('model', 'varchar')
            ->addField('brand_id', 'int4')
            ->addField('brand', 'varchar')
            ->addField('supplier_order_id', 'int4')
            ->addField('supplier_id', 'int4')
            ->addField('supplier', 'varchar')
            ->addField('subcategory_id', 'int4')
            ->addField('subcategory', 'varchar')
            ->addField('category_id', 'int4')
            ->addField('category', 'varchar')
            ->addField('domain_id', 'int4')
            ->addField('domain', 'varchar')
            ->addField('warehouse', 'varchar')
            ->addField('weight', 'int4');
    }
}
