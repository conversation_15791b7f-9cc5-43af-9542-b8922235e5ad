<?php

namespace App\Database\Orm;

use Doctrine\DBAL\Exception\DriverException;

class DatabaseErrorExtractor
{
    public static function extract(DriverException $exception): string
    {
        $raw_message = $exception->getMessage();
        $matches = [];
        preg_match('#(?<error_message>SQLSTATE.*)#', $raw_message, $matches);

        return $matches['error_message'] ?? $raw_message;
    }
}
