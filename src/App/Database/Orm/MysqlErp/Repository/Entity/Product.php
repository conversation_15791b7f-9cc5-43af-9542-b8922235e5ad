<?php

declare(strict_types=1);

namespace App\Database\Orm\MysqlErp\Repository\Entity;

class Product
{
    public int $article_id;
    public string $sku;
    public string $type = 'article';
    public \DateTimeInterface $updated_at;
    public int $subcategory_id;
    public int $category_id;
    public int $domain_id;
    public float $vat_rate = 0.2;
    public ?float $markup_rate = null;
    public ?float $margin_rate = null;
    public float $margin = 0.0;
    public ?string $warranty_type = null;
}
