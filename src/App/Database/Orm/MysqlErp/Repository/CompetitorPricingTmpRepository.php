<?php

namespace App\Database\Orm\MysqlErp\Repository;

use App\Database\ConnectionProvider\AbstractMysqlErpRepository;
use App\Database\Orm\MysqlErp\Repository\Entity\CompetitorPricing;
use App\Database\Orm\MysqlErp\Repository\Model\CompetitorPricingTmpModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

/**
 * This is a temporary table used while importing competitor pricing with the new flux.
 */
final class CompetitorPricingTmpRepository extends AbstractMysqlErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = CompetitorPricingTmpModel::class;
    public const ENTITY_NAME = CompetitorPricing::class;

    /** @throws \Exception */
    public function linkSkus(): int
    {
        $sql = <<<'MYSQL'
        UPDATE backOffice.competitor_pricing_tmp cp
        INNER JOIN backOffice.BO_CTG_PDT_ART_ean ean ON ean.ean = cp.ean
        INNER JOIN backOffice.produit p ON p.id_produit = ean.BO_CTG_PDT_ART_article_id
        SET cp.sku = p.reference
        WHERE cp.sku IS NULL
        MYSQL;

        return $this->getConnection()->executeStatement($sql);
    }
}
