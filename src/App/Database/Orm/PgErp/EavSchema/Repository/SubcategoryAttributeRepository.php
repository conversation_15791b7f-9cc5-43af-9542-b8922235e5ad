<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\EavSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgErpRepository;
use App\Database\Orm\PgErp\EavSchema\Repository\Entity\SubcategoryAttribute;
use App\Database\Orm\PgErp\EavSchema\Repository\Model\SubcategoryAttributeModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class SubcategoryAttributeRepository extends AbstractPgErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = SubcategoryAttributeModel::class;
    public const ENTITY_NAME = SubcategoryAttribute::class;
}
