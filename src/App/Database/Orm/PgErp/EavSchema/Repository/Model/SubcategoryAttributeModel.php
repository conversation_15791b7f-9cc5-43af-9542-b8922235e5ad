<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\EavSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation eav.subcategory_attribute.
 *
 * @ORM\Model(name="eav.subcategory_attribute", engine="postgresql")
 */
class SubcategoryAttributeModel
{
    /** @ORM\Column(primary_key=true) */
    public int $subcategory_id;

    /** @ORM\Column(primary_key=true) */
    public int $attribute_id;

    /** @ORM\Column */
    public ?int $display_order = null;

    /** @ORM\Column */
    public ?string $filter_status = null;
}
