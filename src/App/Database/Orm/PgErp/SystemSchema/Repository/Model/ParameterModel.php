<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\SystemSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation system.parameter.
 *
 * @ORM\Model(name="system.parameter", engine="postgresql")
 */
class ParameterModel
{
    /** @ORM\Column(primary_key=true) */
    public string $name;

    /** @ORM\Column */
    public ?string $value = null;

    /** @ORM\Column */
    public ?string $description = null;
}
