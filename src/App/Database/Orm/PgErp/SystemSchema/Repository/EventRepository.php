<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\SystemSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgErpRepository;
use App\Database\Orm\PgErp\SystemSchema\Repository\Entity\Event;
use App\Database\Orm\PgErp\SystemSchema\Repository\Model\EventModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class EventRepository extends AbstractPgErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = EventModel::class;
    public const ENTITY_NAME = Event::class;
}
