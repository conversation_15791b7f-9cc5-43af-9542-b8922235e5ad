<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgDataWarehouseRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrder;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model\CustomerOrderModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class CustomerOrderRepository extends AbstractPgDataWarehouseRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = CustomerOrderModel::class;
    public const ENTITY_NAME = CustomerOrder::class;
}
