<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity;

class SellInLine
{
    public int $sell_in_line_id;
    public ?int $quantity = null;
    public ?float $purchase_cost = null;
    public ?\DateTimeInterface $ordering_at = null;
    public ?\DateTimeInterface $delivering_at = null;
    public ?string $ean_code = null;
    public ?string $reference = null;
    public ?string $model = null;
    public ?int $brand_id = null;
    public ?string $brand = null;
    public ?int $supplier_order_id = null;
    public ?int $supplier_id = null;
    public ?string $supplier = null;
    public ?int $subcategory_id = null;
    public ?string $subcategory = null;
    public ?int $category_id = null;
    public ?string $category = null;
    public ?int $domain_id = null;
    public ?string $domain = null;
    public ?string $warehouse = null;
    public ?int $weight = null;
}
