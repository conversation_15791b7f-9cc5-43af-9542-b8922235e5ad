<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity;

class SupplierContract
{
    public int $supplier_contract_id;
    public int $supplier_id;
    public int $year;
    public ?int $brand_id = null;
    public ?array $discount_description = [];
    public array $pam = [];
    public array $rfa = [];
    public array $additional_rewards = [];
    public float $unconditional_discount;
}
