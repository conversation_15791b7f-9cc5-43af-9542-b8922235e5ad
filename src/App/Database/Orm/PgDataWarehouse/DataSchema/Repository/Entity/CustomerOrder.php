<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity;

final class CustomerOrder
{
    public int $customer_order_id;
    public ?\DateTimeInterface $created_at = null;
    public ?string $created_by = null;
    public ?string $origin = null;
    public ?string $source = null;
    public ?string $sales_location = null;
    public ?string $postal_code = null;
    public ?string $country = null;
    public bool $is_btob = false;
    public bool $is_internal = false;
    public ?\DateTimeInterface $last_modified_at = null;
    public ?int $customer_id = null;
    public ?int $quote_id = null;
    public ?string $source_group = null;
    public ?int $customer_age = null;
    public ?string $customer_gender = null;
    public ?\DateTimeInterface $customer_since = null;
    public ?string $customer_from = null;
    public bool $shipping_is_billing = false;
    public ?float $shipping_cost = null;
    public ?string $carrier_selected = null;
    public ?string $payment_method = null;
    public ?\DateTimeInterface $payment_created_at = null;
    public ?\DateTimeInterface $payment_accepted_at = null;
    public ?string $promo_code = null;
    public ?string $customer_hash = null;
    public ?string $status_name = null;
}
