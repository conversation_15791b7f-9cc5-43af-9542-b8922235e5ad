<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity;

class SupplierOrderProduct
{
    public \DateTimeInterface $date;
    public ?int $supplier_id = null;
    public ?int $supplier_order_id = null;
    public int $supplier_order_product_id;
    public ?int $product_id = null;
    public ?float $ordered_quantity = null;
    public ?float $delivered_quantity = null;
    public ?\DateTimeInterface $expected_delivery_date = null;
    public ?\DateTimeInterface $actual_delivery_date = null;
    public ?bool $dispute = null;
    public ?float $buying_price = null;
    public ?string $supplier_order_type = null;
}
