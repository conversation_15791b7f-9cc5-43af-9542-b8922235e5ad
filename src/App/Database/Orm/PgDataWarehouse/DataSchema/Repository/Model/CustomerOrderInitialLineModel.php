<?php

declare(strict_types=1);
/**
 * CustomerOrderInitialLine Definition.
 *
 * Structure class for relation data.customer_order_initial_line.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.customer_order_initial_line.
 *
 * @ORM\Model(name="data.customer_order_initial_line", engine="postgresql")
 */
class CustomerOrderInitialLineModel
{
    /** @ORM\Column(primary_key=true) */
    public int $customer_order_product_initial_id;

    /** @ORM\Column */
    public ?string $reference = null;

    /** @ORM\Column */
    public ?string $model = null;

    /** @ORM\Column */
    public ?string $brand = null;

    /** @ORM\Column */
    public ?string $supplier = null;

    /** @ORM\Column */
    public ?int $quantity = null;

    /** @ORM\Column */
    public ?float $total_purchase_cost = null;

    /** @ORM\Column */
    public ?float $total_gross_excl_tax = null;

    /** @ORM\Column */
    public ?float $total_discount_excl_tax = null;

    /** @ORM\Column */
    public ?float $total_net_excl_tax = null;

    /** @ORM\Column */
    public ?float $total_guarantee_excl_tax = null;

    /** @ORM\Column */
    public ?float $total_margin = null;

    /** @ORM\Column */
    public ?string $subcategory = null;

    /** @ORM\Column */
    public ?int $subcategory_id = null;

    /** @ORM\Column */
    public ?string $category = null;

    /** @ORM\Column */
    public ?int $category_id = null;

    /** @ORM\Column */
    public ?string $domain = null;

    /** @ORM\Column */
    public ?int $domain_id = null;

    /** @ORM\Column */
    public ?int $customer_order_id = null;

    /** @ORM\Column */
    public ?float $vat_rate = null;

    /** @ORM\Column */
    public ?string $promo_code = null;

    /** @ORM\Column */
    public ?string $product_type = null;

    /** @ORM\Column */
    public ?int $available_quantity = null;

    /** @ORM\Column */
    public ?\DateTimeInterface $estimated_supplier_order_delivery_date = null;

    /** @ORM\Column */
    public ?\DateTimeInterface $customer_order_created_at = null;
}
