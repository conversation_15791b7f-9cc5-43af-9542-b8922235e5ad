<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Converter\Type\PgTimestampConverter;
use SonVideo\Orm\Definition as ORM;

/**
 * @ORM\Model(name="data.customer_order_payment_status", engine="postgresql")
 */
class CustomerOrderPaymentStatusModel
{
    /** @ORM\Column(primary_key=true) */
    public int $customer_order_payment_id;

    /** @ORM\Column(primary_key=true) */
    public string $name;

    /** @ORM\Column(type=PgTimestampConverter::class, primary_key=true) */
    public \DateTimeInterface $created_at;
}
