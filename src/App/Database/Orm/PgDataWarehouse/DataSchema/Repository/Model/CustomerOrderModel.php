<?php

declare(strict_types=1);
/**
 * CustomerOrder Definition.
 *
 * Structure class for relation data.customer_order.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Converter\Type\PgTimestampConverter;
use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.customer_order.
 *
 * @ORM\Model(name="data.customer_order", engine="postgresql")
 */
class CustomerOrderModel
{
    /** @ORM\Column(primary_key=true) */
    public int $customer_order_id;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $created_at = null;

    /** @ORM\Column */
    public ?string $created_by = null;

    /** @ORM\Column */
    public ?string $origin = null;

    /** @ORM\Column */
    public ?string $source = null;

    /** @ORM\Column */
    public ?string $sales_location = null;

    /** @ORM\Column */
    public ?string $postal_code = null;

    /** @ORM\Column */
    public ?string $country = null;

    /** @ORM\Column */
    public ?bool $is_btob = null;

    /** @ORM\Column */
    public ?bool $is_internal = null;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $last_modified_at = null;

    /** @ORM\Column */
    public ?int $customer_id = null;

    /** @ORM\Column */
    public ?int $quote_id = null;

    /** @ORM\Column */
    public ?string $source_group = null;

    /** @ORM\Column */
    public ?int $customer_age = null;

    /** @ORM\Column */
    public ?string $customer_gender = null;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $customer_since = null;

    /** @ORM\Column */
    public ?string $customer_from = null;

    /** @ORM\Column */
    public ?bool $shipping_is_billing = null;

    /** @ORM\Column */
    public ?float $shipping_cost = null;

    /** @ORM\Column */
    public ?string $carrier_selected = null;

    /** @ORM\Column */
    public ?string $payment_method = null;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $payment_created_at = null;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $payment_accepted_at = null;

    /** @ORM\Column */
    public ?string $promo_code = null;

    /** @ORM\Column */
    public ?string $customer_hash = null;

    /** @ORM\Column */
    public ?string $status_name = null;
}
