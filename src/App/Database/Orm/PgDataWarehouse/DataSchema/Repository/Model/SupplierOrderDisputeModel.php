<?php

declare(strict_types=1);
/**
 * SupplierOrderDispute Definition.
 *
 * Structure class for relation data.supplier_order_dispute.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.supplier_order_dispute.
 *
 * @ORM\Model(name="data.supplier_order_dispute", engine="postgresql")
 */
class SupplierOrderDisputeModel
{
    /** @ORM\Column(primary_key=true) */
    public \DateTimeInterface $date;

    /** @ORM\Column */
    public ?int $nb_supplier_order = null;

    /** @ORM\Column */
    public ?int $nb_supplier_order_dispute = null;
}
