<?php

declare(strict_types=1);
/**
 * DataParameter Definition.
 *
 * Structure class for relation data.data_parameter.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.data_parameter.
 *
 * @ORM\Model(name="data.data_parameter", engine="postgresql")
 */
class DataParameterModel
{
    /** @ORM\Column(primary_key=true) */
    public string $key;

    /** @ORM\Column */
    public ?string $value = null;
}
