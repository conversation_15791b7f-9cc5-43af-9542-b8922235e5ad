<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgDataWarehouseRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\ArticleSalePrice;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model\ArticleSalePriceModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class ArticleSalePriceRepository extends AbstractPgDataWarehouseRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = ArticleSalePriceModel::class;
    public const ENTITY_NAME = ArticleSalePrice::class;
}
