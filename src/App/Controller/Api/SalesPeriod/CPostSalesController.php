<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\SalesPeriod;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\SalesPeriod\Entity\SalesEntity;
use SonVideo\Erp\SalesPeriod\Mysql\Repository\SalesRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostSalesController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a list of sales.
     *
     * @throws \Exception
     * @Rest\Post("/api/v1/sales", name="cpost_sales")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="start_at DESC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="25",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @OA\Tag(name="Sales period")
     * @OA\Response(response=200, description="Retrieve a list of sales")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        SalesRepository $sales_repository
    ): JsonResponse {
        $params = $param_fetcher->all();

        $query_builder
            ->setWhere($param_fetcher->get('where') ?? [], SalesRepository::COLUMNS_MAPPING)
            ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
            ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

        $pager = $sales_repository->findAllPaginated($query_builder);

        $sales = $this->mapToEntitiesData($pager->getResults(), SalesEntity::class);

        return JSendResponse::success([
            'sales' => $sales,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    protected function getRouteName(): string
    {
        return 'cpost_sales';
    }
}
