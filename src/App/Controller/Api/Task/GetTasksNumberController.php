<?php

namespace App\Controller\Api\Task;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetTasksNumberController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/tasks",
     * name="get_tasks_nb")
     * @OA\Tag(name="Task")
     * @OA\Response(response=200, description="Get how many tasks")
     * @Security(name="Bearer")
     */
    public function __invoke(TaskRepository $repository): JsonResponse
    {
        try {
            $tasks_nb = $repository->countTasksByUser($this->getUser()->get('id_utilisateur'));
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['quantity' => $tasks_nb]);
    }
}
