<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Carrier;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeDto;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibleShipmentMethodDto;
use SonVideo\Erp\Carrier\Exception\ShipmentMethod\EligibilityInvalidEnvelopeException;
use SonVideo\Erp\Carrier\Manager\Eligibility\ShipmentMethodResolver;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

final class PostCarrierEligibleShipmentMethodsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Post("/api/v1/carrier/eligible-shipment-methods", name="post_carrier_eligible_shipment_methods")
     *
     * @Nelmio\Areas({"ezl"})
     * @OA\RequestBody(@Nelmio\Model(type=EligibilityEnvelopeDto::class))
     *
     * @Operation(
     *     tags={"Carrier"},
     *     summary="Retrieve a list of eligible shipment methods",
     *     @OA\Response(
     *         response="200",
     *         description="The eligible shipment methods information",
     *        @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref=@Nelmio\Model(type=EligibleShipmentMethodDto::class))
     *         )
     *     )
     * )
     *
     * @throws ExceptionInterface
     */
    public function __invoke(
        Request $request,
        SerializerInterface $serializer,
        ShipmentMethodResolver $shipment_method_resolver
    ): JsonResponse {
        try {
            /** @var EligibilityEnvelopeDto $envelope */
            $envelope = $serializer->deserialize($request->getContent(), EligibilityEnvelopeDto::class, 'json');
            $payload = $shipment_method_resolver->resolve($envelope);
        } catch (EligibilityInvalidEnvelopeException $exception) {
            $this->logger->error('[post_eligible_shipment_methods] An error occurred.', ['exception' => $exception]);

            // bad payload error messages are returned to the users for feedback
            return JSendResponse::error($exception->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error('[post_eligible_shipment_methods] An error occurred.', ['exception' => $exception]);

            // All the other errors are masked
            return JSendResponse::error('An error occurred while searching for the eligible shipment methods');
        }

        return JSendResponse::success($payload);
    }

    protected function getRouteName(): string
    {
        return 'post_carrier_eligible_shipment_methods';
    }
}
