<?php

namespace App\Controller\Api\EasyLounge;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Client\EasyLoungeApiClient;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class GetInvoiceController.
 */
class GetProductPricesController extends AbstractApiController
{
    /**
     * Get product competitors prices from easylounge.
     *
     * @Rest\Get("/api/v1/easy-lounge/product/{sku}/prices",
     *     requirements={"sku"="^[A-Z0-9]{1}[\-_A-Z0-9]{0,39}$"},
     *     name="get_easylounge_product_prices"
     * )
     * @OA\Tag(name="Easylounge Product prices")
     * @OA\Response(response=200, description="Get product competitors prices from easylounge by customer sku")
     * @Security(name="Bearer")
     */
    public function __invoke(string $sku, EasyLoungeApiClient $api_client): JsonResponse
    {
        try {
            $response = $api_client->get(sprintf('/product-api/product/%s/prices', $sku));
        } catch (\Exception $exception) {
            $error_code = $exception->getCode() >= 400 ? $exception->getCode() : 500;

            return JSendResponse::error($exception->getMessage(), $error_code);
        }

        return JSendResponse::success(json_decode($response, null, 512, JSON_THROW_ON_ERROR));
    }
}
