<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\EasyLounge;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Client\EasyLoungeApiClient;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class GetCustomerController.
 */
class GetCustomerController extends AbstractApiController
{
    /**
     * Get information of an easylounge customer using its email.
     *
     * @Rest\Get("/api/v1/easy-lounge/customer/{email}",
     * requirements={"email"="^\S+$"},
     * name="get_easylounge_customer")
     * @OA\Tag(name="Customer Order")
     * @OA\Response(response=200, description="Get information of an easylounge customer using its email")
     * @Security(name="Bearer")
     */
    public function __invoke(string $email, EasyLoungeApiClient $api_client): JsonResponse
    {
        try {
            $response = $api_client->get(sprintf('/customer-api/customer/%s', $email));
        } catch (\Exception $exception) {
            $error_code = $exception->getCode() >= 400 ? $exception->getCode() : 500;

            return JSendResponse::error($exception->getMessage(), $error_code);
        }

        return JSendResponse::success(json_decode($response, null, 512, JSON_THROW_ON_ERROR));
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'get_easylounge_customer';
    }
}
