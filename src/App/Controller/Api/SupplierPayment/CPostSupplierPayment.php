<?php

namespace App\Controller\Api\SupplierPayment;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\SupplierPayment\Entity\SupplierPaymentEntity;
use SonVideo\Erp\SupplierPayment\Mysql\Repository\SupplierPaymentRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

final class CPostSupplierPayment extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Post("/api/v1/supplier-payment", name="cpost_supplier_payment")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, allows returning a lighter payload")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="supplier_payment_id",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Rest\RequestParam(
     *     name="included_dependencies",
     *     default={},
     *     description="Array of dependencies to hydrate in the result")
     *
     * @OA\Tag(name="SupplierPayment")
     * @OA\Response(response=200, description="Get list of suppliers")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        SupplierPaymentRepository $repository
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], SupplierPaymentRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);

            $supplier_payment = ColumnHelper::intersect(
                $this->mapToEntitiesData($pager->getResults(), SupplierPaymentEntity::class),
                $param_fetcher->get('fields') ?? []
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'supplier_payment' => $supplier_payment,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }
}
