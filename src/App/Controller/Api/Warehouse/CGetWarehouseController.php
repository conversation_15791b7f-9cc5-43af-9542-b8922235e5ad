<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Warehouse;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Repository\WarehouseRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class CGetWarehouseController.
 */
class CGetWarehouseController extends AbstractApiController
{
    /**
     * Get list of warehouses.
     *
     * @Rest\Get("/api/v1/warehouses", name="cget_warehouse")
     *
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Page of the overview.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="50", description="Number of items per page.")
     * @OA\Tag(name="Warehouse")
     * @OA\Response(response=200, description="Get list of warehouses")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, WarehouseRepository $repository): JsonResponse
    {
        try {
            $params = $param_fetcher->all();
            $pager = $repository->getAllPaginated($params['page'], $params['limit'], 'ordre,nom_depot', [
                'is_active_bo' => 1,
            ]);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'warehouses' => JSendFormatter::convertNumeric($pager->getResults()),
            '_request' => JSendFormatter::convertNumeric($params),
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cget_warehouse';
    }
}
