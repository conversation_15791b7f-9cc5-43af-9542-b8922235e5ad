<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Customer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Customer\Manager\CustomerManager;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class PostCustomerAddressController.
 */
class PostCustomerAddressController extends AbstractApiController
{
    /**
     * Create a customer address on CMS.
     *
     * @Rest\Post("/api/v1/customer/{customer_id}/address",
     *     requirements={"customer_id"="^\d+$"},
     *     name="post_customer_address"
     * )
     * @Rest\RequestParam(
     *     name="address",
     *     default={},
     *     description="Array of address")
     *
     * @Operation(
     *     tags={"Customer"},
     *     summary="Create a customer address on CMS",
     *     @OA\Response(
     *         response="200",
     *         description="The info of the customer address was create successfully on CMS"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $customer_id,
        ParamFetcher $param_fetcher,
        CustomerManager $customer_manager
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $result = $customer_manager->createAddress($customer_id, $params['address']);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($result);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_customer_address';
    }
}
