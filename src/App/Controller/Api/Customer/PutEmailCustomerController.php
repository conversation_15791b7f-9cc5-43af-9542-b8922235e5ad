<?php

namespace App\Controller\Api\Customer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Customer\Manager\CustomerManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;

class PutEmailCustomerController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/customer/email", name="put_email_customer")
     *
     * @Rest\RequestParam(
     *     name="old_email",
     *     requirements={"old_email"="^\S+$"},
     *     description="Old customer email")
     *
     * @Rest\RequestParam(
     *     name="new_email",
     *     requirements={"new_email"="^\S+$"},
     *     description="New customer email")
     * @OA\Tag(name="Customer")
     * @OA\Response(response=204, description="Update the customer's email")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, CustomerManager $customer_manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::CUSTOMER_EMAIL_UPDATE]);

        $old_email = $param_fetcher->get('old_email');
        $new_email = $param_fetcher->get('new_email');

        try {
            $customer_manager->updateEmail($old_email, $new_email);

            return JSendResponse::noContent();
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'put_email_customer';
    }
}
