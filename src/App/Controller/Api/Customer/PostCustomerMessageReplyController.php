<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Customer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\CustomerMessage\Manager\CustomerMessageManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class PostCustomerMessageReplyController extends AbstractApiController
{
    /**
     * Reply to a customer messages.
     *
     * @Rest\Post(
     *     "/api/v1/customer-message/{id}/reply",
     *     requirements={"id"="^\d+$"},
     *     name="post_customer_message_reply"
     * )
     *
     * @Rest\RequestParam(
     *     name="message",
     *     requirements={@Assert\Type("string"), @Assert\NotBlank()},
     *     description="Message to reply"
     * )
     *
     * @Operation(
     *     tags={"Customer"},
     *     summary="Reply to a customer message",
     *     description="Reply to a customer message",
     *     @OA\Response(
     *         response="201",
     *         description="Reply sent"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        string $id,
        ParamFetcher $param_fetcher,
        CustomerMessageManager $customer_message_manager
    ): JsonResponse {
        try {
            $customer_message_manager->reply($id, $this->getUser(), $param_fetcher->get('message'));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::noContent();
    }

    protected function getRouteName(): string
    {
        return 'post_customer_message_reply';
    }
}
