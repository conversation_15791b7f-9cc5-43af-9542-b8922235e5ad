<?php

namespace App\Controller\Api\PricingStrategy;

use App\Adapter\Serializer\SerializerInterface;
use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\PricingStrategy\Dto\CreationContext\PricingStrategyCreationContextDto;
use SonVideo\Erp\PricingStrategy\Manager\PricingStrategyManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostPricingStrategyController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/pricing-strategy",
     *     name="post_pricing_strategy")
     *
     * @OA\RequestBody(@Nelmio\Model(type=PricingStrategyCreationContextDto::class))
     *
     * @Operation(
     *     tags={"Pricing Strategy"},
     *     summary="Create a pricing strategy",
     *     description="Create a pricing strategy",
     *     @OA\Response(
     *         response="200",
     *         description="Return the id of the created pricing strategy"
     *     )
     * )
     */
    public function __invoke(
        Request $request,
        SerializerInterface $serializer,
        PricingStrategyManager $manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            /** @var PricingStrategyCreationContextDto $pricing_strategy_dto */
            $pricing_strategy_dto = $serializer->deserialize(
                $request->getContent(),
                PricingStrategyCreationContextDto::class,
                'json'
            );

            return JSendResponse::success(['pricing_strategy_id' => $manager->create($pricing_strategy_dto)]);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST, $exception->getContext());
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
