<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\ExpectedDelivery;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SupplierOrderProduct\Mysql\Repository\ExpectedDeliveryRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteExpectedDeliveryController extends AbstractApiController
{
    /**
     * @Rest\Delete(
     *     "/api/v1/expected-delivery/{expected_delivery_id}",
     *     requirements={"expected_delivery_id"="^\d+$"},
     *     name="delete_expected_delivery"
     * )
     *
     * @Operation(
     *     tags={"ExpectedDelivery"},
     *     summary="Delete an expected delivery",
     *     description="Delete an expected delivery on a supplier order",
     *     @OA\Response(
     *         response="200",
     *         description="Delete an expected delivery"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(int $expected_delivery_id, ExpectedDeliveryRepository $repository): JsonResponse
    {
        $this->checkAuthorization([UserPermission::SUPPLIER_ORDER_UPDATE]);

        try {
            $deleted = $repository->delete($expected_delivery_id);
        } catch (NotFoundException $exception) {
            $this->logger->warning($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::internalError($exception);
        } catch (\Exception $exception) {
            $this->logger->warning($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'deleted' => $deleted,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'delete_expected_delivery';
    }
}
