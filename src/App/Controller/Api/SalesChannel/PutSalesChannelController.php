<?php

namespace App\Controller\Api\SalesChannel;

use App\Adapter\Serializer\SerializerInterface;
use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SalesChannel\Dto\SalesChannelContextDto;
use SonVideo\Erp\SalesChannel\Manager\SalesChannelManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PutSalesChannelController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/sales-channel/{sales_channel_id}",
     *   requirements={"sales_channel_id"="^\d+$"},
     *   name="put_sales_channel")
     *
     * @OA\RequestBody(@Nelmio\Model(type=SalesChannelContextDto::class))
     *
     * @Operation(
     *     tags={"Sales channel"},
     *     summary="Update a sales channel",
     *     @OA\Response(
     *         response="200",
     *         description="ok - if the sales channel was updated successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $sales_channel_id,
        Request $request,
        SalesChannelManager $sales_channel_manager,
        SerializerInterface $serializer
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_BUYERS_WRITE]);

        try {
            /** @var SalesChannelContextDto $sales_channel */
            $sales_channel = $serializer->deserialize($request->getContent(), SalesChannelContextDto::class, 'json');

            if ($sales_channel->sales_channel_id !== $sales_channel_id) {
                throw new \InvalidArgumentException('Invalid parameters');
            }

            $updated = $sales_channel_manager->update($sales_channel);

            return JSendResponse::success(['updated' => $updated]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
