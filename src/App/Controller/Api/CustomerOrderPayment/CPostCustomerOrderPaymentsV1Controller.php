<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\CustomerOrderPayment;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\CustomerOrderPayment\Manager\TransactionStateDecorator;
use SonVideo\Erp\Repository\CustomerOrder\Payment\CustomerOrderPaymentRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostCustomerOrderPaymentsV1Controller extends AbstractApiController
{
    /**
     * Retrieve a filtered list of customer order payments.
     *
     * @Rest\Post("/api/v1/customer-order-payments", name="cpost_customer_order_payments_v1")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     default={},
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="sort_by",
     *     requirements=@Assert\Type("string"),
     *     default="creation_date",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="sort_direction",
     *     requirements=@Assert\Type("string"),
     *     default="asc",
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     * @Rest\RequestParam(
     *     name="limit",
     *     default="9999",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @OA\Tag(name="Customer order payments")
     * @OA\Response(response=200, description="Retrieve a filtered list of customer order payments")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        CustomerOrderPaymentRepository $repository,
        QueryBuilder $query_builder,
        TransactionStateDecorator $transaction_state_decorator
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();

            $query_builder
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('sort_by'), $param_fetcher->get('sort_direction'))
                ->setWhere($params['where']);

            $pager = $repository->findAllPaginated($query_builder);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'payments' => JSendFormatter::convertNumeric($pager->getResults()),
            '_request' => JSendFormatter::convertNumeric($params),
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_customer_order_payments_v1';
    }
}
