<?php

namespace App\Controller\Api\Sticker;

use App\Contract\CarrierV2ClientInterface;
use App\Controller\Api\AbstractApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\Response;

class GetStickerController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/sticker/{shipment_parcel_id}", name="get_sticker")
     *
     * @Security(name="Bearer")
     */
    public function __invoke(int $shipment_parcel_id, CarrierV2ClientInterface $http_client): Response
    {
        $this->checkAuthorization([UserPermission::SHIPMENT_READ]);

        return $http_client->getSticker($shipment_parcel_id);
    }
}
