<?php

namespace App\Controller\Api\SellerCommission;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SellerCommission\Mysql\Repository\CommissionRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetCommissionsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a list of sellers commissions.
     *
     * @Rest\Get("/api/v1/seller-commission/commissions",
     * name="get_seller_commission_commissions")
     *
     * @Rest\QueryParam(name="type", nullable=false, requirements="^(retail_store|call_center)$", description="Type of sellers")
     * @Rest\QueryParam(name="at", nullable=true, requirements="\d{4}-\d{2}", description="Month to find commissions.")
     *
     * @OA\Tag(name="Seller commission")
     * @OA\Response(response=200, description="Retrieve a list of sellers commissions")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        CommissionRepository $commission_repository,
        QueryBuilder $query_builder
    ): JsonResponse {
        $params = $param_fetcher->all(true);
        $wheres = [];
        $roles = [];

        switch ($params['type']) {
            case 'retail_store':
                $this->checkAuthorization([UserPermission::RETAIL_STORE_COMMISSIONS_READ]);
                $roles = ['RETAIL_STORE_MANAGER', 'RETAIL_STORE_SELLER'];

                if (!$this->hasAuthorization(UserPermission::RETAIL_STORE_ALL_COMMISSIONS_READ)) {
                    $wheres['retail_store_id'] = ['_in' => [$this->getUser()->warehouse_id]];
                }

                break;
            case 'call_center':
                $this->checkAuthorization([UserPermission::CALL_CENTER_COMMISSIONS_READ]);
                $roles = ['CALL_CENTER_MANAGER', 'CALL_CENTER_SELLER'];
                break;
        }

        $wheres['warehouse_role'] = ['_in' => $roles];

        try {
            $at = (new \DateTime($params['at']))->format('Y-m-01');

            $query_builder->setWhere($wheres, CommissionRepository::COLUMNS_MAPPING);
            $commissions = $commission_repository->findAllAtWhere($at, $query_builder);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['commissions' => $commissions]);
    }

    protected function getRouteName(): string
    {
        return 'get_seller_commission_commissions';
    }
}
