<?php

namespace App\Controller\Api\SellerCommission;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SellerCommission\Mysql\Repository\AllCommissionsRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetAllCommissionsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a list of sellers commissions.
     *
     * @Rest\Get("/api/v1/seller-commission/all-commissions",
     * name="get_seller_commission_all_commissions")
     *
     * @Rest\QueryParam(name="at", nullable=true, requirements="\d{4}-\d{2}", description="Month to find commissions.")
     *
     * @OA\Tag(name="Seller commission")
     * @OA\Response(response=200, description="Retrieve a list of sellers commissions")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        AllCommissionsRepository $commission_repository,
        QueryBuilder $query_builder
    ): JsonResponse {
        $params = $param_fetcher->all(true);

        $this->checkAuthorization([UserPermission::ALL_COMMISSIONS_READ]);

        try {
            $at = new \DateTime($params['at']);
            $begin = $at->format('Y-m-01');
            $end = $at->format('Y-m-31');

            $commissions = $commission_repository->findAllBetween($begin, $end);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['commissions' => $commissions]);
    }
}
