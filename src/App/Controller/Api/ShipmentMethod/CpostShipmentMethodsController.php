<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\ShipmentMethod;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Carrier\Entity\ShipmentMethodEntity;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CpostShipmentMethodsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a filtered list of Shipment methods.
     *
     * @Rest\Post("/api/v1/shipment-methods", name="cpost_shipment_methods")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, allows returning a lighter payload")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="shipment_method_id ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Rest\RequestParam(
     *     name="included_dependencies",
     *     default={},
     *     requirements=@Assert\All({
     *         @Assert\Choice({"carrier"}, message="{{ value }} does not exists, available keys are : {{ choices }}")
     *     }),
     *     description="Array of dependencies to hydrate in the result")
     *
     * @Operation(
     *     tags={"Shipment method"},
     *     summary="Retrieve a filtered list of shipment methods",
     *     description="The shipment_methods can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of shipment methods"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        ShipmentMethodReadRepository $repository
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], ShipmentMethodReadRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'))
                ->setIncludedDependencies($params['included_dependencies']);

            $pager = $repository->findAllPaginated($query_builder);

            $shipment_methods = ColumnHelper::intersect(
                $this->mapToEntitiesData(array_values($pager->getResults()), ShipmentMethodEntity::class),
                $param_fetcher->get('fields') ?? []
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'shipment_methods' => $shipment_methods,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_shipment_methods';
    }
}
