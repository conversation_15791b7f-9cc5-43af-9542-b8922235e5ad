<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Quote;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use App\Formatter\String\StringFormatter;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Quote\Contract\QuoteRetrieverTrait;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostQuoteInternalCommentController extends AbstractApiController
{
    use QuoteRetrieverTrait;

    /**
     * @Rest\Post("/api/v1/quote/{quote_id}/internal-comment",
     *   requirements={"quote_id"="^\d+$"},
     *   name="post_quote_internal_comment")
     *
     * @Rest\RequestParam(
     *   name="data",
     *   default={},
     *   requirements=@Assert\Type("array"))
     *
     * @OA\Tag(name="Quote")
     * @OA\Response(response=200, description="Create an internal comment")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $quote_id,
        ParamFetcher $param_fetcher,
        SystemEventLogger $system_event_logger,
        QuoteRepository $quote_repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::QUOTE_WRITE]);

        $data = (array) $param_fetcher->get('data');

        if ([] === $data) {
            return JSendResponse::error('No data supplied', Response::HTTP_BAD_REQUEST);
        }

        try {
            $this->retrieveQuote($quote_id, $quote_repository);
            $user = $this->getUser();

            $system_event_logger->log(
                new LoggableSystemEvent(LoggableSystemEvent::SYSTEM_ERP_MYSQL, 'quote.internal_comment', [
                    '_rel' => [
                        'quote' => $quote_id,
                    ],
                    'data' => [
                        'message' => StringFormatter::transliterate(strip_tags($data['message'])),
                    ],
                    'meta' => [
                        'created_by' => [
                            'user_id' => $user->id_utilisateur,
                            'username' => $user->utilisateur,
                            'firstname' => $user->prenom,
                            'lastname' => $user->nom,
                        ],
                    ],
                ])
            );
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_quote_internal_comment';
    }
}
