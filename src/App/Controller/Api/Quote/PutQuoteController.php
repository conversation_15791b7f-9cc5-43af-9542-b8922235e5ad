<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Quote;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Quote\Manager\QuoteManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PutQuoteController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/quote/{quote_id}",
     *   requirements={"quote_id"="^\d+$"},
     *   name="put_quote")
     * @Rest\RequestParam(
     *   name="data",
     *   default={},
     *   requirements=@Assert\Type("array"))
     *
     * @Operation(
     *     tags={"Quote"},
     *     summary="Update a quote with specified fields",
     *     @OA\Response(
     *         response="200",
     *         description="ok - if the quote was updated successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(int $quote_id, ParamFetcher $param_fetcher, QuoteManager $quote_manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::QUOTE_WRITE]);

        try {
            $updated = $quote_manager->update($quote_id, (array) $param_fetcher->get('data'), $this->getUser());

            return JSendResponse::success(['updated' => $updated]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\UnexpectedValueException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'put_quote';
    }
}
