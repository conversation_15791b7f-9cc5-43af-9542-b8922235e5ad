<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Quote;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Quote\Manager\QuoteCreator;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class PostQuoteController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/quote",
     *     name="post_quote")
     *
     * @Rest\RequestParam(
     *     name="customer_id",
     *     nullable=false,
     *     requirements=@Assert\Type("int"),
     *     description="ID of the customer")
     * @OA\Tag(name="Quote")
     * @OA\Response(response=200, description="Create a quote")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, QuoteCreator $quote_creator): JsonResponse
    {
        $this->checkAuthorization([UserPermission::QUOTE_WRITE]);

        $params = $param_fetcher->all();

        try {
            $quote_id = $quote_creator->create($params, $this->getUser());
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['quote_id' => $quote_id]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_quote';
    }
}
