<?php

namespace App\Controller\Api\Quote;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Quote\Manager\QuoteLineUpdater;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PutQuoteLineController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Put(
     *  "/api/v1/quote/{quote_id}/quote-line/{quote_line_id}",
     *   requirements={"quote_id"="^\d+$"},
     *   name="put_quote_line"
     * )
     *
     * @Rest\RequestParam(
     *   name="display_order",
     *   requirements=@Assert\Type("integer")
     * )
     * @Operation(
     *     tags={"Quote"},
     *     summary="change the position of a line in a quote",
     *     @OA\Response(
     *         response="200",
     *         description="Return an updated list of quote lines for the specified quote_id"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $quote_id,
        int $quote_line_id,
        ParamFetcher $param_fetcher,
        QuoteLineUpdater $quote_line_update
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::QUOTE_WRITE]);

        try {
            $results = $quote_line_update->moveQuoteLine(
                $quote_id,
                $quote_line_id,
                $param_fetcher->get('display_order')
            );

            $response = JSendResponse::success(['quote' => $results]);
        } catch (NotFoundException $exception) {
            $response = JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            $response = JSendResponse::internalError($exception);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            $response = JSendResponse::error($exception->getMessage());
        }

        return $response;
    }
}
