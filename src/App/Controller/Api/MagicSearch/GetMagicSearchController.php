<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\MagicSearch;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\MagicSearch\Collection\MagicSearchQueryProviderCollection;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;
use SonVideo\Erp\MagicSearch\Manager\MagicSearchQueryMaker;
use <PERSON>ymfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

final class GetMagicSearchController extends AbstractApiController implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    /**
     * @Rest\Get("/api/v1/magic-search", name="get_magic_search")
     *
     * @Rest\QueryParam(name="search_terms", requirements=@Assert\NotNull, description="Search terms to search against.")
     * @Rest\QueryParam(name="context", default="all", description="Context of the current search.")
     * @Rest\QueryParam(name="size", default="15", description="How many items should be fetched for each context.")
     * @Rest\QueryParam(name="from", default="0", description="From where do we start (pagination).")
     *
     * @OA\Tag(name="Magic search")
     * @OA\Response(response=200, description="Search various data against given search terms")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        MagicSearchQueryProviderCollection $collection,
        MagicSearchQueryMaker $magic_search
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();

            /** @var MagicSearchHttpRequestPayload $dto */
            $dto = $this->serializer->denormalize($params, MagicSearchHttpRequestPayload::class);

            /** @var MagicSearchQueryProviderInterface[] $search_providers */
            $search_providers = $collection->getHandlers($dto->context);

            $results = $magic_search->make($dto, $search_providers);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(
            array_merge($results, [
                '_request' => $param_fetcher->all(),
            ])
        );
    }
}
