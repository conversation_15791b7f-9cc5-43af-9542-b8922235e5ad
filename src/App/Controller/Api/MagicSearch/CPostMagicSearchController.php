<?php

namespace App\Controller\Api\MagicSearch;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\MagicSearch\Collection\MagicSearchQueryProviderCollection;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;
use SonVideo\Erp\MagicSearch\Manager\MagicSearchQueryMaker;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

final class CPostMagicSearchController extends AbstractApiController implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    /**
     * Search various data against given search terms.
     *
     * @Rest\Post("/api/v1/magic-search", name="cpost_magic_search")
     *
     * @OA\RequestBody(@Nelmio\Model(type=MagicSearchHttpRequestPayload::class))
     *
     * @OA\Tag(name="Magic search")
     * @OA\Response(response=200, description="Search various data against given search terms")
     * @Security(name="Bearer")
     */
    public function __invoke(
        Request $request,
        ParamFetcher $param_fetcher,
        MagicSearchQueryProviderCollection $collection,
        MagicSearchQueryMaker $magic_search
    ): JsonResponse {
        try {
            /** @var MagicSearchHttpRequestPayload $dto */
            $dto = $this->serializer->deserialize($request->getContent(), MagicSearchHttpRequestPayload::class, 'json');

            /** @var MagicSearchQueryProviderInterface[] $search_providers */
            $search_providers = $collection->getHandlers($dto->context);

            $results = $magic_search->make($dto, $search_providers);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(
            array_merge($results, [
                '_request' => $param_fetcher->all(),
            ])
        );
    }
}
