<?php

namespace App\Controller\Api\KnowledgeBase;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\KnowledgeBase\Manager\Media;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostMediaUploadController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/knowledge-base/media/upload",
     * name="post_knowledge_base_media_upload")
     *
     * @Operation(
     *     tags={"knowledge base"},
     *     summary="MediaUpload an media",
     *     description="MediaUpload an media",
     *     @OA\Response(
     *         response="200",
     *         description="The path to the newly created file"
     *     )
     * )
     */
    public function __invoke(Request $request, Media $media): JsonResponse
    {
        try {
            if (!$request->files->has('file')) {
                throw new \InvalidArgumentException('"file" key name was not found on the request payload');
            }

            $uploaded_file = $request->files->get('file');

            if (false === $uploaded_file->isValid()) {
                throw new \InvalidArgumentException('You must supply a file to upload');
            }

            return JSendResponse::success(['target_path' => $media->moveToFinalPath($uploaded_file)]);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
