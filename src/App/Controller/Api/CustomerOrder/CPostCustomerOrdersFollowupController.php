<?php

namespace App\Controller\Api\CustomerOrder;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrdersFollowup;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class CPostCustomerOrdersFollowupController extends AbstractApiController
{
    /**
     * Get paginated list of customer orders.
     *
     * @Rest\Post("/api/v1/customer-orders/followup", name="cpost_customer_orders_followup")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="created_at desc",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="9999",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @OA\Tag(name="Customer Orders")
     * @OA\Response(response=200, description="Get list of customer orders")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        CustomerOrdersFollowup $paid_orders_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::STATISTICS_GLOBAL_READ]);

        try {
            $pager = $paid_orders_manager->getCustomerOrders(
                $param_fetcher->get('where') ?? [],
                $param_fetcher->get('page'),
                $param_fetcher->get('limit'),
                $param_fetcher->get('order_by')
            );
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'customer_orders' => $pager->getResults(),
            '_request' => $param_fetcher->all(),
            '_pager' => $pager,
        ]);
    }
}
