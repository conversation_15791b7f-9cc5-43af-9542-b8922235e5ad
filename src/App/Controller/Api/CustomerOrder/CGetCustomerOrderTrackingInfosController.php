<?php

namespace App\Controller\Api\CustomerOrder;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Shipment\Mysql\Repository\ShipmentRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class CGetCustomerOrderTrackingInfosController extends AbstractApiController
{
    /**
     * Get all tracking infos for a customer order.
     *
     * @Rest\Get("/api/v1/customer-order/{customer_order_id}/tracking-infos",
     * requirements={"customer_order_id"="^\d+$"},
     * name="cget_customer_order_tacking_infos")
     * @OA\Tag(name="Customer Order")
     * @OA\Response(response=200, description="All tracking infos for a customer order",
     *     @OA\JsonContent(
     *         type="array",
     *         @OA\Items(ref=@Model(type=\SonVideo\Erp\Shipment\Entity\ShipmentTrackingInfoEntity::class, groups={"full"}))
     *      )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(int $customer_order_id, ShipmentRepository $shipment_repository): JsonResponse
    {
        try {
            return JSendResponse::success([
                'tracking_infos' => $shipment_repository->getTrackingInfoByCustomerOrderId($customer_order_id),
            ]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }
    }
}
