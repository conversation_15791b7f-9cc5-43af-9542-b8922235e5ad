<?php

namespace App\Controller\Api\CustomerOrder;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use OpenApi\Annotations as OA;
use SonVideo\Erp\BusinessRule\CustomerOrder\CarrierBusinessRule;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderForEditionPageReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetCustomerOrderForEditionPageController extends AbstractApiController
{
    /**
     * Get complete information's on a customer order specifically for the edition page in ERP Client.
     *
     * @Rest\Get("/api/v1/customer-order/{customer_order_id}/for-edition-page",
     * requirements={"customer_order_id"="^\d+$"},
     * name="get_customer_order_v2")
     *
     * @Nelmio\Operation(
     *     tags={"Customer Order"},
     *     @OA\Response(
     *         response="200",
     *         description="The requested customer order",
     *        @OA\JsonContent(
     *           type="array",
     *           @OA\Items(ref=@Nelmio\Model(type=\SonVideo\Erp\CustomerOrder\Entity\CustomerOrderForEditionPageEntity::class))
     *        )
     *     )
     * )
     * @Nelmio\Security(name="Bearer")
     */
    public function __invoke(
        int $customer_order_id,
        CarrierBusinessRule $carrier_business_rule,
        CustomerOrderForEditionPageReadRepository $customer_order_repository
    ): JsonResponse {
        try {
            $this->handleInconsistentCarrierFor($customer_order_id, $carrier_business_rule);

            return JSendResponse::success($customer_order_repository->findOneById($customer_order_id));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function handleInconsistentCarrierFor(
        int $customer_order_id,
        CarrierBusinessRule $carrier_business_rule
    ): void {
        $is_carrier_inconsistent = $carrier_business_rule->isInconsistentForCustomerOrder($customer_order_id);
        $carrier_business_rule->logInconsistentCarrierIfNeeded($customer_order_id, $is_carrier_inconsistent);
    }
}
