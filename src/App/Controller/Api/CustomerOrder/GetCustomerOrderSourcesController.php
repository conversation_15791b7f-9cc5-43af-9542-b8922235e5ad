<?php

namespace App\Controller\Api\CustomerOrder;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderSourceManager;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetCustomerOrderSourcesController extends AbstractApiController
{
    /**
     * Get list of customer order sources.
     *
     * @Rest\Get("/api/v1/customer-order/sources")
     *
     * @OA\Tag(name="Customer Order")
     * @OA\Response(response=200, description="get list of sources")
     * @Security(name="Bearer")
     */
    public function __invoke(CustomerOrderSourceManager $manager): JsonResponse
    {
        try {
            $response = $manager->findAllSources();
        } catch (\Throwable $e) {
            JSendResponse::error($e->getMessage());
        }

        return JSendResponse::success([
            'sources' => $response,
        ]);
    }
}
