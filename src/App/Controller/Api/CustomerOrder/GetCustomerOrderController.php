<?php

namespace App\Controller\Api\CustomerOrder;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\BusinessRule\CustomerOrder\CarrierBusinessRule;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetCustomerOrderController extends AbstractApiController implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    /**
     * Get information on a customer order.
     *
     * @Rest\Get("/api/v1/customer-order/{customer_order_id}",
     * requirements={"customer_order_id"="^\d+$"},
     * name="get_customer_order")
     *
     * @OA\Tag(name="Customer Order")
     * @OA\Response(
     *     response=200,
     *     description="Meta information on the customer order",
     *         @Model(type=\SonVideo\Erp\Entity\OrderEntity::class, groups={"full"})
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $customer_order_id,
        CarrierBusinessRule $carrier_business_rule,
        CustomerOrderRepository $customer_order_repository
    ): JsonResponse {
        try {
            $this->handleInconsistentCarrierFor($customer_order_id, $carrier_business_rule);

            $response = $customer_order_repository->findOneById($customer_order_id);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($this->serializer->normalize($response));
    }

    protected function handleInconsistentCarrierFor(int $customer_order_id, CarrierBusinessRule $carrier_business_rule)
    {
        $is_carrier_inconsistent = $carrier_business_rule->isInconsistentForCustomerOrder($customer_order_id);
        $carrier_business_rule->logInconsistentCarrierIfNeeded($customer_order_id, $is_carrier_inconsistent);
    }
}
