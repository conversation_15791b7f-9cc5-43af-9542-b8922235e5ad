<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Warehouse;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Repository\WarehouseRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class CGetWarehouseParametersController.
 */
class CGetWarehouseParametersController extends AbstractApiController
{
    /**
     * Get all parameters for a warehouse.
     *
     * @Rest\Get("/api/v1/wms/warehouse/{warehouse_id}/parameters",
     * requirements={"warehouse_id"="^\d+$"},
     * name="cget_warehouse_parameters")
     * @OA\Tag(name="Warehouse parameters")
     * @OA\Response(response=200, description="All warehouse parameters")
     * @Security(name="Bearer")
     */
    public function __invoke(int $warehouse_id, WarehouseRepository $repository): JsonResponse
    {
        try {
            $response = $repository->findAllParameters($warehouse_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), JsonResponse::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'warehouse_parameters' => $response,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cget_warehouse_parameters';
    }
}
