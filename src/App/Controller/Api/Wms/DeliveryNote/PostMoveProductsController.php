<?php
/*
 * This file is part of erp package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Formatter\Http\JSendResponse;
use App\Validator\Constraint as CustomAssert;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Process\DeliveryNote\ProductMover;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\LocationRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class PostMoveProductsController.
 */
class PostMoveProductsController extends AbstractApiController
{
    /**
     * Post move products.
     *
     * @Rest\Post("/api/v1/wms/delivery-note/move-products",
     * name="post_move_products")
     *
     * @Rest\RequestParam(
     *     name="origin_location",
     *     requirements={"origin_location"="^\d+$"},
     *     description="Origin location"
     * )
     * @Rest\RequestParam(
     *     name="destination_location",
     *     requirements={"destination_location"="^\d+$"},
     *     description="Destination location"
     * )
     * @Rest\RequestParam(
     *     name="delivery_ticket_ids",
     *     description="Delivery ticket ids, ordered"
     * )
     * @Rest\RequestParam(
     *     name="products",
     *     requirements=@CustomAssert\ProductsQuantities,
     *     description="List of products to move"
     * )
     *
     * @OA\Tag(name="Delivery note")
     * @OA\Response(response=200, description="Move products linked to multiple delivery notes")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        LocationRepository $location_repository,
        ProductMover $product_mover
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::PRODUCT_MOVE_CREATE]);

        try {
            $result_summary = $product_mover->moveMultipleProducts(
                $param_fetcher->get('origin_location'),
                $param_fetcher->get('destination_location'),
                $param_fetcher->get('delivery_ticket_ids'),
                $param_fetcher->get('products'),
                $this->getUser()
            );
        } catch (InternalErrorException $exception) {
            return JSendResponse::internalError($exception);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($result_summary);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_delivery_ticket_move_products';
    }
}
