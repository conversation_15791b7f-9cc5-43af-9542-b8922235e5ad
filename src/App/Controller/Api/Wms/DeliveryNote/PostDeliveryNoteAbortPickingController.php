<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Exception\SqlErrorMessageException;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Referential\DeliveryTicketLogStatus;
use SonVideo\Erp\Repository\Shipping\DeliveryTicketActivityLogWriteRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class PostDeliveryNoteAbortPickingController.
 */
class PostDeliveryNoteAbortPickingController extends AbstractApiController
{
    /**
     * Abort picking on a delivery note.
     *
     * @Rest\Post("/api/v1/wms/delivery-note/{delivery_note_id}/abort-picking",
     * requirements={"delivery_note_id"="^\d+$"},
     * name="post_wms_delivery_note_abort_picking")
     *
     * @Operation(
     *     tags={"WMS"},
     *     description="Abort picking on a delivery note",
     *     @OA\Response(
     *         response="200",
     *         description="Return the id of the aborted delivery note"
     *     )
     * )
     *
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $delivery_note_id,
        DeliveryTicketActivityLogWriteRepository $delivery_ticket_repository,
        DeliveryNoteRepository $delivery_note_repository
    ): JsonResponse {
        try {
            $delivery_ticket_repository->write(
                $delivery_note_id,
                DeliveryTicketLogStatus::PICKING_ABORTED,
                $this->getUser()->id_utilisateur
            );

            $delivery_note_repository->autoArrangeVirtualProducts($delivery_note_id);
        } catch (SqlErrorMessageException $exception) {
            return new JsonResponse(JSendFormatter::error($exception->getMessage(), 1000), Response::HTTP_BAD_REQUEST);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error(
                sprintf(
                    'An error occurred while attempting to log a picking abortion for the BL "%s"',
                    $delivery_note_id
                ),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }

        return JSendResponse::success(['delivery_note_id' => $delivery_note_id]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_wms_delivery_note_abort_picking';
    }
}
