<?php

namespace App\Controller\Api\Wms\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Wms\Manager\ParcelUpdater;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PutWmsParcelController extends AbstractApiController
{
    /**
     * Put Parcel.
     *
     * @Rest\Put(
     *     "/api/v1/wms/parcel/{parcel_id}",
     *      requirements={"parcel_id"="^\d+$"},
     *      name="put_wms_parcel"
     * )
     *
     * @Rest\RequestParam(
     *     name="parcel_number",
     *     requirements=@Assert\Type("string")
     * )
     *
     * @OA\Tag(name="WMS Parcel")
     * @OA\Response(response=200, description="Update parcel number")
     * @Security(name="Bearer")
     */
    public function __invoke(int $parcel_id, ParamFetcher $params, ParcelUpdater $updater): JsonResponse
    {
        $this->checkAuthorization([UserPermission::DELIVERY_NOTE_WRITE]);

        try {
            $updater->updateParcelNumber($parcel_id, $params->get('parcel_number'));
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([]);
    }
}
