<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryCollectWriteRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class PostInventoryArticleCountController.
 */
class PostInventoryArticleCountController extends AbstractApiController
{
    /**
     * Count an article in an ongoing inventory collect (Upsert).
     *
     * @Rest\Post("/api/v1/wms/inventory/{inventory_id}/product/{product_id}/count",
     *    requirements={"inventory_id"="^\d+$", "product_id"="^\d+$"},
     *    name="post_wms_inventory_article_count"
     * )
     *
     * @Rest\RequestParam(
     *    name="location_id",
     *    requirements={"location_id"="^\d+$"},
     *    description="Location on which the article has been counted"
     * )
     *
     * @Rest\RequestParam(
     *    name="quantity",
     *    requirements={"quantity"="^\d+$"},
     *    description="Counted quantity for the gieven product"
     * )
     *
     * @Rest\RequestParam(
     *     name="use_main_collect",
     *     requirements=@Assert\Type("bool"),
     *     default=false,
     *     description="If true, the user must have an additional admin permission."
     * )
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="Count an article in an ongoing inventory collect (Upsert)",
     *     @OA\Response(
     *         response="200",
     *         description="ok - if the counted quantity has been saved successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $inventory_id,
        int $product_id,
        ParamFetcher $param_fetcher,
        InventoryReadRepository $inventory_read_repository,
        ArticleReadRepository $article_read_repository,
        InventoryCollectWriteRepository $inventory_collect_repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::INVENTORY_UPDATE]);

        try {
            if (!$inventory_read_repository->exists($inventory_id)) {
                throw new NotFoundException('Inventory not found.');
            }

            if (!$article_read_repository->exists($product_id)) {
                throw new NotFoundException('Product not found.');
            }

            $inventory_collect_repository->count(
                $product_id,
                $inventory_id,
                $param_fetcher->get('location_id'),
                $param_fetcher->get('quantity'),
                $this->getUser()->id_utilisateur,
                $param_fetcher->get('use_main_collect')
            );
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (SqlErrorMessageException $exception) {
            return new JsonResponse(
                JSendFormatter::error($exception->getMessage(), 1000),
                JsonResponse::HTTP_BAD_REQUEST
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['ok']);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_wms_inventory_article_count';
    }
}
