<?php

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class GetInventoryArticleByCodeController.
 */
class GetInventoryArticleByCodeController extends AbstractApiController
{
    /**
     * Get an article by its barcode, with additional information about its usage in current inventory.
     *
     * @Rest\Get(
     *     "/api/v1/wms/inventory/{inventory_id}/article/by-code/{barcode}",
     *      requirements={"inventory_id"="^\d+$", "barcode"=".+"},
     *      name="get_wms_inventory_article_by_code"
     * )
     *
     * @OA\Tag(name="Location")
     * @OA\Response(response=200, description="Get an article by its barcode, with additional information about its
     *                             usage in current inventory")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $inventory_id,
        string $barcode,
        ArticleReadRepository $repository,
        InventoryReadRepository $inventory_read_repository,
        QueryBuilder $query_builder
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::INVENTORY_READ]);

        try {
            if (!$inventory_read_repository->exists($inventory_id)) {
                throw new NotFoundException('Inventory not found.');
            }

            $product_id = $repository->findIdWithBarcode($barcode);

            if (0 === $product_id) {
                throw new NotFoundException(sprintf('Article not found with code "%s".', $barcode));
            }

            $article = $repository->findOneByIdOrSku($product_id);

            // Decorate default response with some meta info on the current inventory
            $where = [
                'inventory_id' => ['_eq' => $inventory_id],
                'product_id' => ['_eq' => $article['product_id']],
            ];

            $query_builder
                ->setWhere($where, InventoryReadRepository::INFO_COLUMS_MAPPING)
                ->setPage(1, 999)
                ->setOrderBy('article_collect_id ASC');

            $article['_meta'] = $inventory_read_repository
                ->findAllInfoOnArticleCollectPaginated($query_builder)
                ->getResults();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), JsonResponse::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['article' => $article]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'get_wms_inventory_article_by_code';
    }
}
