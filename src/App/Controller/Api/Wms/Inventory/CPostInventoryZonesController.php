<?php

namespace App\Controller\Api\Wms\Inventory;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Entity\InventoryZoneEntity;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryZoneReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostInventoryZonesController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * List inventory zones.
     *
     * @Rest\Post("/api/v1/wms/inventory/zones",
     *     name="cpost_wms_inventory_zones"
     * )
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     default={},
     *     description="Only returns the specified fields per row, for a lighter payload")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="name ASC",
     *     description="Sort by field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements=@Assert\Type("string"),
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="200",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="List inventory zones",
     *     @OA\Response(
     *         response="200",
     *         description="The list of inventory zones"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        InventoryZoneReadRepository $repository
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], InventoryZoneReadRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);

            $zones = ColumnHelper::intersect(
                $this->mapToEntitiesData($pager->getResults(), InventoryZoneEntity::class),
                $param_fetcher->get('fields') ?? []
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'zones' => $zones,
            '_request' => JSendFormatter::convertNumeric($params),
            '_pager' => $pager,
        ]);
    }
}
