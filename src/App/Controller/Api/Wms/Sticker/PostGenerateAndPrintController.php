<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Sticker;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Carrier\Manager\Sticker\GenerationProcessManager;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Utility\Printer\ZebraPrinter;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostGenerateAndPrintController extends AbstractApiController
{
    /**
     * Generate a shipment sticker for given delivery note id and print it on given printer id.
     *
     * @Rest\Post("/api/v1/wms/sticker/generate-and-print", name="post_wms_sticker_generate_and_print")
     *
     * @Rest\RequestParam(
     *     name="delivery_note_id",
     *     requirements=@Assert\Type("int"),
     *     description="The delivery note id you want to generate and print a sticker for"
     * )
     *
     * @Rest\RequestParam(
     *     name="expedition_parcel_id",
     *     nullable=true,
     *     requirements=@Assert\Type("int"),
     *     description="The parcel id on which the mono-parcel sticker should be generated"
     * )
     *
     * @Rest\RequestParam(
     *     name="printer_id",
     *     nullable=true,
     *     requirements=@Assert\Type("int"),
     *     description="The printer on which the generated sticker will be printed on"
     * )
     *
     * @Rest\RequestParam(
     *     name="printer_name",
     *     nullable=true,
     *     requirements=@Assert\Type("string"),
     *     description="The printer on which the generated sticker will be printed on"
     * )
     *
     * @OA\Tag(name="WMS - Sticker")
     * @OA\Response(response=200, description="Generate a shipment sticker for given delivery note id and print it on
     *                             given printer id")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        DeliveryNoteRepository $delivery_note_repository,
        GenerationProcessManager $process,
        Request $request,
        ZebraPrinter $zebra_printer
    ): JsonResponse {
        // No specific authorization needed: any user logged in (having a valid token) should be able to print a sticker

        try {
            // will throw an exception if the delivery note does not exist
            $delivery_note_repository->findOneById($param_fetcher->get('delivery_note_id'));

            if (null !== $param_fetcher->get('printer_name')) {
                $zebra_printer->loadByName($param_fetcher->get('printer_name'));
            } else {
                $zebra_printer->load($param_fetcher->get('printer_id'));
            }

            $process
                ->loadShipment($param_fetcher->get('delivery_note_id'), $param_fetcher->get('expedition_parcel_id'))
                ->generateStickerAndUpdateShipment(
                    $this->getUserAuthorizationBearer($request),
                    $this->getUser()->id_utilisateur,
                    $zebra_printer
                );
        } catch (NotFoundException $exception) {
            return JSendResponse::error(
                sprintf('Le bon de livraison %s n\'existe pas', $param_fetcher->get('delivery_note_id')),
                Response::HTTP_NOT_FOUND
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error(
                sprintf('[BL %s] %s', $param_fetcher->get('delivery_note_id'), $exception->getMessage()),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }

        return JSendResponse::success(['ok']);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_wms_sticker_generate_and_print';
    }
}
