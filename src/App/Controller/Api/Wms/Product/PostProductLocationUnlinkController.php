<?php
/*
 * This file is part of erp package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Product;

use App\Controller\Api\AbstractApiController;
use App\Exception\SqlErrorMessageException;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\MoveRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class PostProductLocationUnlinkController.
 */
class PostProductLocationUnlinkController extends AbstractApiController
{
    /**
     * Post product location unlink.
     *
     * @Rest\Post(
     *     "/api/v1/wms/product/{product_id}/location/{location_id}/unlink",
     *     requirements={"product_id"="^\d+$", "location_id"="^\d+$"},
     *     name="post_product_location_unlink"
     * )
     * @Rest\RequestParam(
     *     name="delivery_ticket_id",
     *     nullable=true,
     *     requirements={"delivery_ticket_id"="^\d+$"},
     *     description="Delivery ticket id"
     * )
     *
     * @OA\Tag(name="WMS/Product")
     * @OA\Response(response=200, description="Unlink quantities of a product in a location")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $product_id,
        int $location_id,
        ParamFetcher $param_fetcher,
        MoveRepository $move_repository
    ): JsonResponse {
        $params = $param_fetcher->all();
        $delivery_ticket_id = empty($params['delivery_ticket_id']) ? null : (int) $params['delivery_ticket_id'];
        $user_id = $this->getUser()->get('id_utilisateur');

        if ($delivery_ticket_id) {
            $this->checkAuthorization([UserPermission::PRODUCT_LOCATION_UNLINK_DELIVERY]);

            try {
                $move_repository->unlinkDelivery($product_id, $location_id, $delivery_ticket_id, $user_id);
            } catch (SqlErrorMessageException $exception) {
                return new JsonResponse(
                    JSendFormatter::error($exception->getMessage(), 1000),
                    JsonResponse::HTTP_BAD_REQUEST
                );
            } catch (Exception $exception) {
                return JSendResponse::error($exception->getMessage());
            }

            return JSendResponse::success([]);
        }

        return JSendResponse::error('Missing parameter in API call.', JsonResponse::HTTP_BAD_REQUEST);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_product_location_unlink';
    }
}
