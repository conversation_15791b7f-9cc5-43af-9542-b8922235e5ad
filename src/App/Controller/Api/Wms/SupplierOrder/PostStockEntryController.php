<?php

namespace App\Controller\Api\Wms\SupplierOrder;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\SupplierOrderProduct\Manager\SupplierOrderProductManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostStockEntryController extends AbstractApiController
{
    /**
     * Make an entry stock for a specific product.
     *
     * @Rest\Post("/api/v1/wms/stock-entry")
     *
     * @Rest\RequestParam(
     *     name="supplier_order_id",
     *     requirements=@Assert\Type("int"),
     *     nullable=true
     * )
     * @Rest\RequestParam(
     *     name="transfer_id",
     *     requirements=@Assert\Type("int"),
     *     nullable=true,
     *     default=null
     * )
     * @Rest\RequestParam(
     *     name="product_id",
     *     requirements=@Assert\Type("int")
     * )
     * @Rest\RequestParam(
     *     name="quantity",
     *     requirements=@Assert\Type("int")
     * )
     * @Rest\RequestParam(
     *     name="warehouse_id",
     *     requirements=@Assert\Type("int")
     * )
     *
     * @OA\Tag(name="WMS - Stock entry")
     * @OA\Response(response=200, description="Generate an entry stock for the given product/order/transfer")
     * @Security(name="Bearer")
     *
     * @deprecated
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        SupplierOrderProductManager $supplier_order_product_manager
    ): JsonResponse {
        try {
            $supplier_order_product_manager->makeStockEntry(
                $this->getUser()->id_utilisateur,
                $param_fetcher->get('supplier_order_id'),
                $param_fetcher->get('transfer_id'),
                $param_fetcher->get('product_id'),
                $param_fetcher->get('quantity'),
                $param_fetcher->get('warehouse_id')
            );
        } catch (\LogicException $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error(
                'Une erreur est survenue lors de l\'entrée stock.',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);
        }

        return JSendResponse::noContent();
    }
}
