<?php

namespace App\Controller\Api\SupplierContract;

use App\Controller\Api\AbstractApiController;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Params\FiltersParams;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\SupplierContract\Manager\SupplierContractManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostSupplierContract extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/supplier-contracts", name="cpost_supplier_contracts")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, for a lighter payload"
     * )
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters"
     * )
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="supplier_id",
     *     description="Sort by field."
     * )
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction."
     * )
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset"
     * )
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="20",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page"
     * )
     *
     * @Operation(
     *     tags={"Supplier contract"},
     *     summary="Retrieve a list of supplier contracts",
     *     description="Supplier contracts can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a list of supplier contracts"
     *     )
     * )
     *
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        SupplierContractManager $manager
    ): JsonResponse {
        try {
            $pager = $manager->getFilteredCollection(FiltersParams::fromParamFetcher($param_fetcher));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'supplier_contracts' => ColumnHelper::intersect($pager->getResults(), $param_fetcher->get('fields') ?? []),
            '_request' => $param_fetcher->all(),
            '_pager' => $pager,
        ]);
    }
}
