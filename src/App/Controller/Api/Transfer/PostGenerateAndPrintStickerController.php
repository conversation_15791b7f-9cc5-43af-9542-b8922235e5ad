<?php

namespace App\Controller\Api\Transfer;

use App\Contract\CarrierV2ClientInterface;
use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostGenerateAndPrintStickerController extends AbstractApiController
{
    /**
     * Generate a shipment sticker for given delivery note id and print it on given printer id.
     *
     * @Rest\Post("/api/v1/transfer/generate-and-print-sticker", name="post_transfer_sticker_generate_and_print")
     *
     * @Rest\RequestParam(
     *     name="delivery_note_id",
     *     requirements=@Assert\Type("int"),
     *     description="The delivery note id you wnat to genrate and print a sticker for"
     * )
     *
     * @Rest\RequestParam(
     *     name="printer_id",
     *     nullable=true,
     *     requirements=@Assert\Type("int"),
     *     description="The printer on which the generated sticker will be printed on"
     * )
     *
     * @Rest\RequestParam(
     *     name="nb_stickers",
     *     nullable=true,
     *     requirements=@Assert\Type("int"),
     *     description="Number of stickers to print"
     * )
     *
     * @OA\Tag(name="WMS - Transfers - Sticker")
     * @OA\Response(response=200, description="Generate a shipment sticker for given delivery note id and print it on
     *                             given printer id")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, CarrierV2ClientInterface $carrier_http_client): JsonResponse
    {
        try {
            $carrier_http_client->generateStickerAndPrint(
                $param_fetcher->get('delivery_note_id'),
                $param_fetcher->get('printer_id'),
                $this->getUser()->id_utilisateur,
                $param_fetcher->get('nb_stickers')
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error(
                sprintf('[BL %s] %s', $param_fetcher->get('delivery_note_id'), $exception->getMessage()),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }

        return JSendResponse::success(['ok']);
    }
}
