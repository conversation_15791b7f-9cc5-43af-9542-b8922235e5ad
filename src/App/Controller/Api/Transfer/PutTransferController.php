<?php

namespace App\Controller\Api\Transfer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use App\Params\FiltersParams;
use App\Validator\Constraint as CustomAssert;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Transfer\Manager\TransferManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class PutTransferController extends AbstractApiController
{
    /**
     * Update a transfer.
     *
     * @Rest\Put(
     *      "/api/v1/transfer/{transfer_id}",
     *      requirements={"transfer_id"="^\d+$"},
     *      name="put_transfer"
     * )
     *
     * @Rest\RequestParam(
     *     name="products",
     *     requirements=@CustomAssert\AddProductsQuantities,
     *     description="Products to set in the transfer"
     * )
     *
     * @OA\Tag(name="Transfer")
     * @OA\Response(response=200, description="Update a transfer with some products")
     * @Security(name="Bearer")
     */
    public function __invoke(int $transfer_id, ParamFetcher $param_fetcher, TransferManager $manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::TRANSFER_CREATE]);

        $params = $param_fetcher->all();

        try {
            $manager->addProducts($transfer_id, $params['products']);
            $response = JSendResponse::success([
                'transfer' => $manager->getOneBy(
                    FiltersParams::create()->setFilters(['transfer_id' => ['_eq' => $transfer_id]])
                ),
            ]);
        } catch (\Exception $exception) {
            $response = JSendResponse::error(
                sprintf('Transfer update has failed: %s', $exception->getMessage()),
                Response::HTTP_INTERNAL_SERVER_ERROR,
                ['params' => $params]
            );
        }

        return $response;
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_transfer';
    }
}
