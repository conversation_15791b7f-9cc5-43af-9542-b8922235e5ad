<?php

namespace App\Controller\Api\Transfer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Transfer\Manager\TransferCollectionManager;
use Symfony\Component\HttpFoundation\JsonResponse;

class CGetStalledTransfersController extends AbstractApiController
{
    /**
     * Retrieve a filtered list of transfer collections.
     *
     * @Rest\Get("/api/v1/stalled-transfers", name="cget_stalled_transfers")
     *
     * @OA\Tag(name="Transfer")
     * @OA\Response(response=200, description="Retrieve a list of Transfer collections")
     * @Security(name="Bearer")
     */
    public function __invoke(TransferCollectionManager $manager): JsonResponse
    {
        try {
            $transfers = $manager->getStalledTransfers();

            $response = JSendResponse::success([
                'stalled_transfers' => $transfers,
            ]);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            $response = JSendResponse::error($exception->getMessage());
        }

        return $response;
    }
}
