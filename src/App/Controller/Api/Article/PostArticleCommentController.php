<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleCommentManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostArticleCommentController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/article/{article_id}/comment",
     * requirements={"article_id"="^\d+$"},
     * name="post_article_comment")
     *
     * @Rest\RequestParam(
     *      name="data",
     *      default={},
     *      requirements=@Assert\Type("array")
     * )
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Add an comment to an article",
     *     description="Add an comment to an article",
     *     @OA\Response(
     *         response="200",
     *         description="The inserted comment id"
     *     )
     * )
     */
    public function __invoke(
        int $article_id,
        ParamFetcher $param_fetcher,
        ArticleCommentManager $article_comment_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_COMMENT_WRITE]);
        /** @var array $data */
        $data = $param_fetcher->get('data');

        try {
            $article_comment_id = $article_comment_manager->create($article_id, $data, $this->getUser());

            return JSendResponse::success(['article_comment_id' => $article_comment_id]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST, $exception->getContext());
        } catch (\Throwable $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
