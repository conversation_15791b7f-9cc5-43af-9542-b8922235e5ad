<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleStockManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class PutArticleSafetyStockThresholdController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/article/{article_id}/warehouse/{warehouse_id}",
     *      requirements={"aticle_id"="^\d+$", "warehouse_id"="^\d+$"},
     *      name="put_article_safety_stock_threshold"
     * )
     *
     * @Rest\RequestParam(
     *     name="safety_stock_threshold",
     *     requirements={"safety_stock_threshold"="^[0-9]{0,8}$"},
     *     description="New safety stock threshold"
     * )
     * @OA\Tag(name="Article")
     * @OA\Response(response=204, description="Update the article safety stock")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $article_id,
        int $warehouse_id,
        ParamFetcher $param_fetcher,
        ArticleStockManager $articleManager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE]);

        $safety_stock_threshold = $param_fetcher->get('safety_stock_threshold');

        try {
            $nb_affected = $articleManager->updateSafetyStockThreshold(
                $safety_stock_threshold,
                $article_id,
                $warehouse_id,
                $this->getUser()
            );

            return JSendResponse::success(
                [],
                $nb_affected > 0 ? Response::HTTP_NO_CONTENT : Response::HTTP_ALREADY_REPORTED
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'put_article_safety_stock_threshold';
    }
}
