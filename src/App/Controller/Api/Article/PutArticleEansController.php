<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleEanManager;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PutArticleEansController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/article/{article_id}/eans",
     *      requirements={"aticle_id"="^\d+$"},
     *      name="put_article_ean"
     * )
     *
     * @Rest\RequestParam(
     *     name="eans",
     *     requirements=@Assert\Type("array"),
     *     description="Eans"
     * )
     *
     * @OA\Tag(name="Article")
     * @OA\Response(response=200, description="Update article eans")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $article_id,
        ParamFetcher $param_fetcher,
        ArticleEanManager $article_ean_manager,
        ValidatorInterface $validator
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_LOGISTIC_WRITE]);
        /** @var array $eans */
        $eans = $param_fetcher->get('eans');

        try {
            $dto = $article_ean_manager->createDto($article_id, $eans);
            $errors = $validator->validate($dto);
            if (count($errors) > 0) {
                return JSendResponse::error('Invalid parameters', Response::HTTP_BAD_REQUEST, [
                    'validation_errors' => ConstraintMessageFormatter::extract($errors),
                ]);
            }

            $updated = $article_ean_manager->update($dto, $this->getUser());

            return JSendResponse::success(['updated' => $updated]);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'put_article_ean';
    }
}
