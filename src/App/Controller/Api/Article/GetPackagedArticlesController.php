<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Mysql\Repository\PackagedArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use Symfony\Component\Console\Exception\InvalidArgumentException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetPackagedArticlesController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/package/{package_id}/packaged-articles",
     * requirements={"package_id"="^\d+$"},
     * name="get_packaged_articles")
     *
     * @OA\Tag(name="Article")
     * @OA\Response(response=200, description="Fetch packaged articles of an article of type 'compose'")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $package_id,
        SingleArticleReadRepository $single_article_read_repository,
        PackagedArticleRepository $packaged_article_repository
    ): JsonResponse {
        try {
            if (!$single_article_read_repository->isPackage($package_id)) {
                throw new InvalidArgumentException('Not a package.');
            }

            $packaged_articles = $packaged_article_repository->findAllByPackageId($package_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($packaged_articles);
    }
}
