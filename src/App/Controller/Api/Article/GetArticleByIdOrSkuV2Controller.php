<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleV2RepositoryMarginEnricher;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetArticleByIdOrSkuV2Controller extends AbstractApiController
{
    /**
     * Get information on an article using its sku or its id.
     *
     * @Rest\Get("/api/v2/article/{id_or_sku}",
     * requirements={"id_or_sku"="^[A-Z0-9]{1}[\-_A-Z0-9]{0,39}$"},
     * name="get_article_by_id_or_sku_v2")
     * @OA\Tag(name="Article")
     * @OA\Response(response=200, description="Get information on an article using its sku or its id")
     * @Security(name="Bearer")
     */
    public function __invoke(
        string $id_or_sku,
        SingleArticleReadRepository $repository,
        ArticleV2RepositoryMarginEnricher $margin_enricher
    ): JsonResponse {
        try {
            $article = $repository->getOneByIdOrSku($id_or_sku);
            $enriched_article = $margin_enricher->enrichSalesChannelMargins($article);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($enriched_article);
    }
}
