<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticlePromoBudgetManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteArticlePromoBudgetController extends AbstractApiController
{
    /**
     * @Rest\Delete("/api/v1/article/{article_id}/promo-budget/{promo_budget_id}",
     * requirements={"article_id"="^\d+$", "promo_budget_id"="^\d+$"},
     * name="delete_article_promo_budget")
     *
     * @Operation(
     *     tags={"Promo budget"},
     *     summary="Remove a promo budget",
     *     @OA\Response(
     *         response="204",
     *         description="OK - if the promo budget has been deleted successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $article_id,
        int $promo_budget_id,
        ArticlePromoBudgetManager $article_promo_budget_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            $article_promo_budget_manager->delete($article_id, $promo_budget_id);

            return JSendResponse::noContent();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }
}
