<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Mysql\Repository\ArticleStockRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetArticleStockController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/article-stock/{id_or_sku}",
     * requirements={"id_or_sku"="^[A-Z0-9]{1}[\-_A-Z0-9]{0,39}$"},
     * name="get_article_stock")
     *
     * @OA\Tag(name="Article")
     * @OA\Response(response=200, description="Fetch an article stock by its sku")
     * @Security(name="Bearer")
     */
    public function __invoke(string $id_or_sku, ArticleStockRepository $repository): JsonResponse
    {
        try {
            $stock = $repository->findAll($id_or_sku);

            if ([] === $stock) {
                throw new NotFoundException(sprintf('Article not found with sku or id "%s".', $id_or_sku));
            }
        } catch (NotFoundException $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'stock' => $stock,
            'current_user_warehouse_id' => $this->getUser()->warehouse_id,
        ]);
    }
}
