<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Document\Manager\Pdf\Code128ZebraStickerPDF;
use SonVideo\Erp\Document\Manager\Pdf\DestockZebraStickerPDF;
use SonVideo\Erp\Referential\Printer;
use SonVideo\Erp\Utility\Printer\ZebraPrinter;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostStickerPrintController extends AbstractApiController
{
    /**
     * Generate a code 128 or destock sticker and print it on given printer id.
     *
     * @Rest\Post("/api/v1/article/{id_or_sku}/sticker/{type}/print", requirements={
     *      "id_or_sku"="^[A-Z0-9]{1}[\-_A-Z0-9]{0,39}$",
     *      "type"="(code-128|destock)"
     *  }, name="post_sticker_print")
     *
     * @Rest\RequestParam(
     *     name="printer_id",
     *     nullable=true,
     *     requirements=@Assert\Type("int"),
     *     description="The printer on which the generated sticker will be printed on"
     * )
     *
     * @Rest\RequestParam(
     *     name="printer_name",
     *     nullable=true,
     *     requirements=@Assert\Type("string"),
     *     description="The printer on which the generated sticker will be printed on"
     * )
     *
     * @Rest\RequestParam(
     *     name="quantity",
     *     requirements=@Assert\Type("int"),
     *     description="Quantity of sticker to print"
     * )
     *
     * @OA\Tag(name="Sticker")
     * @OA\Response(response=204, description="No content")
     * @Security(name="Bearer")
     */
    public function __invoke(
        string $id_or_sku,
        string $type,
        ParamFetcher $param_fetcher,
        ZebraPrinter $zebra_printer,
        DestockZebraStickerPDF $destock_zebra_sticker_PDF,
        Code128ZebraStickerPDF $code128_zebra_sticker_PDF
    ): JsonResponse {
        try {
            $printer_id = $param_fetcher->get('printer_id');
            $printer_name = $param_fetcher->get('printer_name');
            $quantity = $param_fetcher->get('quantity');

            if (null === $printer_id && null === $printer_name) {
                throw new NotFoundException('Either printer_id or printer_name should be provided.');
            }

            if ('destock' === $type) {
                $sticker = $destock_zebra_sticker_PDF->generate(['id_or_sku' => $id_or_sku]);
                $size = Printer::TYPE_102x152;
            } else {
                $sticker = $code128_zebra_sticker_PDF->generate(['id_or_sku' => $id_or_sku]);
                $size = Printer::TYPE_32x25;
            }

            if (null !== $printer_name) {
                $zebra_printer->loadByName($param_fetcher->get('printer_name'), $size);
            } else {
                $zebra_printer->load($param_fetcher->get('printer_id'), $size);
            }

            for ($i = 0; $i < $quantity; ++$i) {
                $zebra_printer->print('destock', $sticker);
            }
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return JSendResponse::success([], Response::HTTP_NO_CONTENT);
    }
}
