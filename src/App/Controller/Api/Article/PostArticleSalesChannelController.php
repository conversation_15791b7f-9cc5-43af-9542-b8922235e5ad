<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostArticleSalesChannelController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/article/{article_id}/sales-channel",
     *  requirements={"article_id"="^\d+$"},
     * name="post_article_sales_channel")
     *
     * @Rest\RequestParam(
     *     name="sales_channel_id",
     *     nullable=false,
     *     requirements=@Assert\Type("integer"),
     *     description="sales channel to add to the article")
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Add an sales channel to an article",
     *     description="Add an comment to an article",
     *     @OA\Response(
     *         response="204",
     * *         description="ok - if the article sales channel has been intert successfull"
     *     )
     * )
     */
    public function __invoke(
        int $article_id,
        ParamFetcher $param_fetcher,
        ArticleSalesChannelManager $article_sales_channel_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_BUYERS_WRITE]);
        /** @var int $sales_channel_id */
        $sales_channel_id = $param_fetcher->get('sales_channel_id');

        try {
            $article_sales_channel_manager->createOrEnable($article_id, $sales_channel_id);

            return JSendResponse::noContent();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Throwable $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
