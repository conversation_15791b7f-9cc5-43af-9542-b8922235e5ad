<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleCommentManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PutArticleCommentController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/article/{article_id}/comment/{article_comment_id}",
     *      requirements={"aticle_id"="^\d+$", "article_comment_id"="^\d+$"},
     *      name="put_article_comment"
     * )
     *
     * @Rest\RequestParam(
     *     name="data",
     *     default={},
     *     requirements=@Assert\Type("array")
     * )
     *
     * @OA\Tag(name="Article")
     * @OA\Response(response=204, description="Update the article comment")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $article_id,
        int $article_comment_id,
        ParamFetcher $param_fetcher,
        ArticleCommentManager $article_comment_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_COMMENT_WRITE]);

        try {
            $nb_affected = $article_comment_manager->update(
                $article_id,
                $article_comment_id,
                (array) $param_fetcher->get('data'),
                $this->getUser()
            );

            return JSendResponse::success(['updated' => $nb_affected]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }
}
