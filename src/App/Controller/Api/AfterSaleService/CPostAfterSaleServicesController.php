<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\AfterSaleService;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\OptionalColumnDefinition;
use App\Entity\OptionalColumnsLoader;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\AfterSaleService\Entity\AfterSaleServiceEntity;
use SonVideo\Erp\AfterSaleService\Mysql\Repository\AfterSaleServiceRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostAfterSaleServicesController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Post("/api/v1/after-sale-services", name="cpost_after_sale_services")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="created_at",
     *     description="Sort by field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements=@Assert\Type("string"),
     *     default="DESC",
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="included_dependencies",
     *     default={},
     *     description="Array of dependencies to hydrate in the result")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="9999",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"After-Sales"},
     *     summary="Retrieve a list of after sale services",
     *     description="After sales can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a list of after sale services"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        AfterSaleServiceRepository $repository,
        OptionalColumnsLoader $optional_columns_loader
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($params['where'], AfterSaleServiceRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);
            $results = $optional_columns_loader
                ->addColumn(
                    (new OptionalColumnDefinition(
                        'article',
                        fn (array $article_ids): array => $repository->fetchArticle($article_ids)
                    ))
                        ->asObject()
                        ->withInternalPivot('article_id')
                )
                ->load($pager->getResults(), 'after_sale_service_id', $params['included_dependencies']);

            $after_sale_services = $this->mapToEntitiesData($results, AfterSaleServiceEntity::class);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'after_sale_services' => $after_sale_services,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_after_sale_services';
    }
}
