<?php

namespace App\Controller\Api\Competitor;

use App\Controller\Api\AbstractApiController;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Competitor\Mysql\Repository\CompetitorRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostCompetitorsController extends AbstractApiController
{
    /**
     * Retrieve a filtered list of competitors.
     *
     * @Rest\Post("/api/v1/competitors", name="cpost_competitors")
     *
     * @Rest\RequestParam(
     *      name="fields",
     *      nullable=true,
     *      description="Only returns the specified fields per row, for returning a lighter response")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="id ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     * @Rest\RequestParam(
     *     name="limit",
     *     default="12",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"Competitors"},
     *     summary="Retrieve a filtered list of competitors",
     *     description="The competitors can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of competitors"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        CompetitorRepository $competitor_repository
    ): JsonResponse {
        try {
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [])
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'))
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'));

            $pager = $competitor_repository->findAllPaginated($query_builder);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'competitors' => ColumnHelper::intersect($pager->getResults(), $param_fetcher->get('fields') ?? []),
            '_request' => $param_fetcher->all(),
            '_pager' => $pager,
        ]);
    }
}
