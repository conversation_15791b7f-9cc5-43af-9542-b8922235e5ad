<?php

namespace App\Controller\Api\SupplierOrderProduct;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SupplierOrder\Exception\SupplierOrderNotFoundException;
use SonVideo\Erp\SupplierOrderProduct\Dto\SupplierOrderProductCreationContextDto;
use SonVideo\Erp\SupplierOrderProduct\Exception\SupplierOrderProductRequestPayloadException;
use SonVideo\Erp\SupplierOrderProduct\Manager\SupplierOrderProductCreator;
use SonVideo\Erp\SupplierOrderProduct\Mysql\Repository\SupplierOrderProductRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class PutSupplierOrderProductController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Put("/api/v1/supplier-order/{supplier_order_id}/product",
     *     requirements={"supplier-order-id"="^\d+$"},
     *     name="put_supplier_order_product")
     *
     * @OA\RequestBody(@Nelmio\Model(type=SupplierOrderProductCreationContextDto::class))
     * @OA\Tag(name="Supplier Order Product")
     * @OA\Response(
     *     response=200,
     *     description="Update supplier order product",
     * )
     * @OA\Response(
     *     response=201,
     *     description="Create supplier order product",
     * )
     * @OA\Response(
     *     response=400,
     *     description="Something is wrong with the payload",
     * )
     * @OA\Response(
     *     response=401,
     *     description="Unauthorized",
     * )
     * @OA\Response(
     *     response=500,
     *     description="An error occurred while creating the supplier order",
     * )
     *
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $supplier_order_id,
        Request $request,
        ParamFetcher $param_fetcher,
        SupplierOrderProductCreator $supplier_order_product_creator,
        SerializerInterface $serializer,
        SupplierOrderProductRepository $supplier_order_product_repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_BUYERS_WRITE]);

        /** @var SupplierOrderProductCreationContextDto $product */
        $product = $serializer->deserialize(
            $request->getContent(),
            SupplierOrderProductCreationContextDto::class,
            'json'
        );

        try {
            $code = 200;
            if (!$supplier_order_product_repository->exists($supplier_order_id, $product->product_id)) {
                $code = 201;
            }
            $supplier_order_product = $supplier_order_product_repository->findOneById(
                $supplier_order_product_creator->create($supplier_order_id, $product)
            );

            return JSendResponse::success(['supplier_order_product' => $supplier_order_product], $code);
        } catch (SupplierOrderProductRequestPayloadException $exception) {
            $this->logger->error('[put_supplier_order_product] Something may be wrong with the payload', [
                'exception' => $exception,
            ]);

            return JSendResponse::fail($exception->getMessage());
        } catch (SupplierOrderNotFoundException $exception) {
            $this->logger->error("[put_supplier_order_product] Supplier order $supplier_order_id not found", [
                'exception' => $exception,
            ]);

            return JSendResponse::fail($exception->getMessage(), 404);
        } catch (Exception $exception) {
            $this->logger->error(
                '[put_supplier_order_product] An error occurred while adding product to the supplier order',
                [
                    'exception' => $exception,
                ]
            );

            return JSendResponse::error('An error occurred while adding product to the supplier order');
        }
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_supplier_order_product';
    }
}
