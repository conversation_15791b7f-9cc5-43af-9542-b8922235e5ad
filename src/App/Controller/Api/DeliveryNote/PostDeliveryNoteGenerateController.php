<?php
/*
 * This file is part of erp package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Manager\DeliveryNoteGenerator;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class PostDeliveryNoteGenerateController.
 */
class PostDeliveryNoteGenerateController extends AbstractApiController
{
    /**
     * Post move products.
     *
     * @Rest\Post("/api/v1/delivery-note/generate",
     * name="post_delivery_note_generate")
     *
     * @Rest\RequestParam(
     *     name="customer_order_id",
     *     requirements=@Assert\Type("integer"),
     *     description="Customer order id"
     * )
     * @Rest\RequestParam(
     *     name="carrier_id",
     *     requirements=@Assert\Type("integer"),
     *     description="Carrier id"
     * )
     * @Rest\RequestParam(
     *     name="shipment_method_id",
     *     requirements=@Assert\Type("integer"),
     *     description="Shipment method id"
     * )
     * @OA\Tag(name="Delivery note")
     * @OA\Response(response=200, description="Generate delivery note for customer order.")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, DeliveryNoteGenerator $delivery_note_generator): JsonResponse
    {
        try {
            $result_summary = $delivery_note_generator->generate(
                $param_fetcher->get('customer_order_id'),
                $param_fetcher->get('carrier_id'),
                $this->getUser()->get('utilisateur'),
                $param_fetcher->get('shipment_method_id')
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($result_summary);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_delivery_note_generate';
    }
}
