<?php

namespace App\Controller\Api\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Manager\DeliveryNoteManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PutDeliveryNoteController extends AbstractApiController
{
    /**
     * Update delivery note shipment data.
     *
     * @Rest\Put("/api/v1/delivery-note/{delivery_note_id}",
     *     requirements={"delivery_note_id"="^\d+$"},
     *     name="put_delivery_note"
     * )
     *
     * @Rest\RequestParam(
     *     name="address",
     *     nullable=true,
     *     requirements=@Assert\Type("array")
     * )
     * @Rest\RequestParam(
     *     name="cellphone",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="city",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="civility",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="company",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="country_id",
     *     nullable=true,
     *     requirements=@Assert\Type("integer")
     * )
     * @Rest\RequestParam(
     *     name="email",
     *     nullable=true,
     *     requirements=@Assert\Email
     * )
     * @Rest\RequestParam(
     *     name="firstname",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="lastname",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="phone",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="postal_code",
     *     nullable=true,
     *     requirements=@Assert\Type("string")
     * )
     * @Rest\RequestParam(
     *     name="shipment_method_id",
     *     nullable=true,
     *     requirements=@Assert\Type("integer")
     * )
     *
     * @OA\Tag(name="Delivery note")
     * @OA\Response(response=200, description="Update shipment on delivery note")
     * @Security(name="Bearer")
     */
    public function __invoke(int $delivery_note_id, ParamFetcher $params, DeliveryNoteManager $manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::DELIVERY_NOTE_WRITE]);

        try {
            $manager->update($delivery_note_id, array_filter($params->all()));
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([]);
    }

    public function getRouteName(): string
    {
        return 'put_delivery_note';
    }
}
