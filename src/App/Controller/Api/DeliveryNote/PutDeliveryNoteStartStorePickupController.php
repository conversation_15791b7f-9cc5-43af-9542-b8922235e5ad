<?php
/*
 * This file is part of erp package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Referential\InternalError;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class PutDeliveryNoteStartStorePickupController.
 */
class PutDeliveryNoteStartStorePickupController extends AbstractApiController
{
    /**
     * Mark a delivery note as "store pickup started".
     *
     * @Rest\Put(
     *     "/api/v1/delivery-note/{delivery_note_id}/start-store-pickup",
     *     requirements={"delivery_note_id"="^\d+$"},
     *     name="put_delivery_note_start_store_pickup"
     * )
     * @OA\Tag(name="Delivery Note")
     * @OA\Response(response=200, description="Start the store pickup of a delivery note")
     * @Security(name="Bearer")
     */
    public function __invoke(int $delivery_note_id, DeliveryNoteRepository $repository): JsonResponse
    {
        try {
            // Retrieve delivery note
            $delivery_note = $repository->findOneById($delivery_note_id);

            // verify the delivery note is of store pickup type
            if (!$delivery_note->is_pickup) {
                throw new InternalErrorException(InternalError::DELIVERY_NOTE_NOT_A_PICKUP, new \LogicException('Delivery note is not a pickup'), ['delivery_note_id' => $delivery_note_id]);
            }

            // Start store pickup if not already started
            if (!$delivery_note->store_pickup_started_at instanceof \DateTimeInterface) {
                $repository->startStorePickup($delivery_note_id);

                // ...and retrieve updated dn
                $delivery_note = $repository->findOneById($delivery_note_id);
            }
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::internalError($exception);
        } catch (Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'store_pickup_started_at' => $delivery_note->store_pickup_started_at,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_delivery_note_start_store_pickup';
    }
}
