<?php
/*
 * This file is part of erp package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Filesystem\Manager\DefaultFileSystem;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class PostDeliveryProofController.
 */
class PostDeliveryProofController extends AbstractApiController
{
    /**
     * Post delivery note proof.
     *
     * @Rest\Post("/api/v1/delivery-note/{delivery_note_id}/proof",
     *     requirements={"delivery_note_id"="^\d+$"},
     *     name="post_delivery_note_proof"
     * )
     *
     * @Rest\FileParam(
     *     name="picture",
     *     nullable=false,
     *     description="Picture of the proof"
     * )
     * @Rest\RequestParam(
     *     name="warehouse_id",
     *     nullable=false,
     *     requirements="^\d+$",
     *     description="Warehouse on which the pickup is done"
     * )
     * @Rest\RequestParam(
     *     name="created_at",
     *     nullable=false,
     *     requirements="^\d+$",
     *     description="UTC timestamp of the capture"
     * )
     *
     * @OA\Tag(name="Delivery note")
     * @OA\Response(response=200, description="Post the pickup delivery proof")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $delivery_note_id,
        ParamFetcher $param_fetcher,
        DefaultFileSystem $file_system
    ): JsonResponse {
        try {
            $picture = $param_fetcher->get('picture');
            $warehouse_id = $param_fetcher->get('warehouse_id');
            $created_at = $param_fetcher->get('created_at');

            $filename = sprintf('PreuveRetrait%s-%s-%s.jpeg', $warehouse_id, $delivery_note_id, $created_at);

            $file_system->createOrOverwriteStream(
                DefaultFileSystem::DELIVERY_NOTE_PROOF_DIRECTORY,
                $filename,
                $picture->getRealPath()
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['filename' => $filename]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_delivery_note_proof';
    }
}
