<?php
/*
 * This file is part of erp package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Category;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Formatter\Http\JSendResponse;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SubCategory\Dto\UpdateContext\SubcategoryContextDto;
use SonVideo\Erp\SubCategory\Manager\SubcategoryUpdater;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PutSubcategoryController extends AbstractApiController implements DataLoaderAwareInterface
{
    use DataLoaderAwareTrait;

    /**
     * @Rest\Put(
     *     "/api/v1/subcategory/{subcategory_id}",
     *     requirements={"subcategory_id"="^\d+$"},
     *     name="put_subcategory"
     * )
     *
     * @Rest\RequestParam(
     *      name="data",
     *      default={},
     *      requirements=@Assert\Type("array")
     *  )
     *
     * @OA\Tag(name="Taxonomy")
     * @OA\Response(response=200, description="Update subcategory")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $subcategory_id,
        ParamFetcher $param_fetcher,
        ValidatorInterface $validator,
        SubcategoryUpdater $subcategory_updater
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::SUBCATEGORY_UPDATE]);

        /** @var array $data */
        $data = $param_fetcher->get('data');

        if ($data['subcategory_id'] !== $subcategory_id) {
            return JSendResponse::error('subcategory_id mismatch', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        /** @var SubcategoryContextDto $dto */
        $dto = $this->data_loader->hydrate($data, SubcategoryContextDto::class);

        try {
            $nb_affected_rows = $subcategory_updater->update($dto);

            if (0 === $nb_affected_rows) {
                // occurs if the subcategory do not exist or if there is nothing to changes
                return JSendResponse::error('No changes occurred', Response::HTTP_NOT_FOUND);
            }
        } catch (InternalErrorException $exception) {
            return JSendResponse::internalError($exception);
        } catch (Exception $exception) {
            $this->logger->warning($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::noContent();
    }
}
