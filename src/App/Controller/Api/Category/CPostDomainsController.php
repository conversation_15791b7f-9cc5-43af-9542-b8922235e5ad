<?php

namespace App\Controller\Api\Category;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Domain\Entity\DomainEntity;
use SonVideo\Erp\Domain\Mysql\Repository\DomainRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostDomainsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Post("/api/v1/domains", name="cpost_domains")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="name ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"Taxonomy"},
     *     summary="Retrieve a filtered list of domains",
     *     description="The domains can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of domains"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        DomainRepository $repository
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], DomainRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);
            $domains = $this->mapToEntitiesData($pager->getResults(), DomainEntity::class);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'domains' => $domains,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }
}
