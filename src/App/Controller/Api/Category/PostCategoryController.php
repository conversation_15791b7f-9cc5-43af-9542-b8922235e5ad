<?php

namespace App\Controller\Api\Category;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Category\Exception\CategoryNameAlreadyExistException;
use SonVideo\Erp\Category\Manager\CategoryManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostCategoryController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/category",
     * name="post_category")
     *
     * @Rest\RequestParam(
     *      name="category_name",
     *      requirements=@Assert\Type("string")
     * )
     *
     * @Rest\RequestParam(
     *       name="domain_id",
     *       requirements=@Assert\Type("int")
     *  )
     *
     * @Operation(
     *     tags={"Category"},
     *     summary="Add a category",
     *     description="Add a category",
     *     @OA\Response(
     *         response="200",
     *         description="The inserted category id"
     *     )
     * )
     */
    public function __invoke(ParamFetcher $param_fetcher, CategoryManager $category_manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::SUBCATEGORY_UPDATE]);
        /** @var string $category_name */
        $category_name = $param_fetcher->get('category_name');
        /** @var int $domain_id */
        $domain_id = $param_fetcher->get('domain_id');

        try {
            $category_id = $category_manager->create($domain_id, $category_name);

            return JSendResponse::success(['category_id' => $category_id]);
        } catch (CategoryNameAlreadyExistException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_CONFLICT);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error(
                $exception->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR,
                $exception->getContext()
            );
        } catch (\Throwable $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
