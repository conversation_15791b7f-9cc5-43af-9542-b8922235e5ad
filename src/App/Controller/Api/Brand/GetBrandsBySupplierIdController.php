<?php

namespace App\Controller\Api\Brand;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Brand\Mysql\Repository\BrandRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

final class GetBrandsBySupplierIdController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/supplier/{supplier_id}/brands",
     * requirements={"supplier_id"="^\d+$"},
     * name="get_brands_by_supplier_id"
     * )
     *
     * @OA\Tag(name="Brands")
     * @OA\Response(response=200, description="Get list of brands by supplier")
     * @Security(name="Bearer")
     */
    public function __invoke(int $supplier_id, BrandRepository $repository): JsonResponse
    {
        try {
            $brands = $repository->findAllBySupplierId($supplier_id);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['brands' => $brands]);
    }
}
