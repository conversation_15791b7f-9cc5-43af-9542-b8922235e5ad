<?php

namespace App\Controller\Api\Brand;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Supplier\Mysql\Repository\SupplierRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

final class GetSuppliersByBrandIdController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/brand/{brand_id}/suppliers",
     * requirements={"brand_id"="^\d+$"},
     * name="get_suppliers_by_brand_id"
     * )
     *
     * @OA\Tag(name="Brands")
     * @OA\Response(response=200, description="Get list of suppliers by brand")
     * @Security(name="Bearer")
     */
    public function __invoke(int $brand_id, SupplierRepository $repository): JsonResponse
    {
        try {
            $suppliers = $repository->findAllByBrandId($brand_id);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['suppliers' => $suppliers]);
    }

    protected function getRouteName(): string
    {
        return 'get_suppliers_by_brand_id';
    }
}
