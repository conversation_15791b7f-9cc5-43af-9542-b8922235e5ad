<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Rpc;

use App\Exception\InternalServerErrorException;
use SonVideo\Erp\Quote\Manager\QuoteForRpcRetriever;
use SonVideo\RpcBundle\Controller\ControllerTrait;

class QuoteController
{
    use ControllerTrait;

    /**
     * getClassMethods.
     *
     * Return list of rpc method(s) implemented in this class
     */
    public static function getClassMethods(): array
    {
        $domain = 'quote:';

        return [
            $domain . 'fetch_one' => ['quote_id' => true],
        ];
    }

    /** @throws \Exception */
    public function executeFetchOne(array $args): array
    {
        $params = $this->extractParameters($args, ['quote_id']);

        try {
            return $this->getContainer()
                ->get(QuoteForRpcRetriever::class)
                ->fetchOne($params['quote_id'])
                ->toArray();
        } catch (\Exception $previous_exception) {
            // Nicer log in Sentry
            throw new InternalServerErrorException(sprintf('[QUOTE] Failed to fetch one quote with id "%s"', $params['quote_id']), $previous_exception);
        }
    }
}
