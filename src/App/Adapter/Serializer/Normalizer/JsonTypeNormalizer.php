<?php

namespace App\Adapter\Serializer\Normalizer;

use App\DataLoader\Type\JsonType;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

class JsonTypeNormalizer implements DenormalizerInterface
{
    public function denormalize($data, $type, $format = null, array $context = [])
    {
        return json_decode($data, true, 512, JSON_THROW_ON_ERROR);
    }

    public function supportsDenormalization($data, $type, $format = null): bool
    {
        return is_string($data) && JsonType::class === $type;
    }
}
