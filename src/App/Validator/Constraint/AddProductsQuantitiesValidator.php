<?php

namespace App\Validator\Constraint;

use <PERSON><PERSON>fony\Component\Validator\Constraint;
use S<PERSON>fony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * Class AddProductsQuantitiesValidator.
 */
class AddProductsQuantitiesValidator extends ConstraintValidator
{
    /** {@inheritdoc} */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof AddProductsQuantities) {
            throw new UnexpectedTypeException($constraint, ProductsQuantities::class);
        }

        if (!is_array($value)) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ reason }}', 'Value is not an array.')
                ->addViolation();

            return;
        }

        foreach ($value as $one_value) {
            if (!array_key_exists('product_key', $one_value)) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->setParameter('{{ reason }}', 'The parameter "product_key" is mandatory')
                    ->addViolation();
            }

            if (!is_numeric($one_value['product_key']) || $one_value['product_key'] < 1) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->setParameter(
                        '{{ reason }}',
                        sprintf('Field "%s" must be a numeric greater than 0.', 'product_key')
                    )
                    ->addViolation();
            }

            if (!(array_key_exists('quantity', $one_value) xor array_key_exists('add_quantity', $one_value))) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->setParameter('{{ reason }}', 'One of parameters "quantity" or "add_quantity" is mandatory')
                    ->addViolation();
            }

            $field = array_key_exists('quantity', $one_value) ? 'quantity' : 'add_quantity';

            if (!is_numeric($one_value[$field]) || $one_value[$field] < 1) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->setParameter('{{ reason }}', sprintf('Field "%s" must be a numeric greater than 0.', $field))
                    ->addViolation();
            }
        }
    }
}
