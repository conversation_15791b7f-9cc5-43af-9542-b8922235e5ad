<?php

namespace App\Validator\Constraint;

use App\Exception\NotFoundException;
use SonVideo\Erp\Article\Entity\PackagedArticleEntity;
use SonVideo\Erp\Article\Manager\ArticleCachedDataProvider;
use SonVideo\Erp\Article\Manager\PackageCachedDataProvider;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class IsBelowPackagedArticlesValidator extends ConstraintValidator
{
    private ArticleCachedDataProvider $article_data_provider;
    private PackageCachedDataProvider $package_data_provider;

    public function __construct(
        ArticleCachedDataProvider $article_data_provider,
        PackageCachedDataProvider $package_data_provider
    ) {
        $this->article_data_provider = $article_data_provider;
        $this->package_data_provider = $package_data_provider;
    }

    /** @throws NotFoundException */
    public function validate($price, Constraint $constraint): void
    {
        if (!$constraint instanceof IsBelowPackagedArticles) {
            throw new UnexpectedTypeException($constraint, IsBelowPackagedArticles::class);
        }

        $dto = $this->context->getRoot();

        $article = $this->article_data_provider->getOneById($dto->article_id);

        if ($article['is_package'] && $price > $this->getPackagedArticlesTotalSellingPrice($dto->article_id)) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'package_selling_price_too_high')
                ->setParameter('{{ price }}', $price)
                ->setParameter('{{ reason }}', 'The price must be below the packaged article price')
                ->addViolation();
        }
    }

    private function getPackagedArticlesTotalSellingPrice(int $article_id): float
    {
        return array_reduce(
            $this->package_data_provider->findAllByPackageId($article_id),
            static fn ($carry, PackagedArticleEntity $packaged_article): float => $carry +
                $packaged_article->quantity * $packaged_article->unit_selling_price,
            0.0
        );
    }
}
