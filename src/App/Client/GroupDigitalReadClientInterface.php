<?php

namespace App\Client;

/**
 * @phpstan-type ArticleItem array{
 *      article_code: string,
 *      supplier: int,
 *      brand: int,
 *      range: string,
 *      ean: int,
 *      division: string
 *  }
 */
interface GroupDigitalReadClientInterface
{
    /**
     * @return array<int,string> Array associating each brand id to its code (eg: 239252 => "HOD")
     *
     * @throws \Exception
     */
    public function getAllBrands(): array;

    /**
     * @return array<int,string> Array associating each supplier id to its code (eg: 3001 => "BLUEWINE")
     *
     * @throws \Exception
     */
    public function getAllSuppliers(): array;

    /**
     * @return array<int, ArticleItem>
     *
     * @throws \Exception
     */
    public function getAllArticles(): array;
}
