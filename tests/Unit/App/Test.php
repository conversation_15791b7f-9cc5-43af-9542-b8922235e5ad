<?php
/*
 * This file is part of wall-e package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Unit;

use App\Kernel;
use League\Flysystem\MountManager;
use mageekguy\atoum;
use mageekguy\atoum\adapter;
use mageekguy\atoum\annotations\extractor;
use mageekguy\atoum\asserter\generator;
use mageekguy\atoum\test\assertion\manager;
use Psr\Container\ContainerInterface;
use Symfony\Component\Dotenv\Dotenv;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Class Test.
 *
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class Test extends atoum\test
{
    public const ENV_FILE = __DIR__ . '/../../../.env.test';
    public const FIXTURES_DIR = __DIR__ . '/../../fixtures';

    /** @var Kernel */
    protected $kernel;

    /** @var bool */
    protected $kernel_booted = false;

    /** {@inheritdoc} */
    public function __construct(
        adapter $adapter = null,
        extractor $annotationExtractor = null,
        generator $asserterGenerator = null,
        manager $assertionManager = null,
        \Closure $reflectionClassFactory = null
    ) {
        parent::__construct(
            $adapter,
            $annotationExtractor,
            $asserterGenerator,
            $assertionManager,
            $reflectionClassFactory
        );
        $this->setTestNamespace('Tests\Unit');

        static::loadEnv();
    }

    /** Handy method to call within the test method to avoid atoum telling us that there is a void method */
    public function assertMockeryAssertionsPasses(): void
    {
        if ($container = \Mockery::getContainer()) {
            $mockery_assertions = $container->mockery_getExpectationCount();

            if ($mockery_assertions > 0) {
                $this->integer($mockery_assertions)->isEqualTo($mockery_assertions);
            }
        }
    }

    /**
     * This is the equivalent of the tearDown method in PHPUNIT
     * Not to confuse with the tearDown method below which is run only once after all test methods are finished.
     */
    public function afterTestMethod($method): void
    {
        if ($container = \Mockery::getContainer()) {
            $mockery_assertions = $container->mockery_getExpectationCount();

            // Add all mockery assertions to atoum assertions count
            for ($i = 0; $i < $mockery_assertions; ++$i) {
                $this->getScore()->addPass();
            }
        }

        // This triggers Mockery expectations
        \Mockery::close();
    }

    public function tearDown(): void
    {
        $this->getKernel()->shutdown();
    }

    protected static function loadEnv(): void
    {
        $dotenv = new Dotenv();

        $dotenv->load(static::ENV_FILE);

        if (file_exists(static::ENV_FILE . '.local')) {
            $dotenv->load(static::ENV_FILE . '.local');
        }
    }

    protected function getKernel(): Kernel
    {
        if (null === $this->kernel || !$this->kernel_booted) {
            $this->initKernel();
        }

        return $this->kernel;
    }

    protected function initKernel(): void
    {
        $this->kernel = new Kernel($_ENV['APP_ENV'], true);
        $this->kernel->boot();
        $this->kernel_booted = true;
    }

    /**
     * @see vendor/symfony/framework-bundle/Test/KernelTestCase.php:77
     * @see https://symfony.com/blog/new-in-symfony-4-1-simpler-service-testing
     */
    protected function getContainer(): ContainerInterface
    {
        return $this->getKernel()
            ->getContainer()
            ->get('test.service_container');
    }

    protected function getUploadedFile(string $file_name, string $mime_type): UploadedFile
    {
        $kernel_root_dir = $this->getContainer()->getParameter('kernel.root_dir');
        $file_path = sprintf('%s/../../tests/fixtures/files/%s', $kernel_root_dir, $file_name);

        return new UploadedFile($file_path, $file_name, $mime_type, null, true);
    }

    public static function moveFile(
        ContainerInterface $container,
        string $target_fs,
        string $target_name,
        string $source_name
    ): void {
        $mount_manager = $container->get(MountManager::class);
        $source_fs = $mount_manager->getFilesystem('mock_filesystem');
        $target_fs = $mount_manager->getFilesystem(sprintf('%s_filesystem', $target_fs));

        if ($target_fs->has($target_name)) {
            $target_fs->delete($target_name);
        }

        $content = $source_fs->read($source_name);
        $target_fs->write($target_name, $content);
    }
}
