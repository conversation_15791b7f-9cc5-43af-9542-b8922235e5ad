<?php

namespace App\Tests\Mock\Erp\Client;

use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\Payment\Manager\PaymentV2CreatorInterface;

class PaymentV2Creator implements PaymentV2CreatorInterface
{
    public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
    {
        return null;
    }

    public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
    {
        return null;
    }
}
