<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Erp\Client;

use SonVideo\Erp\Client\OgoneApiClient as Original;

class OgoneApiClient extends Original
{
    public function call(string $payment_creation_proof, string $account)
    {
        $xml = '';
        if ('1827682-4' === $payment_creation_proof) {
            $xml = <<<XML
            <?xml version="1.0"?>
            <ncresponse
            orderID="1827682-4"
            PAYID=""
            PAYIDSUB=""
            NCSTATUS="5"
            NCERROR="********"
            NCERRORPLUS="unknown orderid 1827682-1 for merchant sonvideo2"
            ACCEPTANCE=""
            STATUS="">
            </ncresponse>
            XML;
        }
        if ('2051667-1' === $payment_creation_proof) {
            $xml = <<<XML
            <?xml version="1.0"?>
            <ncresponse
            orderID="2051667-1"
            PAYID="**********"
            PAYIDSUB=""
            NCSTATUS="4"
            NCERROR=""
            NCERRORPLUS=""
            ACCEPTANCE=""
            STATUS="1"
            IPCTY="FR"
            CCCTY=""
            ECI=""
            CVCCheck="NO"
            AAVCheck="NO"
            VC="NO"
            amount="50"
            currency="EUR"
            PM="CreditCard"
            BRAND=""
            CARDNO=""
            IP="*************"
            PSPID="sonvideobo2"
            MOYEN="CBS-OT">
            </ncresponse>
            XML;
        }
        if ('2051933-4' === $payment_creation_proof) {
            $xml = <<<XML
            <?xml version="1.0"?>
            <ncresponse
            orderID="2051933-4"
            PAYID="5837049670"
            PAYIDSUB=""
            NCSTATUS="0"
            NCERROR="0"
            NCERRORPLUS="!"
            ACCEPTANCE="999337"
            STATUS="5"
            IPCTY="FR"
            CCCTY="FR"
            ECI="1"
            CVCCheck="OK"
            AAVCheck="NO"
            VC="NO"
            amount="154.9"
            currency="EUR"
            PM="CreditCard"
            BRAND="MasterCard"
            CARDNO="XXXXXXXXXXXX3381"
            IP="**************"
            PSPID="sonvideobo2"
            MOYEN="CBS-OT">
            </ncresponse>
            XML;
        }
        if ('2052062-4' === $payment_creation_proof) {
            $xml = <<<XML
            <?xml version="1.0"?>
            <ncresponse
            orderID="2052062-4"
            PAYID="5837496109"
            PAYIDSUB=""
            NCSTATUS="0"
            NCERROR="0"
            NCERRORPLUS="!"
            ACCEPTANCE="178938"
            STATUS="6"
            IPCTY="GB"
            CCCTY="GB"
            ECI="1"
            CVCCheck="OK"
            AAVCheck="NO"
            VC="NO"
            amount="4.39"
            currency="EUR"
            PM="CreditCard"
            BRAND="VISA"
            CARDNO="XXXXXXXXXXXX8361"
            IP="*************"
            PSPID="sonvideobo2"
            MOYEN="CBS-COT">
            </ncresponse>
            XML;
        }

        if ('123456-1' === $payment_creation_proof) {
            $xml = <<<XML
            <?xml version="1.0"?>
            <ncresponse
            orderID="123456-1"
            PAYID="5837496109"
            PAYIDSUB=""
            NCSTATUS="0"
            NCERROR="0"
            NCERRORPLUS="!"
            ACCEPTANCE="178938"
            STATUS="6"
            IPCTY="GB"
            CCCTY="GB"
            ECI="1"
            CVCCheck="OK"
            AAVCheck="NO"
            VC="NO"
            amount="4.39"
            currency="EUR"
            PM="CreditCard"
            BRAND="VISA"
            CARDNO="XXXXXXXXXXXX8361"
            IP="*************"
            PSPID="sonvideobo2"
            MOYEN="CBS-COT">
            </ncresponse>
            XML;
        }

        if ('123457-1' === $payment_creation_proof) {
            $xml = <<<XML
            <?xml version="1.0"?>
            <ncresponse
            orderID="123457-1"
            PAYID="5837496108"
            PAYIDSUB=""
            NCSTATUS="0"
            NCERROR="0"
            NCERRORPLUS="!"
            ACCEPTANCE="178939"
            STATUS="6"
            IPCTY="GB"
            CCCTY="GB"
            ECI="1"
            CVCCheck="OK"
            AAVCheck="NO"
            VC="NO"
            amount="4.39"
            currency="EUR"
            PM="CreditCard"
            BRAND="VISA"
            CARDNO="XXXXXXXXXXXX8362"
            IP="*************"
            PSPID="sonvideobo2"
            MOYEN="CBS-COT">
            </ncresponse>
            XML;
        }

        return $xml;
    }
}
