<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Carrier\Client;

use League\Flysystem\FileNotFoundException;
use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use SonVideo\Erp\Carrier\Contract\CarrierClientInterface;
use SonVideo\Erp\Carrier\Contract\MondialRelayAwareTrait;

class MondialRelayClient implements CarrierClientInterface
{
    use MondialRelayAwareTrait;

    public const FIXTURE_FILESYSTEM = 'mock_filesystem';

    /** @var FilesystemInterface */
    private $filesystem;

    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(static::FIXTURE_FILESYSTEM);
    }

    public function init(string $end_point, array $options = [])
    {
        return $this;
    }

    /**
     * @param $params
     *
     * @throws FileNotFoundException
     */
    public function WSI2_CreationEtiquette($params): \SimpleXMLElement
    {
        $xml = str_ireplace(
            // simple xml does not like the soap specific tags
            ['soap:', 'ns1:'],
            '',
            $this->filesystem->read(sprintf('carrier/sticker/mondial-relay-%s.xml', $params['NDossier']))
        );

        $xml = str_replace(
            sprintf('/fixtures/%s', $params['NDossier']),
            base64_encode($this->filesystem->read(sprintf('carrier/sticker/%s.pdf', $params['NDossier']))),
            $xml
        );

        return simplexml_load_string($xml);
    }

    /**
     * logRequestAndResponse.
     *
     * @return $this
     */
    public function logRequestAndResponse(string $status = 'success')
    {
        return $this;
    }
}
