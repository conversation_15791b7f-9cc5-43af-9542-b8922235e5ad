<?php

namespace App\Tests\Utils\Database;

class PgDatabase
{
    /** @var string */
    public static $specific_fixtures_dir = __DIR__ . '/../../fixtures/pgsql_specific_fixtures';

    protected static function getDefaultPlaceHolders(): array
    {
        $pass = $_ENV['DATABASE_SU_PASSWORD'];

        return [
            ':host' => $_ENV['DATABASE_HOST'],
            ':port' => $_ENV['DATABASE_PORT'],
            ':name' => $_ENV['DATABASE_NAME'],
            ':name_template' => $_ENV['DATABASE_NAME_TEMPLATE'],
            ':user' => 'postgres',
            ':pass' => empty($pass) ? '' : sprintf('PGPASSWORD="%s" ', $pass),
        ];
    }

    public static function reloadFixtures(array $placeholders = []): void
    {
        if ([] === $placeholders) {
            $placeholders = static::getDefaultPlaceHolders();
        }

        static::killOpenConnections($placeholders);
        static::createDb($placeholders);
    }

    protected static function killOpenConnections(array $placeholders): void
    {
        $command = <<<EOF
        :pass psql -p :port -U :user -h :host --command "select pg_terminate_backend(pid) from pg_stat_activity where datname = ':name' and pid <> pg_backend_pid();" \
            && :pass psql -p :port -U :user -h :host --command "select pg_terminate_backend(pid) from pg_stat_activity where datname = ':name_template' and pid <> pg_backend_pid();"
        EOF;

        static::exec(strtr($command, $placeholders));
    }

    protected static function createDb(array $placeholders): void
    {
        $command = <<<EOF
        :pass dropdb -p :port -U :user -h :host --if-exists :name \
            && :pass createdb -p :port -U :user -h :host -T :name_template -O svd_dev :name
        EOF;
        static::exec(strtr($command, $placeholders));
    }

    public static function loadSpecificFixtures(array $files, array $placeholders = []): void
    {
        if ([] === $placeholders) {
            $placeholders = static::getDefaultPlaceHolders();
        }

        $placeholders[':files'] = implode(
            ' ',
            array_map(function ($file): string {
                return sprintf('-f %s', realpath(sprintf('%s/%s', static::$specific_fixtures_dir, $file)));
            }, $files)
        );

        static::exec(strtr(':pass psql -q -p :port -U :user -h :host -d :name :files', $placeholders));
    }

    /**
     * _exec.
     *
     * @throws \Exception
     */
    protected static function exec(string $command): void
    {
        $return_code = null;
        $output_buffer = null;

        exec($command);

        if (0 != $return_code) {
            throw new \Exception($output_buffer);
        }
    }
}
