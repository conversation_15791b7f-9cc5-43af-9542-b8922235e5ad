<?php

namespace App\Tests\Utils\Database;

class MySqlDatabase
{
    /** @var string */
    public static $specific_fixtures_dir = __DIR__ . '/../../fixtures/mysql';

    /** @var string */
    public static $triggers_dir = __DIR__ . '/../../../vendor/son-video/phcdb/routines/*/triggers';

    public static $common_fixtures_dir = __DIR__ . '/../../../vendor/son-video/phcdb/migrations/test.sql';

    /**
     * clearDatabases.
     *
     * A few points to highlight:
     * - We won't use truncation as MySQl does not support TRUNCATE CASCADE and TRUNCATE statements may fail if the
     * table as an FK
     * - We don't filter table with either records or an auto increment higher than 1 because it's not reliable
     *   The documentation specify that the stat table is not synced with the table:
     *   "For InnoDB tables, the row count is only a rough estimate used in MYSQL optimization. (This is also true if
     *   the InnoDB table is partitioned.)"
     *
     * @throws \Exception
     */
    public static function clearDatabases(array $placeholders = []): void
    {
        $databases = ['backOffice', 'PHC_frontoffice', 'paiements', 'data_warehouse'];
        $databases_flat_list = implode(
            ',',
            array_map(static function ($db): string {
                return sprintf("'%s'", $db);
            }, $databases)
        );

        if ([] === $placeholders) {
            $placeholders = static::getDefaultPlaceHolders();
        }

        // 1 - Remove triggers based on the DELETE events, otherwise the DELETE FROM table mays fails in the next queries
        $query = <<<MYSQL
        SELECT CONCAT('DROP TRIGGER IF EXISTS ', TRIGGER_SCHEMA ,'.', TRIGGER_NAME ,';') AS stmts
          FROM information_schema.TRIGGERS
          WHERE TRIGGER_SCHEMA IN (:databases)
          AND EVENT_MANIPULATION = 'DELETE'
        ;
        MYSQL;
        $query = strtr($query, [
            ':databases' => $databases_flat_list,
        ]);
        $placeholders[':source'] = implode("\n", ['--skip-column-names  << EOF', $query, 'EOF']);
        $drop_triggers_statements = static::loadTextInDb($placeholders)['output_buffer'];

        // for debugging purpose
        // echo print_r($drop_triggers_statements, true) . "\n";

        // 2 - Prepare DELETE statements on all table in specified schemas
        $query = <<<MYSQL
        SELECT
          concat('DELETE FROM ', TABLE_SCHEMA, '.', TABLE_NAME, ';') AS stmts
        FROM
          information_schema.TABLES
        WHERE TABLE_SCHEMA IN (:databases)
          AND TABLE_TYPE = 'BASE TABLE'
        ;
        MYSQL;
        $query = strtr($query, [
            ':databases' => $databases_flat_list,
        ]);
        $placeholders[':source'] = implode("\n", ['--skip-column-names  << EOF', $query, 'EOF']);
        $delete_statements = static::loadTextInDb($placeholders)['output_buffer'];

        // for debugging purpose
        // echo print_r($delete_statements, true) . "\n";

        // 3 - Reset the AUTO INCREMENT on some tables
        $query = <<<MYSQL
        SELECT
          concat('ALTER TABLE ', TABLE_SCHEMA, '.', TABLE_NAME, ' AUTO_INCREMENT = 1;') AS stmts
        FROM
          information_schema.TABLES
        WHERE TABLE_SCHEMA IN (:databases)
          AND TABLE_TYPE = 'BASE TABLE'
          AND (AUTO_INCREMENT IS NOT NULL AND AUTO_INCREMENT > 1)
        ;
        MYSQL;
        $query = strtr($query, [
            ':databases' => $databases_flat_list,
        ]);
        $placeholders[':source'] = implode("\n", ['--skip-column-names  << EOF', $query, 'EOF']);
        $reset_autoincrements_statements = static::loadTextInDb($placeholders)['output_buffer'];

        // for debugging purpose
        // echo print_r($reset_autoincrements_statements, true) . "\n";

        // 4 - Deactivate FKs since we're using Innodb and put the statements in between.
        $placeholders[':source'] = implode(
            "\n",
            array_merge(
                ['--skip-column-names  << EOF'],
                ['SET FOREIGN_KEY_CHECKS = 0;'],
                $drop_triggers_statements,
                $delete_statements,
                $reset_autoincrements_statements,
                ['SET FOREIGN_KEY_CHECKS = 1;'],
                ['EOF']
            )
        );

        static::loadTextInDb($placeholders);

        // 5 - Recreate DELETE triggers
        static::reloadTriggers($placeholders);

        static::reloadCommonFixtures($placeholders);
    }

    public static function loadSpecificFixtures(array $files, array $placeholders = []): void
    {
        $placeholders = array_merge(static::getDefaultPlaceHolders(), $placeholders);

        $is_in_query_debug_mode = ($_ENV['DEBUG_MODE'] ?? 'OFF') === 'QUERY';
        foreach ($files as $file) {
            if ($is_in_query_debug_mode) {
                $content = file_get_contents(realpath(sprintf('%s/%s', static::$specific_fixtures_dir, $file)));
                $queries = explode('-- DEBUG_QUERY', $content);

                foreach ($queries as $query) {
                    $placeholders[':source'] = implode("\n", ['<< EOF', trim($query), 'EOF']);
                    static::loadTextInDb($placeholders);
                }
            } else {
                $placeholders[':source'] = realpath(sprintf('%s/%s', static::$specific_fixtures_dir, $file));

                static::loadFileInDb($placeholders);
            }
        }
    }

    protected static function reloadTriggers(array $placeholders = []): void
    {
        if ([] === $placeholders) {
            $placeholders = static::getDefaultPlaceHolders();
        }

        $files = glob(static::$triggers_dir . '/*.sql');
        foreach ($files as $file) {
            $placeholders[':source'] = realpath($file);
            $placeholders[':extra'] = ' --delimiter=//  --default-character-set=latin1';

            static::loadFileInDb($placeholders);
        }
    }

    protected static function reloadCommonFixtures(array $placeholders = []): void
    {
        if ([] === $placeholders) {
            $placeholders = static::getDefaultPlaceHolders();
        }

        $placeholders[':source'] = realpath(static::$common_fixtures_dir);
        $placeholders[':extra'] = ' --delimiter=//  --default-character-set=latin1';

        static::loadFileInDb($placeholders);
    }

    protected static function loadFileInDb(array $placeholders): void
    {
        if (!isset($placeholders[':source']) || !file_exists($placeholders[':source'])) {
            throw new \Exception(sprintf('fixture "%s" does not exists', $placeholders[':source']));
        }

        $placeholders[':extra'] = $placeholders[':extra'] ?? '';
        $placeholders[':database'] = $placeholders[':database'] ?? 'backOffice';

        static::exec(
            strtr('mysql --default-character-set=utf8 :user:pass-h :host -D :database :extra < :source', $placeholders)
        );
    }

    /**
     * @return array{command: string, output_buffer: string[], return_code: int}
     *
     * @throws \Exception
     */
    protected static function loadTextInDb(array $placeholders): array
    {
        $placeholders[':database'] = $placeholders[':database'] ?? 'backOffice';

        // EOF replaces $ by an empty string as it's the variable prefix in bash context
        return static::exec(
            strtr(
                'mysql --default-character-set=utf8 :user:pass-h :host -D :database :source',
                str_replace('$', '\$', $placeholders)
            )
        );
    }

    protected static function getDefaultPlaceHolders(): array
    {
        $db_url = parse_url($_ENV['DATABASE_LEGACY_URL']);

        return [
            ':host' => $db_url['host'],
            ':port' => $db_url['port'],
            ':user' => sprintf('-u %s ', $db_url['user']),
            ':pass' => sprintf('-p%s ', $db_url['pass']),
        ];
    }

    /**
     * @return array{command: string, output_buffer: string[], return_code: int}
     *
     * @throws \Exception
     */
    protected static function exec(string $command): array
    {
        exec($command, $output_buffer, $return_code);

        // for debugging purpose
        //         echo print_r(compact('command', 'output_buffer', 'return_code'), true) . "\n";

        if (0 != $return_code) {
            echo print_r($command, true) . "\n";

            throw new \Exception(sprintf('[MYSQL COMMAND ERROR CODE %s] %s', $return_code, implode("\n", $output_buffer)));
        }

        return ['command' => $command, 'output_buffer' => $output_buffer, 'return_code' => $return_code];
    }
}
