<?php

namespace App\Tests\Utils\PHPUnit\EndToEnd;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

abstract class AbstractCPostBasicWebTestCase extends WebTestCase
{
    protected KernelBrowser $client;
    protected BasicE2eJsonRequest $request;

    protected const URL_ENDPOINT = self::class;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $this->request = BasicE2eJsonRequest::with($this->client);

        if (self::class === static::URL_ENDPOINT) {
            throw new \Exception('URL_ENDPOINT must be defined in the child class');
        }
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            Request::METHOD_POST,
            static::URL_ENDPOINT,
            [],
            [],
            ['CONTENT_TYPE' => 'application/json']
        );

        $this->assertEquals(
            Response::HTTP_UNAUTHORIZED,
            $this->client->getResponse()->getStatusCode(),
            'Without authorization, the request should return a 401 response'
        );
    }

    public function test_with_a_non_valid_authorization(): void
    {
        $this->request->post(static::URL_ENDPOINT, '', ['HTTP_AUTHORIZATION' => BearerReferential::UNKNOWN_TOKEN]);

        $this->assertEquals(
            Response::HTTP_UNAUTHORIZED,
            $this->client->getResponse()->getStatusCode(),
            'With a non valid authorization, the request should return a 401 response'
        );
    }
}
