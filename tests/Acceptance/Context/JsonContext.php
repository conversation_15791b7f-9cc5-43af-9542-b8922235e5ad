<?php
/*
 * This file is part of advice package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Acceptance\Context;

use <PERSON>hat\Gherkin\Node\PyStringNode;
use <PERSON>hatch\Context\JsonContext as BaseJsonContext;

/**
 * Class JsonContext.
 *
 * <AUTHOR> <<EMAIL>>
 */
class JsonContext extends BaseJsonContext
{
    /** @var string */
    public static $scripts_dir = __DIR__ . '/../../fixtures/scripts';

    /**
     * Check that given JSON node is an uuid v4.
     *
     * @throws \Exception
     * @Then the JSON node :node should be an uuid
     */
    public function theJsonNodeShouldBeAnUuid(string $node)
    {
        return $this->theJsonNodeShouldMatch($node, '/\w{8}-\w{4}-\w{4}-\w{4}-\w{12}/');
    }

    /**
     * Check that given JSON node is an uuid v4.
     *
     * @throws \Exception
     * @Then the JSON node :node should be an array
     */
    public function theJsonNodeShouldBeAnArray(string $node)
    {
        $json = $this->getJson();
        $actual = $this->inspector->evaluate($json, $node);

        if (!is_array($actual)) {
            throw new \Exception(sprintf("The node is not an array: '%s'", json_encode($actual, JSON_THROW_ON_ERROR)));
        }
    }

    /**
     * Check that given JSON node is a date which is equal to the $string_date.
     *
     * @throws \Exception
     *
     * @Then the JSON node :node should be equal to the datetime :string_date
     */
    public function theJsonNodeShouldBeEqualToTheDateTime(string $node, string $string_date)
    {
        $json = $this->getJson();
        $actual = $this->inspector->evaluate($json, $node);
        $actual_date = new \DateTime($actual);
        $expected_date = new \DateTime($string_date);

        foreach (['y', 'm', 'd', 'h', 'i', 's', 'f'] as $property) {
            if (abs($actual_date->diff($expected_date)->{$property}) > 0) {
                throw new \Exception(sprintf("The node value is '%s'", json_encode($actual, JSON_THROW_ON_ERROR)));
            }
        }
    }

    /**
     * Check that given JSON node is a date which is nearly equal to now.
     *
     * @throws \Exception
     * @Then the JSON node :node should be nearly equal to now
     */
    public function theJsonNodeShouldBeNearlyEqualToNow(string $node)
    {
        $json = $this->getJson();
        $actual = $this->inspector->evaluate($json, $node);
        $actual_date = new \DateTime($actual);
        $expected_date = new \DateTime();

        if (abs($actual_date->diff($expected_date)->s) > 3) {
            throw new \Exception(sprintf("The node value is '%s'", json_encode($actual, JSON_THROW_ON_ERROR)));
        }
    }

    /**
     * theJSONNodeShouldBeEqualToThePyStringNode.
     *
     * @Given /^the JSON node "([^"]*)" should be equal to the string$/
     *
     * @throws \Exception
     */
    public function theJSONNodeShouldBeEqualToThePyStringNode(string $node, PyStringNode $text): void
    {
        $this->theJsonNodeShouldBeEqualToTheString($node, $text->getRaw());
    }

    /**
     * @Given /^the JSON node "([^"]*)" should contain the string$/
     *
     * @throws \Exception
     */
    public function theJSONNodeShouldContainThePyStringNode(string $node, PyStringNode $text): void
    {
        $json = $this->getJson();

        $actual = $this->inspector->evaluate($json, $node);

        $this->assertContains($text->getRaw(), (string) $actual);
    }

    /**
     * @return void
     * @info Sublevels are checked
     *
     * @throws \Exception
     *
     * @Given /^the JSON node "([^"]*)" should be identical to$/
     */
    public function theJsonNodeShouldBeIdenticalTo(string $node, PyStringNode $text)
    {
        $json = $this->getJson();
        $source = json_encode((array) $this->inspector->evaluate($json, $node), JSON_THROW_ON_ERROR);
        $expected = json_encode(json_decode($text->getRaw(), true, 512, JSON_THROW_ON_ERROR), JSON_THROW_ON_ERROR);

        $command = <<<EOF
        bash :tmp_path/json_diff.sh :expected :source
        EOF;

        $test = strtr($command, [
            ':source' => escapeshellarg($source),
            ':expected' => escapeshellarg($expected),
            ':tmp_path' => static::$scripts_dir,
        ]);

        exec($test, $output);

        if (count($output) > 3) {
            $separator = '-------------------------------------------------------';

            $messages = [$separator];
            foreach ($output as $index => $line) {
                // ignore line indicator thrown by the diff tool
                if (false !== strpos($line, '@@')) {
                    continue;
                }

                if ($index > 2) {
                    // colorize in light gray
                    $mask = "\e[1,36m%s\e[0m";

                    if (0 === strpos($line, '+')) {
                        // colorize in orange
                        $mask = "\e[0;33m%s\e[0m";
                    }

                    if (0 === strpos($line, '-')) {
                        // colorize in red
                        $mask = "\e[0;31m%s\e[0m";
                    }

                    $messages[] = sprintf($mask, preg_replace(['/^\+/', '/^-/'], ['>', '!'], $line));
                }
            }

            $messages[] = $separator;

            throw new \Exception(sprintf("\e[0mExpected JSON is not identical to JSON node, diff :\n%s\n", implode("\n", $messages)));
        }
    }

    /**
     * @param string $timestamp_columns list of columns to ignore
     * @info Sublevels are checked
     *
     * @return void
     *
     * @throws \Exception
     * @Given /^the JSON node "([^"]*)" with ignored columns "([^"]*)" should be identical to$/
     */
    public function theJsonNodeWithIgnoredColumnsShouldBeIdenticalTo(
        string $node,
        string $timestamp_columns,
        PyStringNode $text
    ) {
        $json = $this->getJson();
        $source = json_encode((array) $this->inspector->evaluate($json, $node), JSON_THROW_ON_ERROR);
        $expected = json_encode(json_decode($text->getRaw(), true, 512, JSON_THROW_ON_ERROR), JSON_THROW_ON_ERROR);

        foreach (explode(',', str_replace(' ', '', $timestamp_columns)) as $column) {
            // We search for a valued column in both source and expected
            // Only string column (surrounded by double quote) can be ignored
            // Useful for timestamp column which are set on runtime
            $source = preg_replace(sprintf('/(%s":")(.*?)(")/', $column), '$1ignored$3', $source);
            $expected = preg_replace(sprintf('/(%s":")(.*?)(")/', $column), '$1ignored$3', $expected);
        }

        $command = <<<EOF
        bash :tmp_path/json_diff.sh :expected :source
        EOF;

        $test = strtr($command, [
            ':source' => escapeshellarg($source),
            ':expected' => escapeshellarg($expected),
            ':tmp_path' => static::$scripts_dir,
        ]);

        exec($test, $output);

        if (count($output) > 3) {
            $separator = '-------------------------------------------------------';

            $messages = [$separator];
            foreach ($output as $index => $line) {
                // ignore line indicator thrown by the diff tool
                if (false !== strpos($line, '@@')) {
                    continue;
                }

                if ($index > 2) {
                    // colorize in light gray
                    $mask = "\e[1,36m%s\e[0m";

                    if (0 === strpos($line, '+')) {
                        // colorize in orange
                        $mask = "\e[0;33m%s\e[0m";
                    }

                    if (0 === strpos($line, '-')) {
                        // colorize in red
                        $mask = "\e[0;31m%s\e[0m";
                    }

                    $messages[] = sprintf($mask, preg_replace(['/^\+/', '/^-/'], ['>', '!'], $line));
                }
            }

            $messages[] = $separator;

            throw new \Exception(sprintf("\e[0mExpected JSON is not identical to JSON node, diff :\n%s\n", implode("\n", $messages)));
        }
    }

    /**
     * Check that the given node have the correct key(s) and value(s).
     *
     * @Given /^the JSON node "([^"]*)" should have the following key(s) and value(s)$/
     *
     * @throws \Exception
     */
    public function theJSONNodeShouldHaveTheColumnsAndValuesInThePyStringNode(string $node, PyStringNode $text)
    {
        $json = $this->getJson();
        $actual = (array) $this->inspector->evaluate($json, $node);
        $expected = json_decode($text->getRaw(), true, 512, JSON_THROW_ON_ERROR);

        if (count($actual) !== (is_countable($expected) ? count($expected) : 0)) {
            throw new \Exception(sprintf('Expected `%s` keys, got `%s`', is_countable($expected) ? count($expected) : 0, count($actual)));
        }

        foreach ($actual as $key => $value) {
            $formatted_expected_value = '' === $expected[$key] ? '<empty string>' : $expected[$key];
            // does not work with php switch :(
            if (null === $formatted_expected_value) {
                $formatted_expected_value = '<null>';
            }
            if (false === $formatted_expected_value || true === $formatted_expected_value) {
                $formatted_expected_value = $formatted_expected_value ? '<true>' : '<false>';
            }

            if (is_object($value)) {
                $value = (array) $value;
            }

            if (is_array($value)) {
                if (!is_array($expected[$key])) {
                    throw new \Exception(sprintf('Key `%s` should have an `<array>` as value, got `%s`', $key, $formatted_expected_value));
                }

                continue;
            }

            // scalars
            if (
                (!is_float($value) && $value !== $expected[$key]) ||
                (is_float($value) && abs($value - $expected[$key]) > 0.00001)
            ) {
                $formatted_value = '' === $value ? '<empty string>' : $value;
                // does not work with php switch :(
                if (null === $formatted_value) {
                    $formatted_value = '<null>';
                }
                if (false === $formatted_value || true === $formatted_value) {
                    $formatted_value = $formatted_value ? '<true>' : '<false>';
                }

                throw new \Exception(sprintf('Key `%s` should have `%s` as value, got `%s`', $key, $formatted_expected_value, $formatted_value));
            }
        }
    }

    /**
     * Check that given JSON node is an integer greater than a number.
     *
     * @throws \Exception
     *
     * @Then the JSON node :node should be an integer greater than :number
     */
    public function theJsonNodeShouldBeAnIntegerGreaterThan(string $node, int $number)
    {
        $json = $this->getJson();

        $actual = $this->inspector->evaluate($json, $node);

        if ($actual !== (float) $number && $actual <= $number) {
            throw new \Exception(sprintf('The node value is `%s`', json_encode($actual, JSON_THROW_ON_ERROR)));
        }
    }

    /**
     * Checks, that given JSON node has N element(s).
     *
     * @throws \Exception
     *
     * @Then the JSON node :node should have at least :count element(s)
     */
    public function theJsonNodeShouldHaveAtLeastElements(string $node, int $count)
    {
        $json = $this->getJson();

        $actual = $this->inspector->evaluate($json, $node);

        if ($count > count((array) $actual)) {
            throw new \Exception(sprintf('The node have `%d` element(s), expected at least `%d`.', count((array) $actual), $count));
        }
    }

    /**
     * Checks, that given JSON node has art least N key(s).
     *
     * @throws \Exception
     *
     * @Then the JSON node :node should have at least :count key(s)
     */
    public function theJsonNodeShouldHaveColumns(string $node, int $count)
    {
        $json = $this->getJson();

        $actual = $this->inspector->evaluate($json, $node);

        if ($count > count((array) $actual)) {
            throw new \Exception(sprintf('The node have `%d` column(s), expected at least `%d`.', count(array_keys($actual)), $count));
        }
    }

    /**
     * Checks that given JSON node has a '_pager' element properly formatted.
     *
     * @return array
     *
     * @throws \Exception
     *
     * @Then the JSON node :node should have a pager
     */
    public function theJsonNodeShouldHaveAPager(string $node)
    {
        $actual = (array) $this->inspector->evaluate($this->getJson(), $node);

        if (!isset($actual['_pager'])) {
            throw new \Exception('The node misses "_pager" node.');
        }

        $pager = (array) $actual['_pager'];
        $expected_pager_keys = ['from', 'to', 'total', 'page', 'limit', 'last_page'];
        $missing_keys = array_diff($expected_pager_keys, array_keys($pager));
        if ([] !== $missing_keys) {
            throw new \Exception(sprintf('The pager misses following keys: %s', implode(', ', $missing_keys)));
        }

        return $pager;
    }

    /**
     * Checks that given JSON node pager has the expected total.
     *
     * @throws \Exception
     *
     * @Then the JSON node :node pager should have a total of :total
     */
    public function theJsonNodePagerShouldHaveTotal(string $node, int $total)
    {
        $pager = $this->theJsonNodeShouldHaveAPager($node);

        if ($pager['total'] !== $total) {
            throw new \Exception(sprintf('The node\'s pager have a total of "%s", expected "%s".', $pager['total'], $total));
        }
    }

    /**
     * Checks that given JSON node pager has the expected total.
     *
     * @throws \Exception
     *
     * @Then the JSON node :node pager should have a limit of :limit
     */
    public function theJsonNodePagerShouldHaveLimit(string $node, int $limit)
    {
        $pager = $this->theJsonNodeShouldHaveAPager($node);

        if ($pager['limit'] !== $limit) {
            throw new \Exception(sprintf('The node\'s pager have a total of "%s", expected "%s".', $pager['total'], $limit));
        }
    }
}
