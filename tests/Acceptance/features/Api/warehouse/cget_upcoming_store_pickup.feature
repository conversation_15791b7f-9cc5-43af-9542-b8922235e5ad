Feature: Get started store pickups on a warehouse
  In order to pickup a delivery note
  As a user
  I need to be able to list the store pickups started for a given warehouse

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/delivery_notes_for_start_store_pickup.sql"
    And   I send a "GET" request to "/api/v1/warehouse/1/started-store-pickups"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/warehouse/1/started-store-pickups"
    Then  the response status code should be 401

  Scenario: On non existing warehouse, returns an empty list
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/warehouse/99/started-store-pickups"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->store_pickups" should have 0 element
    And   the JSON node "data->_pager->total" should be equal to the number "0"

  Scenario: On warehouse without upcoming store pickups, returns an empty list
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/warehouse/18/started-store-pickups"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->store_pickups" should have 0 element
    And   the JSON node "data->_pager->total" should be equal to the number "0"

  Scenario: Get list in default case
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/warehouse/1/started-store-pickups"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->store_pickups" should have 1 element
    And   the JSON node "data->_pager->total" should be equal to the number "1"
    And   the JSON node "data->_pager->limit" should be equal to the number "100"
    # store pickups node content
    And   the JSON node "data->store_pickups[0]->delivery_note_id" should be equal to the number "456"
    And   the JSON node "data->store_pickups[0]->customer_order_id" should be equal to the number "1712826"
    And   the JSON node "data->store_pickups[0]->first_name" should be equal to "Raymond"
    And   the JSON node "data->store_pickups[0]->last_name" should be equal to "Cartier"
    And   the JSON node "data->store_pickups[0]->store_pickup_started_at" should be equal to the datetime "2020-03-21 15:15:00"
    And   the JSON node "data->store_pickups[0]->workflow_status" should be equal to "PREPARATION_FINISHED"
    And   the JSON node "data->store_pickups[0]->picked_by" should be equal to "gege"
    And   the JSON node "data->store_pickups[0]->picked_by_name" should be equal to "Gérard MANVUSSA"



