Feature: Check if a sticker is reprintable
  In order to generate a sticker
  As a logistician
  I need to check if said sticker by the applciation

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I send a "GET" request to "/api/v1/wms/sticker/4403190/is-handled"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/sticker/4403190/is-handled"
    Then  the response status code should be 401

  @clear-database
  Scenario: The sticker is not re-printable from the application
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-edn.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/sticker/4403190/is-handled"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data.is_handled" should be false

  @clear-database
  Scenario: The sticker is re-printable from the application
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-chronopost.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/sticker/4403191/is-handled"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data.is_handled" should be true
