Feature: Get list of move missions
  As a logistic manager
  I want to see the move missions and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/move_mission/move_missions.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/move-missions"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/move-missions"
    Then  the response status code should be 401

  Scenario: Get list of WMS move missions without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/move-missions"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->move_missions" should have 4 elements

  Scenario: Get list of WMS move missions without filtered on the mission_type_code
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/move-missions" with body:
    """
    {
      "where": {
        "mission_type_code": {
          "_eq": "dummy"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->move_missions" should have 1 elements
    And   the JSON node "data->move_missions[0]->move_mission_id" should be equal to the number "2"

  Scenario: Get list of WMS move missions without filtered on the user id the mission is assigned_to
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/move-missions" with body:
    """
    {
      "where": {
        "assigned_to": {
          "_eq": 1000
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->move_missions" should have 2 elements
    And   the JSON node "data->move_missions[0]->move_mission_id" should be equal to the number "2"
    And   the JSON node "data->move_missions[1]->move_mission_id" should be equal to the number "1"

  Scenario: Get list of WMS move missions without filtered on the warehouse_id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/move-missions" with body:
    """
    {
      "where": {
        "warehouse_id": {
          "_eq": 2
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->move_missions" should have 2 elements
    And   the JSON node "data->move_missions[0]->move_mission_id" should be equal to the number "3"
    And   the JSON node "data->move_missions[1]->move_mission_id" should be equal to the number "1"
