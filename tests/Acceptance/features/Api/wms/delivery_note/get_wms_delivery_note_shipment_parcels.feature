Feature: Fetch info on a delivery note parcels for its preparation in the WMS context

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note/get_wms_delivery_note_shipment_parcels.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/shipment-parcels"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/shipment-parcels"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/shipment-parcels"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with a non existing delivery note id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/666/shipment-parcels"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Le bon de livraison 666 n'existe pas
    """

  Scenario: Check successful response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/shipment-parcels"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->parcels" should have 1 element
    And   the JSON node "data->parcels[0]" should have the following keys and values
    """
    {
      "parcel_id": 455847,
      "parcel_number": 1,
      "parcel_tracking_number": null,
      "delivery_note_id": 4403190,
      "expedition_delivery_note_id": 391952,
      "articles": [
        {
          "parcel_product_id": 1,
          "parcel_id": 455847,
          "article_id": 81078,
          "sku": "ARCAMRBLINKNR",
          "name": "rBlink",
          "quantity": 1
        },
        {
          "parcel_product_id": 2,
          "parcel_id": 455847,
          "article_id": 81078,
          "sku": "ARCAMRBLINKNR",
          "name": "rBlink",
          "quantity": 1
        },
        {
          "parcel_product_id": 3,
          "parcel_id": 455847,
          "article_id": 143445,
          "sku": "FIIOBTR5NR",
          "name": "Performance Audio 40i (1 m)",
          "quantity": 2
        }
      ]
    }
    """
    And   the JSON node "data->parcels[0]->articles" should have 3 elements
    And   the JSON node "data->parcels[0]->articles[0]" should have the following keys and values
    """
    {
      "parcel_product_id": 1,
      "parcel_id": 455847,
      "article_id": 81078,
      "sku": "ARCAMRBLINKNR",
      "name": "rBlink",
      "quantity": 1
    }
    """
