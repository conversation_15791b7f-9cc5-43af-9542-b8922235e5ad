Feature: List inventories
  As a manager
  I want to see the inventories

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouses.sql"
    And   I load mysql fixtures from file "wms/warehouses.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/wms/warehouses"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/wms/warehouses"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v2/wms/warehouses"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Get warehouses (without filters)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/wms/warehouses"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "17"
    And   the JSON node "data->warehouses" should have 17 elements

  Scenario: Get only active warehouses
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/wms/warehouses" with body:
    """
    {
      "where": {
        "is_active_in_bo": {
          "_eq": true
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "16"
    And   the JSON node "data->warehouses" should have 16 elements
    # check content of the first warehouse
    And   the JSON node "data->warehouses[0]" should have the following keys and values
    """
    {
      "warehouse_id": 1,
      "name": "Champigny",
      "address": "314 rue du Pr Paul Milliez",
      "postal_code": "94506",
      "city": "Champigny sur Marne",
      "country_id": 67,
      "country_name": "FRANCE",
      "country_code": "FR",
      "phone_number": "0155091779",
      "email_address": "<EMAIL>",
      "manager_id": 1,
      "description": "D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.",
      "transfer_name": "Son-Vidéo.com",
      "basket_name": "centre logistique",
      "basket_address": "314 rue du Professeur Paul Milliez",
      "basket_city": "Champigny sur Marne",
      "google_map_link": "https://goo.gl/maps/PvjtEonYbaM2",
      "sort_order": 1,
      "opened": "ouvert du lundi au samedi, de 9h à 18h30",
      "opened_days": "du lundi au samedi",
      "opened_hours": "de 9h &agrave; 18h30",
      "is_active": true,
      "is_active_in_bo": true,
      "shorthand_name": "Cha",
      "use_auto_picking": true,
      "wms_code": "03",
      "allowed_inventory_types" : ["full"],
      "warehouse_barcode": "CHAMPIGNY-1",
      "location_id": null,
      "location_code": null,
      "location_label": null,
      "shipment_method_id": 31,
      "users": [],
      "business_hours": {
          "fri": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "mon": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "sat": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "sun": [],
          "thu": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "tue": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "wed": [
              [
                  "10:00",
                  "19:00"
              ]
          ]
      },
      "delivery_days": {
          "fri": true,
          "mon": false,
          "sat": true,
          "sun": false,
          "thu": true,
          "tue": true,
          "wed": true
      }
    }
    """
