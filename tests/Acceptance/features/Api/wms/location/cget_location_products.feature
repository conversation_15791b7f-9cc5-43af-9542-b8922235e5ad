Feature: Get location
  In order to manage a location through the API
  As a user
  I need to be able to retrieve information about a location

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "locations/location_products.sql"
    And   I send a "GET" request to "/api/v1/wms/location/1/products"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/location/1/products"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/location/1/products"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with not found location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/9999/products"
    And   the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Location not found."

  Scenario: Test on location without products
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/2/products"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location_products" should exist
    And   the JSON node "data->location_products" should have 0 element

  Scenario: Load information on a location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/1/products"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location_products" should exist
    And   the JSON node "data->location_products" should have 3 elements
    And   the JSON node "data->location_products[0]->product_id" should be equal to the number "81078"
    And   the JSON node "data->location_products[0]->sku" should be equal to "ARCAMRBLINKNR"
    And   the JSON node "data->location_products[0]->brand" should be equal to "Arcam"
    And   the JSON node "data->location_products[0]->model" should be equal to "rBlink"
    And   the JSON node "data->location_products[0]->package_number" should be equal to the number "2"
    And   the JSON node "data->location_products[0]->countable_manually" should be false
    And   the JSON node "data->location_products[0]->eans" should exist
    And   the JSON node "data->location_products[0]->eans" should have 1 element
    And   the JSON node "data->location_products[0]->eans[0]" should be equal to "5060133602194"
    And   the JSON node "data->location_products[0]->quantities" should exist
    And   the JSON node "data->location_products[0]->quantities" should have 3 elements

    # delivery ticket are sorted in ascending order
    And   the JSON node "data->location_products[0]->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->location_products[0]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->location_products[0]->quantities[0]->quantity" should be equal to "9"
    And   the JSON node "data->location_products[0]->quantities[0]->carrier_id" should be null

    And   the JSON node "data->location_products[0]->quantities[1]->delivery_ticket_id" should be null
    And   the JSON node "data->location_products[0]->quantities[1]->move_mission_id" should be equal to the number "1"
    And   the JSON node "data->location_products[0]->quantities[1]->quantity" should be equal to "1"
    And   the JSON node "data->location_products[0]->quantities[1]->carrier_id" should be null

    And   the JSON node "data->location_products[0]->quantities[2]->delivery_ticket_id" should be equal to the number "4263254"
    And   the JSON node "data->location_products[0]->quantities[2]->move_mission_id" should be null
    And   the JSON node "data->location_products[0]->quantities[2]->quantity" should be equal to "3"
    And   the JSON node "data->location_products[0]->quantities[2]->carrier_id" should be equal to the number "2"
    #  Product câble au mètre
    And   the JSON node "data->location_products[1]->product_id" should be equal to the number "143088"
    And   the JSON node "data->location_products[1]->sku" should be equal to "QEDQE6119"
    And   the JSON node "data->location_products[1]->brand" should be equal to "QED"
    And   the JSON node "data->location_products[1]->model" should be equal to "Performance Audio 40i (1 m)"
    And   the JSON node "data->location_products[1]->countable_manually" should be true
    And   the JSON node "data->location_products[1]->package_number" should be equal to the number "1"
    And   the JSON node "data->location_products[1]->eans" should exist
    And   the JSON node "data->location_products[1]->eans" should have 2 elements
    And   the JSON node "data->location_products[1]->eans[0]" should be equal to "5036694041987"
    And   the JSON node "data->location_products[1]->eans[1]" should be equal to "636926014106"
    And   the JSON node "data->location_products[1]->quantities" should exist
    And   the JSON node "data->location_products[1]->quantities" should have 1 elements
    And   the JSON node "data->location_products[1]->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->location_products[1]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->location_products[1]->quantities[0]->quantity" should be equal to "1"
    And   the JSON node "data->location_products[1]->quantities[0]->carrier_id" should be null
    #  Product vente par lot
    And   the JSON node "data->location_products[2]->product_id" should be equal to the number "143169"
    And   the JSON node "data->location_products[2]->sku" should be equal to "SVCATAPREMHIVER"
    And   the JSON node "data->location_products[2]->brand" should be equal to "SVD Boutique"
    And   the JSON node "data->location_products[2]->model" should be equal to "Catalogue 2020"
    And   the JSON node "data->location_products[2]->package_number" should be equal to the number "1"
    And   the JSON node "data->location_products[2]->countable_manually" should be true
    And   the JSON node "data->location_products[2]->eans" should exist
    And   the JSON node "data->location_products[2]->eans" should have 0 element
    And   the JSON node "data->location_products[2]->quantities" should exist
    And   the JSON node "data->location_products[2]->quantities" should have 1 elements
    And   the JSON node "data->location_products[2]->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->location_products[2]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->location_products[2]->quantities[0]->quantity" should be equal to "1"
    And   the JSON node "data->location_products[2]->quantities[0]->carrier_id" should be null
