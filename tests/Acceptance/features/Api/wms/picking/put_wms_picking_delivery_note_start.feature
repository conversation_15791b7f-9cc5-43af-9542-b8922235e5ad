Feature: As a logistician, I need to mark a picking as started on successfully scanned BL

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/picking/put_wms_picking_delivery_note_start.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/picking/4403190/start"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/picking/4403190/start"
    Then  the response status code should be 401

  Scenario: Test with a non existing delivery note id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/wms/picking/666/start"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Le bon de livraison 666 n'existe pas
    """

  Scenario: Test with token linked to an account which do not have any specific permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/wms/picking/4403190/start"
    # No content
    Then  the response status code should be 204
