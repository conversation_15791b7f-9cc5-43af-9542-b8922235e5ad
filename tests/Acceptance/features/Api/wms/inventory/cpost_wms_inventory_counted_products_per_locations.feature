Feature: Get list of inventory products
  As a logistician
  I want to see the users and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_counted_products_per_locations.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/counted-products-per-locations"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/counted-products-per-locations"
    Then  the response status code should be 401

  Scenario: Get list of counted products in an inventory without filters
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/counted-products-per-locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "5"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->products" should have 5 elements

  Scenario: Get list of counted products in an inventory with a filter on some product ids
    # Inventory requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/counted-products-per-locations" with body:
    """
    {
      "where": {
        "product_id": {
          "_in": [81079, 81080]
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->products" should have 3 elements
    And   the JSON node "data->products[0]" should have the following keys and values
    """
{
  "product_id": 81079,
  "sku": "ARCAMRBLINKNR-MK2",
  "short_description": "Arcam rBlink",
  "counted_by": 1000,
  "counted_by_name": "backoffice",
  "location_id": 1,
  "counted_quantity": 5,
  "expected_quantity": 0,
  "counted_at": null,
  "inventory_collect_id": 3,
  "inventory_collect_number": 1,
  "inventory_id": 2,
  "location_label": "03.01.A$01.01.01",
  "location_code": "03.01.a.01.01.01",
  "is_locked": 0
}
    """
