Feature: Get location by code
  In order to manage a location through the API
  As a logistician
  I need to be able to retrieve information about a location by its code

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/location_by_code.sql"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/location/by-code/03.01.a.01.01.01"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/location/by-code/03.01.a.01.01.01"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/location/by-code/03.01.a.01.01.01"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with not found location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/location/by-code/9999"
    And   the response should be in JSON
    And   the JSON node "code" should be equal to the number "404"
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Location not found."

  Scenario: Test with a location outside a partial inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/location/by-code/03.04.a.02.01.01"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "code" should be equal to the number "1018"
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "This location is not part of this inventory."
    And   the JSON node "data" should have the following keys and values
    """
    {
      "location_label": "03.04.TEST$02.01.01",
      "inventory_id": 3
    }
    """

  Scenario: Load information on a location in a wrong inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/location/by-code/03.01.a.01.01.02"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->location_id" should be equal to the number "2"
    And   the JSON node "data->location->code" should be equal to "03.01.a.01.01.02"
    And   the JSON node "data->location->_meta" should have 3 element
    And   the JSON node "data->location->_meta->is_locked_by" should be null
    And   the JSON node "data->location->_meta->is_locked" should be false
    And   the JSON node "data->location->_meta->can_be_set_empty" should be false


  Scenario: Load information on a location in an ongoing inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/2/location/by-code/03.01.a.01.01.02"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->location_id" should be equal to the number "2"
    And   the JSON node "data->location->code" should be equal to "03.01.a.01.01.02"
    And   the JSON node "data->location->_meta" should have 3 element
    And   the JSON node "data->location->_meta->is_locked_by" should be equal to "backoffice"
    And   the JSON node "data->location->_meta->is_locked" should be true
    And   the JSON node "data->location->_meta->can_be_set_empty" should be false

  Scenario: Test with a location inside a partial inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/location/by-code/03.01.a.01.01.02"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->location_id" should be equal to the number "2"
    And   the JSON node "data->location->code" should be equal to "03.01.a.01.01.02"
    And   the JSON node "data->location->_meta" should have 3 element
    And   the JSON node "data->location->_meta->is_locked_by" should be null
    And   the JSON node "data->location->_meta->is_locked" should be false
    And   the JSON node "data->location->_meta->can_be_set_empty" should be false

  Scenario: Test with a previous location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/location/by-code/03.01.a.01.01.01"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->_meta" should have 3 element
    And   the JSON node "data->location->_meta->is_locked_by" should be null
    And   the JSON node "data->location->_meta->is_locked" should be false
    And   the JSON node "data->location->_meta->can_be_set_empty" should be true

  Scenario: Test with a previous location already scanned as empty
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/location/by-code/03.01.a.01.01.02"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->_meta->is_locked_by" should be null
    And   the JSON node "data->location->_meta->is_locked" should be false
    And   the JSON node "data->location->_meta->can_be_set_empty" should be false

