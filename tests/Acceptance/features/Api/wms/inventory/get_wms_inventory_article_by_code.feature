Feature: Retrieve an article by a given code
  In order to obtain some information about an article through the API
  As a logistician
  I need to be able to identify an article using an arbitrary code

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/article_by_code.sql"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/article/by-code/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/article/by-code/123"
    Then  the response status code should be 401

  Scenario: Test with non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/article/by-code/666"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with code "666".
    """

  Scenario: Test with an existing article on a wrong inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/article/by-code/11230081123"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article" should exist
    And   the JSON node "data->article->product_id" should be equal to "81123"
    And   the JSON node "data->article->_meta" should have 0 element

  Scenario: Test with an existing article on an ongoing inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/2/article/by-code/11230081123"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article" should exist
    And   the JSON node "data->article->product_id" should be equal to "81123"
    And   the JSON node "data->article->_meta" should have 3 elements
