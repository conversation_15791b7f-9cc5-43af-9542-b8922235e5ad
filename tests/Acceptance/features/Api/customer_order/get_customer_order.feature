Feature: get customer order
  In order to manage a customer order through the API
  As a user
  I need to be able to retrieve information about a customer order

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I send a "GET" request to "/api/v1/customer-order/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/customer-order/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 1, "cost": 0}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/1"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Load information on a customer order
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 1, "cost": 9.9}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" with ignored columns "last_modified_at" should be identical to
    """
    {
      "id_commande": 1,
      "no_commande_origine": "1",
      "creation_origine": "son-video.com",
      "date_creation": "1990-01-01 00:00:00",
      "id_boutique": null,
      "vendeur": null,
      "flux": "traitement",
      "V_statut_traitement": "lvr_attente,facturation",
      "en_attente_de_livraison": true,
      "facturation_mode": "bl",
      "ip": "",
      "ip_pays": null,
      "validite_fianet": null,
      "rappel_client": "N",
      "emport_depot": "N",
      "lvr_particulier": "Y",
      "lvr_assurance": "N",
      "detaxe_export": "non",
      "cmd_intragroupe": false,
      "id_prospect": 1,
      "id_devis": null,
      "quote_id": null,
      "commentaire_facture": "test",
      "expedition_diff_date": null,
      "cloture_date": null,
      "cloture_usr": null,
      "tradedoubler_id": null,
      "nombre_visite": 0,
      "clef": null,
      "date_export_status": "1990-01-01",
      "compteur_paiement": 2,
      "cnt_fct_type": "particulier",
      "cnt_fct_email": "<EMAIL>",
      "cnt_fct_societe": "",
      "cnt_fct_civilite": "M.",
      "cnt_fct_nom": "",
      "cnt_fct_prenom": "",
      "cnt_fct_adresse": "tmp",
      "cnt_fct_code_postal": "",
      "cnt_fct_ville": "",
      "cnt_fct_id_pays": 67,
      "cnt_fct_telephone": "",
      "cnt_fct_telephone_bureau": "",
      "cnt_fct_mobile": "",
      "cnt_fct_fax": "",
      "cnt_fct_numero_tva": null,
      "cnt_fct_no_tva_validite": null,
      "cnt_lvr_type": "particulier",
      "cnt_lvr_email": "<EMAIL>",
      "cnt_lvr_societe": "",
      "cnt_lvr_civilite": "M.",
      "cnt_lvr_nom": "",
      "cnt_lvr_prenom": "",
      "cnt_lvr_adresse": "tmp",
      "cnt_lvr_code_postal": "",
      "cnt_lvr_ville": "",
      "cnt_lvr_id_pays": 67,
      "cnt_lvr_telephone": "",
      "cnt_lvr_telephone_bureau": "",
      "cnt_lvr_mobile": "",
      "cnt_lvr_fax": "",
      "cnt_lvr_numero_tva": null,
      "V_montant_ttc": 249,
      "V_montant_ht": 207.5,
      "V_trcns_montant": 249,
      "V_trcns_montant_accepte": 249,
      "V_trcns_montant_remise": 249,
      "V_pmts_montant": 249,
      "V_pmts_montant_accepte": 249,
      "V_pmts_montant_remise": 249,
      "V_rmbts_montant": 0,
      "V_rmbts_montant_accepte": 0,
      "V_rmbts_montant_remise": 0,
      "V_date_lvr_prevue_max": null,
      "email_confirmation": null,
      "sms_confirmation": null,
      "promotion_id": null,
      "entete_svd": true,
      "emport_depot_paris": false,
      "rdv_socol": false,
      "id_transporteur": 2,
      "id_pdt_transporteur": 1,
      "tpt_option_code": null,
      "atradius": false,
      "relance_compta": false,
      "id_commande_mere": null,
      "refacturation": false,
      "depot_emport": null,
      "sms_emport": null,
      "last_modified_at": "2022-10-17 10:12:28",
      "relay_id": null,
      "sales_channel_id": 1,
      "has_inconsistent_carrier": false,
      "has_ongoing_premium_warranty": false,
      "is_paid": true,
      "status": "ongoing",
      "warehouse_id": 5,
      "store_label": "Nantes",
      "order_lines": [],
      "payments": [],
      "delivery_notes": [
        {
          "delivery_note_id": 456,
          "status": null,
          "warehouse_id": null,
          "customer_order_id": null,
          "transfer_id": null,
          "transfer_status": null,
          "transfer_destination_warehouse_id": null,
          "invoice_id": null,
          "creation_date": null,
          "prepared_at": null,
          "carrier_id": null,
          "carrier_product_id": null,
          "is_prepared_in_expedition": null,
          "is_pickup": null,
          "tracking_id": null,
          "store_pickup_started_at": null,
          "picked_by": null,
          "picked_by_name": null,
          "workflow_status": null,
          "delivery_note_products": []
        }
      ],
      "is_amazon_business": false,
      "is_duty_free": false,
      "source_group": null,
      "source": null,
      "tags": []
    }
    """

  Scenario: Load information on a customer order with an inconsistent shipment method id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 11, "cost": 9.9},
  {"shipment_method_id": 22, "cost": 9.9}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" with ignored columns "last_modified_at" should be identical to
    """
    {
      "id_commande": 1,
      "no_commande_origine": "1",
      "creation_origine": "son-video.com",
      "date_creation": "1990-01-01 00:00:00",
      "id_boutique": null,
      "vendeur": null,
      "flux": "traitement",
      "V_statut_traitement": "lvr_attente,facturation",
      "en_attente_de_livraison": true,
      "facturation_mode": "bl",
      "ip": "",
      "ip_pays": null,
      "validite_fianet": null,
      "rappel_client": "N",
      "emport_depot": "N",
      "lvr_particulier": "Y",
      "lvr_assurance": "N",
      "detaxe_export": "non",
      "cmd_intragroupe": false,
      "id_prospect": 1,
      "id_devis": null,
      "quote_id": null,
      "commentaire_facture": "test",
      "expedition_diff_date": null,
      "cloture_date": null,
      "cloture_usr": null,
      "tradedoubler_id": null,
      "nombre_visite": 0,
      "clef": null,
      "date_export_status": "1990-01-01",
      "compteur_paiement": 2,
      "cnt_fct_type": "particulier",
      "cnt_fct_email": "<EMAIL>",
      "cnt_fct_societe": "",
      "cnt_fct_civilite": "M.",
      "cnt_fct_nom": "",
      "cnt_fct_prenom": "",
      "cnt_fct_adresse": "tmp",
      "cnt_fct_code_postal": "",
      "cnt_fct_ville": "",
      "cnt_fct_id_pays": 67,
      "cnt_fct_telephone": "",
      "cnt_fct_telephone_bureau": "",
      "cnt_fct_mobile": "",
      "cnt_fct_fax": "",
      "cnt_fct_numero_tva": null,
      "cnt_fct_no_tva_validite": null,
      "cnt_lvr_type": "particulier",
      "cnt_lvr_email": "<EMAIL>",
      "cnt_lvr_societe": "",
      "cnt_lvr_civilite": "M.",
      "cnt_lvr_nom": "",
      "cnt_lvr_prenom": "",
      "cnt_lvr_adresse": "tmp",
      "cnt_lvr_code_postal": "",
      "cnt_lvr_ville": "",
      "cnt_lvr_id_pays": 67,
      "cnt_lvr_telephone": "",
      "cnt_lvr_telephone_bureau": "",
      "cnt_lvr_mobile": "",
      "cnt_lvr_fax": "",
      "cnt_lvr_numero_tva": null,
      "V_montant_ttc": 249,
      "V_montant_ht": 207.5,
      "V_trcns_montant": 249,
      "V_trcns_montant_accepte": 249,
      "V_trcns_montant_remise": 249,
      "V_pmts_montant": 249,
      "V_pmts_montant_accepte": 249,
      "V_pmts_montant_remise": 249,
      "V_rmbts_montant": 0,
      "V_rmbts_montant_accepte": 0,
      "V_rmbts_montant_remise": 0,
      "V_date_lvr_prevue_max": null,
      "email_confirmation": null,
      "sms_confirmation": null,
      "promotion_id": null,
      "entete_svd": true,
      "emport_depot_paris": false,
      "rdv_socol": false,
      "id_transporteur": 2,
      "id_pdt_transporteur": 1,
      "tpt_option_code": null,
      "atradius": false,
      "relance_compta": false,
      "id_commande_mere": null,
      "refacturation": false,
      "depot_emport": null,
      "sms_emport": null,
      "last_modified_at": "2022-10-17 14:10:09",
      "relay_id": null,
      "sales_channel_id": 1,
      "has_inconsistent_carrier": true,
      "has_ongoing_premium_warranty": false,
      "is_paid": true,
      "status": "ongoing",
      "warehouse_id": 5,
      "store_label": "Nantes",
      "order_lines": [],
      "payments": [],
      "delivery_notes": [
        {
          "delivery_note_id": 456,
          "status": null,
          "warehouse_id": null,
          "customer_order_id": null,
          "transfer_id": null,
          "transfer_status": null,
          "transfer_destination_warehouse_id": null,
          "invoice_id": null,
          "creation_date": null,
          "prepared_at": null,
          "carrier_id": null,
          "carrier_product_id": null,
          "is_prepared_in_expedition": null,
          "is_pickup": null,
          "tracking_id": null,
          "store_pickup_started_at": null,
          "picked_by": null,
          "picked_by_name": null,
          "workflow_status": null,
          "delivery_note_products": []
        }
      ],
      "is_amazon_business": false,
      "is_duty_free": false,
      "source_group": null,
      "source": null,
      "tags": []
    }
    """

  Scenario: Load information on an amazon business and duty free customer order
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 1, "cost": 9.9}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/3"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" with ignored columns "last_modified_at" should be identical to
    """
    {
      "id_commande": 3,
      "no_commande_origine": "3",
      "creation_origine": "amazon.de",
      "date_creation": "1990-01-01 00:00:00",
      "id_boutique": null,
      "vendeur": null,
      "flux": "traitement",
      "V_statut_traitement": null,
      "en_attente_de_livraison": false,
      "facturation_mode": "bl",
      "ip": "",
      "ip_pays": null,
      "validite_fianet": null,
      "rappel_client": "N",
      "emport_depot": "N",
      "lvr_particulier": "Y",
      "lvr_assurance": "N",
      "detaxe_export": "non",
      "cmd_intragroupe": false,
      "id_prospect": 1,
      "id_devis": null,
      "quote_id": null,
      "commentaire_facture": "test",
      "expedition_diff_date": null,
      "cloture_date": null,
      "cloture_usr": null,
      "tradedoubler_id": null,
      "nombre_visite": 0,
      "clef": null,
      "date_export_status": "1990-01-01",
      "compteur_paiement": 2,
      "cnt_fct_type": "particulier",
      "cnt_fct_email": "<EMAIL>",
      "cnt_fct_societe": "",
      "cnt_fct_civilite": "M.",
      "cnt_fct_nom": "",
      "cnt_fct_prenom": "",
      "cnt_fct_adresse": "tmp",
      "cnt_fct_code_postal": "",
      "cnt_fct_ville": "",
      "cnt_fct_id_pays": 67,
      "cnt_fct_telephone": "",
      "cnt_fct_telephone_bureau": "",
      "cnt_fct_mobile": "",
      "cnt_fct_fax": "",
      "cnt_fct_numero_tva": null,
      "cnt_fct_no_tva_validite": null,
      "cnt_lvr_type": "particulier",
      "cnt_lvr_email": "<EMAIL>",
      "cnt_lvr_societe": "",
      "cnt_lvr_civilite": "M.",
      "cnt_lvr_nom": "",
      "cnt_lvr_prenom": "",
      "cnt_lvr_adresse": "tmp",
      "cnt_lvr_code_postal": "",
      "cnt_lvr_ville": "",
      "cnt_lvr_id_pays": 67,
      "cnt_lvr_telephone": "",
      "cnt_lvr_telephone_bureau": "",
      "cnt_lvr_mobile": "",
      "cnt_lvr_fax": "",
      "cnt_lvr_numero_tva": null,
      "V_montant_ttc": 0,
      "V_montant_ht": 0,
      "V_trcns_montant": 0,
      "V_trcns_montant_accepte": 0,
      "V_trcns_montant_remise": 0,
      "V_pmts_montant": 0,
      "V_pmts_montant_accepte": 0,
      "V_pmts_montant_remise": 0,
      "V_rmbts_montant": 0,
      "V_rmbts_montant_accepte": 0,
      "V_rmbts_montant_remise": 0,
      "V_date_lvr_prevue_max": null,
      "email_confirmation": null,
      "sms_confirmation": null,
      "promotion_id": null,
      "entete_svd": true,
      "emport_depot_paris": false,
      "rdv_socol": false,
      "id_transporteur": 7,
      "id_pdt_transporteur": 69,
      "tpt_option_code": null,
      "atradius": false,
      "relance_compta": false,
      "id_commande_mere": null,
      "refacturation": false,
      "depot_emport": null,
      "sms_emport": null,
      "last_modified_at": "2022-10-17 14:12:21",
      "relay_id": null,
      "sales_channel_id": 7,
      "has_inconsistent_carrier": false,
      "has_ongoing_premium_warranty": false,
      "is_paid": false,
      "status": "ongoing",
      "warehouse_id": null,
      "store_label": null,
      "order_lines": [],
      "payments": [],
      "delivery_notes": [],
      "is_amazon_business": true,
      "is_duty_free": true,
      "source_group": null,
      "source": null,
      "tags": [
          {
            "label": "Amazon Business",
            "meta": null,
            "name": "amazon_business"
          }
      ]
    }
    """
