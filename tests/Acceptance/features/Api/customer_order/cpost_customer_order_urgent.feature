Feature: Get list of urgent order
  As someone identified in the ERP
  I want to see the users and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "customer_order/cpost_customer_order_urgent.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/urgent"
    Then  the response status code should be 401

  Scenario: Get list of urgent customer order without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/urgent"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->urgent" should have 2 elements

  Scenario: Get list of urgent customer order filtered by order type
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/urgent" with body:
    """
    {
      "where": {
        "order_type": {
          "_eq": "Amazon"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->urgent" should have 1 elements
    And   the JSON node "data->urgent[0]->order_id" should be equal to "3"
    And   the JSON node "data->urgent[0]->carrier" should be equal to "GLS"
