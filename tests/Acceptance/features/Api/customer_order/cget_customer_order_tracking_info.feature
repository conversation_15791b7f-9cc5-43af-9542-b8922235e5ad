Feature: get list of warehouses
  In ordering to manage warehouses through API
  As a user
  I need to be able to list warehouses

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order/cget_customer_order_tracking_info.sql"
    And   I send a "GET" request to "/api/v1/customer-order/1878297/tracking-infos"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/customer-order/1878297/tracking-infos"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/customer-order/1878297/tracking-infos"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->tracking_infos" should have 5 elements
    And   the JSON node "data->tracking_infos[0]->tracking_url" should be equal to "http://www.dhl.fr/content/fr/fr/dhl_express/suivi_expedition.shtml?brand=DHL&AWB=A"
    And   the JSON node "data->tracking_infos[0]->tracking_number" should be equal to "A"
    And   the JSON node "data->tracking_infos[0]->fullfillment_date" should exist
    And   the JSON node "data->tracking_infos[0]->carrier_name" should be equal to "DHL"

  Scenario: Test with not found customer order id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer-order/6666/tracking-infos"
    And   the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Tracking infos not fount with customer order id 6666."

  Scenario: Test with not found tracking info
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer-order/1878298/tracking-infos"
    And   the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Tracking infos not fount with customer order id 1878298."

  Scenario: Test with not found if have receipt number
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer-order/1878299/tracking-infos"
    And   the JSON node "code" should be equal to the number "404"
