Feature: Retrieve a the number of delivery notes that can be print again
  As a user
  I need to be able to retrieve the number of delivery notes that can be print again

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "logistic/delivery_note_reset_printing.sql"
    And   I send a "GET" request to "/api/v1/nb_to_reset_printing"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/nb_to_reset_printing"
    Then  the response status code should be 401

  Scenario: Test get number of printing that can be reset
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/nb_to_reset_printing"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be equal to "3"