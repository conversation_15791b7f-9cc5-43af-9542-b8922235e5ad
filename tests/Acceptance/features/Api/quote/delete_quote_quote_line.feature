Feature: Delete a quote line

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/delete_quote_line.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/3"
    Then  the response status code should be 403

  Scenario: Test with token linked to an account which permission for product delete
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/3"
    Then  the response status code should be 204

  Scenario: Test with token linked to an account with permission for section delete
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/5"
    Then  the response status code should be 204

  Scenario: Test failed, quote does not exist
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/111111/quote-line/12"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string
    """
    Quote not found with id "111111"
    """

  Scenario: Test failed, quote line does not exist
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/100"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string
    """
    Quote line not found with id "100"
    """

  Scenario: Test failed, Mismatch between quote and quote line.
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/12/quote-line/1"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string
    """
    Mismatch between quote and quote line
    """

  Scenario: Test failed, quote of type quotation has already been sent
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/10/quote-line/1"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "message" should contain "Quote is locked"

  Scenario: Test success, quote of type offer has already been sent
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/quote/11/quote-line/2"
    Then  the response status code should be 204
