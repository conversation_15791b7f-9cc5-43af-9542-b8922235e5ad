Feature: Get list of delivery notes
  As someone identified in the ERP
  I want to see the delivery notes and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/delivery_note.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/delivery-notes"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/delivery-notes"
    Then  the response status code should be 401

  Scenario: Get list of delivery notes without filters
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/delivery-notes"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->delivery_notes" should have 3 elements

  Scenario: Get list of delivery notes filtered
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/delivery-notes" with body:
    """
    {
      "where": {
        "status": {
          "_eq": "au depart"
        },
        "customer_order_id": {
          "_eq": "1712826"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->delivery_notes" should have 2 elements
    And   the JSON node "data->delivery_notes[1]" should have the following keys and values
    """
    {
      "delivery_note_id": 456,
      "status": "au depart",
      "warehouse_id": 1,
      "customer_order_id": 1712826,
      "transfer_id": null,
      "transfer_status": null,
      "transfer_destination_warehouse_id": null,
      "invoice_id": null,
      "creation_date": "2019-08-29 04:05:37",
      "prepared_at": "2019-08-29 11:41:10",
      "carrier_id": 2,
      "carrier_product_id": 1,
      "is_prepared_in_expedition": false,
      "is_pickup": false,
      "tracking_id": null,
      "store_pickup_started_at": null,
      "picked_by": null,
      "picked_by_name": "",
      "workflow_status": "PREPARATION_FINISHED",
      "delivery_note_products": []
    }
    """

  Scenario: Get list of delivery notes filtered with twice the same column
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/delivery-notes" with body:
    """
    {
      "where": {
        "_or": [
          {
            "status": { "_eq": "au depart" }
          },
          {
            "status": { "_eq": "expedie" }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->delivery_notes" should have 3 elements

  Scenario: Get list of delivery notes with product list
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/delivery-notes" with body:
    """
    {
      "where": {
        "status": {
          "_eq": "au depart"
        },
        "customer_order_id": {
          "_eq": "1712826"
        }
      },
      "included_dependencies": ["delivery_note_products"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->delivery_notes" should have 2 elements
    And   the JSON node "data->delivery_notes[1]" should have the following keys and values
    """
    {
      "delivery_note_id": 456,
      "status": "au depart",
      "warehouse_id": 1,
      "customer_order_id": 1712826,
      "transfer_id": null,
      "transfer_status": null,
      "transfer_destination_warehouse_id": null,
      "invoice_id": null,
      "creation_date": "2019-08-29 04:05:37",
      "prepared_at": "2019-08-29 11:41:10",
      "carrier_id": 2,
      "carrier_product_id": 1,
      "is_prepared_in_expedition": false,
      "is_pickup": false,
      "tracking_id": null,
      "store_pickup_started_at": null,
      "picked_by": null,
      "picked_by_name": "",
      "workflow_status": "PREPARATION_FINISHED",
      "delivery_note_products": []
    }
    """
    And   the JSON node "data->delivery_notes[1]->delivery_note_products[0]" should have the following keys and values
    """
    {
      "product_id": 70520,
      "sku": "YAMEPH100SI",
      "type": "article",
      "description": "C\u00e2ble d'enceinte QED Performance Audio 40i, longueur 1 m",
      "marque": "Arcam",
      "modele": "EPH-100",
      "quantity": 1,
      "package_number": 1,
      "countable_manually": false,
      "is_virtual_product": false,
      "is_auto_picked": false,
      "code_128": "15200070520"
    }
    """
