Feature: Retrieve delivery note activity
  In order to obtain some information about a delivery note through the API
  As a user
  I need to be able to retrieve delivery note activities by its id

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/delivery_note.sql"
    And   I send a "GET" request to "/api/v1/delivery-note/123/activity"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/delivery-note/123/activity"
    Then  the response status code should be 401

  Scenario: Test with non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/delivery-note/666/activity"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Found no activity for delivery ticket with id 666
    """

  Scenario: Test with an existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/delivery-note/456/activity"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note_activity" should exist
    # First line is generated by the trigger
    And   the JSON node "data->delivery_note_activity[0]->delivery_ticket_id" should be equal to "456"
    And   the JSON node "data->delivery_note_activity[0]->delivery_ticket_id_without_fk" should be equal to "456"
    And   the JSON node "data->delivery_note_activity[0]->action" should be equal to "CREATED"
    And   the JSON node "data->delivery_note_activity[0]->created_by" should be equal to "1000"
    And   the JSON node "data->delivery_note_activity[0]->created_by_name" should be equal to "backoffice"
    # Second line from fixtures
    And   the JSON node "data->delivery_note_activity[1]->delivery_ticket_id" should be equal to "456"
    And   the JSON node "data->delivery_note_activity[1]->delivery_ticket_id_without_fk" should be equal to "456"
    And   the JSON node "data->delivery_note_activity[1]->action" should be equal to "VALIDATED"
    And   the JSON node "data->delivery_note_activity[1]->created_by" should be equal to "2"
    And   the JSON node "data->delivery_note_activity[1]->created_by_name" should be equal to "Gérard MANVUSSA"
