Feature: Start the store pickup of a delivery note
  In order to pickup a delivery note
  As a user
  I need to be able to mark the store pickup as started

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/delivery_notes_for_start_store_pickup.sql"
    And   I send a "PUT" request to "/api/v1/delivery-note/123/start-store-pickup"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/delivery-note/123/start-store-pickup"
    Then  the response status code should be 401

  Scenario: Test with non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/delivery-note/666/start-store-pickup"
    Then  the response status code should be 404
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Delivery note not found.
    """

  Scenario: Test with delivery note that is not a pickup
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/delivery-note/789/start-store-pickup"
    Then  the response status code should be 400
    Then  the JSON node "code" should be equal to the number "1011"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Delivery note is not a pickup
    """
    And   the JSON node "data->delivery_note_id" should be equal to the number "789"

  Scenario: Test with a pickup delivery note NOT already started
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/delivery-note/123/start-store-pickup"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->store_pickup_started_at->date" should be nearly equal to now

  Scenario: Test with a delivery note already started => should not update the date
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/delivery-note/456/start-store-pickup"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->store_pickup_started_at->date" should be equal to the datetime "2020-03-21 15:15:00"


