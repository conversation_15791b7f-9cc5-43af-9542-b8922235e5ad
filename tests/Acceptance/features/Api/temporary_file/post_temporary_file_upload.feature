Feature: Upload a temporary file
  In order to do some operations on an uploaded file that does not need to be persisted for ever
  As a user
  I need to be able to upload a temporary file

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And I send a POST request to "/api/v1/temporary-file/upload" with parameters:
      | key    | value                 |
      | file   | @../../fixtures/files/temporary_file_upload.csv |
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And I send a POST request to "/api/v1/temporary-file/upload" with parameters:
      | key    | value                 |
      | file   | @../../fixtures/files/temporary_file_upload.csv |
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And I send a POST request to "/api/v1/temporary-file/upload" with parameters:
      | key    | value                 |
      | file   | @../../fixtures/files/temporary_file_upload.csv |
    Then  the response status code should be 403

  Scenario: Upload a temporary file successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I send a POST request to "/api/v1/temporary-file/upload" with parameters:
      | key    | value                 |
      | file   | @../../fixtures/files/temporary_file_upload.csv |
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->target_path" should contain "-file.csv"

