Feature: Retrieve a list of transfer reports
  As someone identified in the ERP
  I want to fetch the list of transfer reports

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "product_stock.sql"
    And   I load mysql fixtures from file "transfer/transfer.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/transfer-reports"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/transfer-reports"
    Then  the response status code should be 401

  Scenario: Get list of stalled transfers
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/transfer-reports"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->reports" should have 2 elements
    And   the JSON node "data->reports" should have the following keys and values
      """
      [
        {
          "transfers": [
            {
              "weight": 0.85,
              "transfer_id": 6666666
            }
          ],
          "carrier_product_id": 1,
          "carrier_product_label": "Express",
          "target_id": 5,
          "target_name": "Nantes",
          "sent_from_id": 1,
          "sent_from_name": "Champigny",
          "tracking_number": 90000001,
          "parcels": 10
        },
        {
          "transfers": [
            {
              "weight": 3.4,
              "transfer_id": 7777777
            }
          ],
          "carrier_product_id": 1,
          "carrier_product_label": "Express",
          "target_id": 5,
          "target_name": "Nantes",
          "sent_from_id": 1,
          "sent_from_name": "Champigny",
          "tracking_number": 90000002,
          "parcels": 1503
        }
      ]
      """
