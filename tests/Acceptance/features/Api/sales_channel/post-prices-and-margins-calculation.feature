Feature: Compute the margin of an article for a given list of sales channels that can have different
  selling prices and commission structure.

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku_v2.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation"
    Then  the response status code should be 401

  Scenario: Api requires a permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation"
    Then  the response status code should be 403

  Scenario: Test empty request to margin check
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation"
    Then  the response status code should be 400

  Scenario: Test empty list of sales request to margin check
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      []
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 0 elements

  Scenario: Test single sales channel item request to margin check
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":1,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":0,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":142.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id":1,
    "selling_price_tax_excluded":231.66666666666669,
    "selling_price_tax_included": 278,
    "sales_channel_commission":0,
    "margin": 87.09000000000003,
    "margin_rate": 0.37865217391304357,
    "markup_rate": 0.609404520327479,
    "errors":null
   }
  """

  Scenario: Test single sales channel item with empty stock
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":1,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":0,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":0,
      "unconditional_discount":0,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id":1,
    "selling_price_tax_excluded":231.66666666666669,
    "selling_price_tax_included": 278,
    "sales_channel_commission":0,
    "margin": 89.09000000000003,
    "margin_rate": 0.3873478260869566,
    "markup_rate": 0.6322475338868784,
    "errors":null
   }
  """

  Scenario: Test single sales channel item with commission fee
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":5,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":10,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id": 5,
    "selling_price_tax_excluded": 231.66666666666669,
    "selling_price_tax_included": 278,
    "sales_channel_commission": 23.16666666666667,
    "margin": 63.923333333333375,
    "margin_rate": 0.2779275362318842,
    "markup_rate": 0.44729783313507365,
    "errors":null
   }
  """

  Scenario: Test single sales channel item with unconditional discount
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":5,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":10,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":10,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id": 5,
    "selling_price_tax_excluded":231.66666666666669,
    "selling_price_tax_included": 278,
    "sales_channel_commission": 23.16666666666667,
    "margin": 78.01433333333338,
    "margin_rate": 0.33919275362318857,
    "markup_rate": 0.6056120085805152,
    "errors":null
   }
  """

  Scenario: Test single sales channel item with promo budget
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":5,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":10,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":10,
      "promo_budget_amount":20
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id": 5,
    "selling_price_tax_excluded": 231.66666666666669,
    "selling_price_tax_included": 278,
    "sales_channel_commission": 23.16666666666667,
    "margin": 98.01433333333338,
    "margin_rate": 0.426149275362319,
    "markup_rate": 0.9007097412522941,
    "errors": null
   }
  """

  Scenario: Test single sales channel item request triggering margin error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":1,
      "selling_price":140,
      "pvgc":279,
      "sales_channel_commission_fee":0,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":142.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id":1,
    "selling_price_tax_excluded":116.66666666666667,
    "selling_price_tax_included":140,
    "sales_channel_commission":0,
    "margin": -27.909999999999997,
    "margin_rate": -0.242695652173913,
    "markup_rate": -0.19529773983626056,
    "errors": [
       {
         "code": "selling_price_margin_is_too_low",
         "message": "Product margin is too low"
       },
       {
         "code": "selling_price_margin_rate_is_too_low",
         "message": "Margin rate should not be lower than 0%"
       }
     ]
   }
  """

  Scenario: Test single sales channel item margin check causing pvgc error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":1,
      "selling_price":280,
      "pvgc":279,
      "sales_channel_commission_fee":0,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":142.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "selling_price_tax_included":140,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 1 elements
    And the JSON node "data[0]" should be identical to
  """
  {
    "sales_channel_id":1,
    "selling_price_tax_excluded":233.33333333333334,
    "selling_price_tax_included":280,
    "sales_channel_commission":0,
    "margin": 88.75666666666669,
    "margin_rate": 0.3831223021582734,
    "markup_rate": 0.621066871924055,
    "errors":[
       {
         "code": "selling_price_is_greater_than_pvgc",
         "message": "Selling price is higher than the GOSP"
       }
     ]
   }

  """

  Scenario: Test multi sales channel item
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel/prices-and-margins-calculation" with body:
    """
      [{
      "article_id":81123,
      "sales_channel_id":1,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":0,
      "ecotax":2,
      "sorecop":1,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      },
      {
      "article_id":81123,
      "sales_channel_id":5,
      "selling_price":278,
      "pvgc":279,
      "sales_channel_commission_fee":10,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      },
      {
      "article_id":81123,
      "sales_channel_id":6,
      "selling_price":120,
      "pvgc":279,
      "sales_channel_commission_fee":11,
      "ecotax":2,
      "sorecop":0,
      "vat_rate":0.2,
      "purchase_price_tax_excluded":140.91,
      "weighted_purchase_price_tax_excluded":142.91,
      "stock":1,
      "unconditional_discount":0,
      "promo_budget_amount":0
      }]
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data" should have 3 elements
    And the JSON node "data" should be identical to
  """
      [
        {
          "errors": null,
          "margin": 86.25666666666669,
          "margin_rate": 0.3763927272727273,
          "markup_rate": 0.6035733445291911,
          "sales_channel_commission": 0,
          "sales_channel_id": 1,
          "selling_price_tax_excluded": 231.66666666666669,
          "selling_price_tax_included": 278
        },
        {
          "errors": null,
          "margin": 63.923333333333375,
          "margin_rate": 0.2779275362318842,
          "markup_rate": 0.44729783313507365,
          "sales_channel_commission": 23.16666666666667,
          "sales_channel_id": 5,
          "selling_price_tax_excluded": 231.66666666666669,
          "selling_price_tax_included": 278
        },
        {
          "errors": [
            {
              "code": "selling_price_margin_is_too_low",
              "message": "Product margin is too low"
            },
            {
              "code": "selling_price_margin_rate_is_too_low",
              "message": "Margin rate should not be lower than 12%"
            }
          ],
          "margin": -55.57666666666667,
          "margin_rate": -0.5651864406779662,
          "markup_rate": -0.3888927763394211,
          "sales_channel_commission": 11,
          "sales_channel_id": 6,
          "selling_price_tax_excluded": 100,
          "selling_price_tax_included": 120
        }
      ]
  """
