Feature: Get list of article planned prices

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_planned_price.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/planned-prices"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/planned-prices"
    Then  the response status code should be 401

  Scenario: Check successful response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/planned-prices" with body:
    """
    {
      "where": {
        "article_id": {
          "_eq": 81078
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->article_planned_prices" should have 1 element
    And   the JSON node "data->article_planned_prices[0]" should be identical to
    """
    {
      "article_planned_price_id": 1,
      "article_id": 81078,
      "selling_price": 199.5,
      "exit_selling_price": 200,
      "starts_at": "2024-06-01 10:35:41",
      "ends_at": "2025-09-01 10:35:41",
      "applied_at": null,
      "created_by": {
        "id": 1033,
        "fullname": "Hugo LAHUTTE"
      },
      "sales_channel_ids": [1, 2, 3]
    }
    """
