Feature: Update article information

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/put_article.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/13895/eans"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/13895/eans"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/13895/eans" with body:
    """
    {"eans": ["***********", "************", "*************"]}
    """
    Then  the response status code should be 403


  Scenario: Try to update with a missing eans column
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_LOGISTIC_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/13895/eans" with body:
    """
    {}
    """
    Then  the response status code should be 400

  Scenario: Try to update with a bad ean
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_LOGISTIC_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/13895/eans" with body:
    """
    {"eans": ["123456789"]}
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "eans[0]": "[key:ean_invalid] value \"123456789\" : this ean is not valid"
      }
    }
    """

  Scenario: Successfully update article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_LOGISTIC_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/13895/eans" with body:
    """
    {"eans": ["***********", "************", "*************"]}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"updated": 4}
    """

