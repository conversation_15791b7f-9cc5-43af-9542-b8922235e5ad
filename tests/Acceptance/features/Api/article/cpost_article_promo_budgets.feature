Feature: Get list of article promo budget

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku_v2.sql"
    And   I load mysql fixtures from file "article/article_promo_budget.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/promo-budgets"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/promo-budgets"
    Then  the response status code should be 401

  Scenario: Check successful response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/promo-budgets" with body:
    """
    {
      "where": {
        "article_id": {
          "_eq": 72215
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->article_promo_budgets" should have 2 element
    And   the JSON node "data->article_promo_budgets[0]" should be identical to
    """
    {
      "id": 1,
      "article_id": 72215,
      "amount": 100,
      "start_at": "2024-06-01 12:00:00",
      "end_at": "2024-06-08 12:00:00"
    }
    """
