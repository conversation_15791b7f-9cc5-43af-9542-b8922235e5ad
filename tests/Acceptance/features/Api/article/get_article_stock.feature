Feature: Retrieve an article stock by a sku

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_stock.sql"
    And   I send a "GET" request to "/api/v1/article-stock/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/article-stock/123"
    Then  the response status code should be 401

  Scenario: Test with non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article-stock/666"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with sku or id "666".
    """

  Scenario: Retrieve an article stock by its sku
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article-stock/ARCAMRBLINKNR"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->current_user_warehouse_id" should be equal to 2
    And   the JSON node "data->stock" should have "4" elements
    And   the JSON node "data->stock[0]" should be identical to
    """
    {
      "warehouse_id": 1,
      "warehouse_name": "Champigny",
      "shipment_method_id": 5,
      "quantity": {
          "in_stock": 25,
          "is_prepared": 0,
          "safety_stock_threshold": 0,
          "awaiting_preparation": 2
      },
      "supplier_orders": [
          {
              "created_at": "2050-06-06",
              "supplier_id": 162,
              "order_type": "standard",
              "order_status": "en cours",
              "warehouse_id": 1,
              "quantity_ordered": 1,
              "supplier_order_id": 2,
              "quantity_delivered": 0,
              "expected_delivery_date": "2050-06-06",
              "deliveries": []
          },
          {
              "created_at": "2050-06-06",
              "supplier_id": 162,
              "order_type": "standard",
              "order_status": "en preparation",
              "warehouse_id": 1,
              "quantity_ordered": 6,
              "supplier_order_id": 3,
              "quantity_delivered": 3,
              "expected_delivery_date": "2050-06-06",
              "deliveries": []
          }
      ],
      "outgoing_transfers": [],
      "incoming_transfers": [],
      "related_customer_order_ids": [1712826,1712828]
    }
    """
    And   the JSON node "data->stock[1]" should be identical to
    """
    {
      "warehouse_id": 3,
      "warehouse_name": "Havre",
      "shipment_method_id": null,
      "quantity": {
        "in_stock": 25,
        "is_prepared": 0,
        "safety_stock_threshold": 0,
        "awaiting_preparation": 3
      },
      "supplier_orders": [
        {
          "created_at": "2050-06-06",
          "expected_delivery_date": "2050-01-19",
          "order_type": "standard",
          "order_status": "en cours",
          "quantity_delivered": 2,
          "quantity_ordered": 10,
          "supplier_id": 162,
          "supplier_order_id": 5,
          "warehouse_id": 3,
          "deliveries": [
            {
              "expected_delivery_id": 10,
              "supplier_order_product_id": 5,
              "expected_delivery_date": "2050-01-15",
              "expected_quantity": 2
            },
            {
              "expected_delivery_id": 11,
              "supplier_order_product_id": 5,
              "expected_delivery_date": "2011-01-01",
              "expected_quantity": 4
            },
            {
              "expected_delivery_id": 12,
              "supplier_order_product_id": 5,
              "expected_delivery_date": "2050-01-19",
              "expected_quantity": 3
            }
          ]
        }
      ],
      "outgoing_transfers": [
        {
          "to_id": 1,
          "from_id": 3,
          "quantity": 5,
          "created_at": "2050-08-08 00:00:00.000000",
          "transfer_id": 104,
          "delivery_note_id": null,
          "customer_order_id": null
        },
        {
          "to_id": 2,
          "from_id": 3,
          "quantity": 1,
          "created_at": "2050-08-08 00:00:00.000000",
          "transfer_id": 100,
          "delivery_note_id": null,
          "customer_order_id": null
        }
      ],
      "incoming_transfers": [],
      "related_customer_order_ids": [
        1712826,
        1712827,
        1712828
      ]
    }
    """
    And   the JSON node "data->stock[2]" should be identical to
    """
    {
        "warehouse_id": 5,
        "warehouse_name": "Nantes",
        "shipment_method_id": 31,
        "quantity": {
            "in_stock": 0,
            "is_prepared": 0,
            "safety_stock_threshold": 0,
            "awaiting_preparation": 0
        },
        "supplier_orders": [],
        "outgoing_transfers": [],
        "incoming_transfers": [
            {
                "to_id": 5,
                "from_id": 1,
                "quantity": 3,
                "created_at": "2050-08-08 00:00:00.000000",
                "transfer_id": 103,
                "delivery_note_id": null,
                "customer_order_id": null
            }
        ],
        "related_customer_order_ids": []
    }
    """
    And   the JSON node "data->stock[3]" should be identical to
    """
    {
        "warehouse_id": 2,
        "warehouse_name": "Paris 8e",
        "shipment_method_id": 14,
        "quantity": {
            "in_stock": 0,
            "is_prepared": 0,
            "safety_stock_threshold": 0,
            "awaiting_preparation": 0
        },
        "supplier_orders": [],
        "outgoing_transfers": [],
        "incoming_transfers": [],
        "related_customer_order_ids": []
    }
    """

  Scenario: Retrieve an article stock by its id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article-stock/81078"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->stock" should have "4" elements
