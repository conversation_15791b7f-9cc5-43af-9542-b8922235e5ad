Feature: Add an planned price to an article

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_planned_price.sql"
    And   I send a "POST" request to "/api/v1/article/81078/planned-price"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/article/81078/planned-price"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/81078/planned-price"
    """
      {
      "selling_price": 184.50,
      "exit_selling_price": 200.00,
      "starts_at": "2024-10-01 10:35:41",
      "ends_at": "2025-10-01 10:35:41",
      "sales_channel_ids": [1, 2, 3]
      }
    """
    Then  the response status code should be 403
    And   the response should be in JSON


  Scenario: Test successfully add planned price to an article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/81078/planned-price" with body:
    """
    {
      "selling_price": 184.50,
      "exit_selling_price": 200.00,
      "starts_at": "2024-10-01 10:35:41",
      "ends_at": "2025-10-01 10:35:41",
      "sales_channel_ids": [1, 2, 3]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 1 element
    And   the JSON node "data->article_planned_price_id" should be equal to the number 5
