Feature: Get list of article_comments

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/cpost_article_comments.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article-comments"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article-comments"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/article-comments"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for comments article API without comments
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article-comments" with body:
    """
    {
      "where": {
        "article_id": {
          "_eq": 81078
        },
        "type": {
          "_eq": "general"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "0"
    And   the JSON node "data->article_comments" should have 0 elements
    And   the JSON node "data->article_comments" should be identical to
    """
    []
    """

  Scenario: Check successful response for comments article API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article-comments" with body:
    """
    {
      "where": {
        "article_id": {
          "_eq": 81123
        },
        "type": {
          "_eq": "general"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->article_comments" should have 1 elements
    And   the JSON node "data->article_comments[0]" should be identical to
    """
    {
      "article_comment_id": 1,
      "article_id": 81123,
      "created_at": "2018-10-09 16:57:05",
      "created_by": {
        "fullname": "Hugo LAHUTTE",
        "id": 1033
      },
      "is_active": true,
      "message": "coucou",
      "type": "general"
    }
    """

  Scenario: Check successful response for alert article API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article-comments" with body:
    """
    {
      "where": {
        "type": {
          "_eq": "alert"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->article_comments" should have 1 elements
    And   the JSON node "data->article_comments[0]" should be identical to
    """
    {
      "article_comment_id": 3,
      "article_id": 81123,
      "created_at": "2019-10-09 17:58:05",
      "created_by": {
        "fullname": "Hugo LAHUTTE",
        "id": 1033
      },
      "is_active": true,
      "message": "ALERTE",
      "type": "alert"
    }
    """

