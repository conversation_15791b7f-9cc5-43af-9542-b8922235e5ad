Feature: Retrieve an article by a given code
  In order to obtain some information about an article through the API
  As a user
  I need to be able to identify an article using an arbitrary code

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_by_code.sql"
    And   I send a "GET" request to "/api/v1/article/by-code/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/article/by-code/123"
    Then  the response status code should be 401

  Scenario: Test with non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article/by-code/666"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with code "666".
    """

  Scenario: Test with an existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article/by-code/11230081123"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article" should exist
    And   the JSON node "data->article->product_id" should be equal to "81123"
    And   the JSON node "data->article->sku" should be equal to "LBCLD25BP"
    And   the JSON node "data->article->subcategory_id" should be equal to "144"
    And   the JSON node "data->article->created_at" should be equal to "2013-02-21"
    And   the JSON node "data->article->updated_at" should be equal to "2019-07-26 00:34:00"
    And   the JSON node "data->article->status" should be equal to "oui"
    And   the JSON node "data->article->brand_name" should be equal to "La Boite Concept"
    And   the JSON node "data->article->brand_id" should be equal to "959"
    And   the JSON node "data->article->selling_price" should be equal to "389.00"
    And   the JSON node "data->article->pvgc" should be equal to "389.00"
    And   the JSON node "data->article->ecotax" should be equal to "0.00"
    And   the JSON node "data->article->sorecop" should be equal to "0.00"
    And   the JSON node "data->article->weight" should be equal to "8.450"
    And   the JSON node "data->article->supplier_id" should be equal to "400"
    And   the JSON node "data->article->supplier_reference" should be equal to "LD-F-25mm-N-P"
    And   the JSON node "data->article->stock_quantity" should be equal to "2"
    And   the JSON node "data->article->basket_description" should be equal to "Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130"
    And   the JSON node "data->article->short_description" should be equal to "Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)"
    And   the JSON node "data->article->initial_selling_price" should be equal to "389.00"
    And   the JSON node "data->article->is_package" should be equal to "0"
    And   the JSON node "data->article->code128" should be equal to "11230081123"
    And   the JSON node "data->article->sku_havre" should be null
    And   the JSON node "data->article->package_number" should be equal to "1"
