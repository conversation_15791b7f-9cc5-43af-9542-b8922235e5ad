Feature: Get list of stock moves

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "stock_move/cpost_stock_moves.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for quotes API with proper type casting
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves" with body:
    """
    {
      "where": {
        "_and": [
          {"stock_move_id": {"_eq": 7551205}}
        ]
      },
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->stock_moves" should have 1 elements
    And   the JSON node "data->stock_moves[0]" should have the following keys and values
    """
    {
      "stock_move_id": 7551205,
      "warehouse_name": "Champigny 2",
      "quantity": 1,
      "location_label": "Stock virtuel",
      "location_code": "21.virtual_stock",
      "created_at": "2022-02-16 16:28:38",
      "user": "admin",
      "user_full_name": "Seigneur ADMIN",
      "type": "interne",
      "comment": "test refs externes BL / avoir / carte cadeau",
      "buy_price": 42,
      "selling_price": 52.2,
      "delivery_note_customer_id": 1,
      "supplier_order_id": null,
      "transfer_id": null,
      "return_note_id": null,
      "move_mission_id": null,
      "credit_note_id": 1,
      "gift_card_id": 1,
      "customer_order_id": 1878297,
      "delivery_note_transfer_id": null
    }
    """

  Scenario: Check successful response for stock-move API with a filter on the warehouse name
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves" with body:
    """
    {
      "where": {
        "_and": [
          {"warehouse_id": {"_eq": 10}}
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->stock_moves" should have 1 elements
    And   the JSON node "data->stock_moves[0]" should have the following keys and values
    """
    {
      "stock_move_id": 7551202,
      "warehouse_name": "Grenoble",
      "quantity": 1,
      "location_label": "Emplacement par défaut (Grenoble)",
      "location_code": "10.store",
      "created_at": "2022-02-16 16:23:37",
      "user": "admin",
      "user_full_name": "Seigneur ADMIN",
      "type": "interne",
      "comment": "test filtre depot Grenoble",
      "buy_price": 50,
      "selling_price": null,
      "delivery_note_customer_id": null,
      "supplier_order_id": null,
      "transfer_id": null,
      "return_note_id": null,
      "move_mission_id": null,
      "credit_note_id": null,
      "gift_card_id": null,
      "customer_order_id": null,
      "delivery_note_transfer_id": null
    }
    """

  Scenario: Check successful response for stock-move API with a filter on the warehouse id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves" with body:
    """
    {
      "where": {
        "_and": [
          {"warehouse_name": {"_eq": "Grenoble"}}
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->stock_moves" should have 1 elements
    And   the JSON node "data->stock_moves[0]" should have the following keys and values
    """
    {
      "stock_move_id": 7551202,
      "warehouse_name": "Grenoble",
      "quantity": 1,
      "location_label": "Emplacement par défaut (Grenoble)",
      "location_code": "10.store",
      "created_at": "2022-02-16 16:23:37",
      "user": "admin",
      "user_full_name": "Seigneur ADMIN",
      "type": "interne",
      "comment": "test filtre depot Grenoble",
      "buy_price": 50,
      "selling_price": null,
      "delivery_note_customer_id": null,
      "supplier_order_id": null,
      "transfer_id": null,
      "return_note_id": null,
      "move_mission_id": null,
      "credit_note_id": null,
      "gift_card_id": null,
      "customer_order_id": null,
      "delivery_note_transfer_id": null
    }
    """

  Scenario: Check successful response for stock-move API with a filter on the product id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/stock-moves" with body:
    """
    {
      "where": {
        "_and": [
          {"product_id": {"_eq": 84048}}
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->stock_moves" should have 2 elements
    And   the JSON node "data->stock_moves[0]" should have the following keys and values
    """
    {
      "stock_move_id": 7551202,
      "warehouse_name": "Grenoble",
      "quantity": 1,
      "location_label": "Emplacement par défaut (Grenoble)",
      "location_code": "10.store",
      "created_at": "2022-02-16 16:23:37",
      "user": "admin",
      "user_full_name": "Seigneur ADMIN",
      "type": "interne",
      "comment": "test filtre depot Grenoble",
      "buy_price": 50,
      "selling_price": null,
      "delivery_note_customer_id": null,
      "supplier_order_id": null,
      "transfer_id": null,
      "return_note_id": null,
      "move_mission_id": null,
      "credit_note_id": null,
      "gift_card_id": null,
      "customer_order_id": null,
      "delivery_note_transfer_id": null
    }
    """
    And   the JSON node "data->stock_moves[1]" should have the following keys and values
    """
    {
      "stock_move_id": 7551196,
      "warehouse_name": "Champigny 2",
      "quantity": -1,
      "location_label": "Zone de sortie",
      "location_code": "21.exit",
      "created_at": "2022-01-24 20:42:35",
      "user": "admin",
      "user_full_name": "Seigneur ADMIN",
      "type": "sortie",
      "comment": "Sortie stock lors de la validation du Bon de Livraison.",
      "buy_price": null,
      "selling_price": null,
      "delivery_note_customer_id": null,
      "supplier_order_id": null,
      "transfer_id": null,
      "return_note_id": null,
      "move_mission_id": null,
      "credit_note_id": null,
      "gift_card_id": null,
      "customer_order_id": null,
      "delivery_note_transfer_id": null
    }
    """