Feature: Retrieve a list of turnover

  @clear-database
  Scenario: Test without authorization
    When I load mysql fixtures from file "users.sql"
    And   I load sql fixtures from file "statistics/cpost_customer_order_workflow.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/workflow"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/workflow"
    Then  the response status code should be 401

  Scenario: Test without premission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/workflow"
    Then  the response status code should be 403

  Scenario: Get workflow
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STATISTICS_GLOBAL_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/workflow" with body:
      """
      {
        "periods": {
          "J": ["2023-08-01", "2023-08-01"]
        },
        "pivots": ["order_status"]
      }
      """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->last_sync" should be equal to the string "2023-09-01 12:00:00"
    And   the JSON should be equal to:
    """
    {
      "status": "success",
      "data": {
        "statistics": [
          {
            "period": "J",
            "from": "2023-08-01",
            "to": "2023-08-02",
            "current": {
              "turnover": 60,
              "number": 2,
              "margin": 35,
              "customer_order_ids": [
                3,
                4
              ]
            },
            "order_status": "Annulée"
          },
          {
            "period": "J",
            "from": "2023-08-01",
            "to": "2023-08-02",
            "current": {
              "turnover": 80,
              "number": 1,
              "margin": 60,
              "customer_order_ids": [
                2
              ]
            },
            "order_status": "Facturée"
          },
          {
            "period": "J",
            "from": "2023-08-01",
            "to": "2023-08-02",
            "current": {
              "turnover": 40,
              "number": 1,
              "margin": 30,
              "customer_order_ids": [
                1
              ]
            },
            "order_status": "Payée"
          }
        ],
        "last_sync": "2023-09-01 12:00:00",
        "_request": {
          "periods": {
            "J": [
              "2023-08-01",
              "2023-08-01"
            ]
          },
          "filters": null,
          "pivots": [
            "order_status"
          ]
        }
      }
    }
    """
