Feature: Retrieve a list of unavailable product
  @clear-database
  Scenario: Test without authorization
    When I load mysql fixtures from file "users.sql"
    And   I load sql fixtures from file "statistics/cpost_unavailable_product.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/unavailable-products"
    Then  the response status code should be 401

 Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/unavailable-products"
    Then  the response status code should be 401

  Scenario: Test without premission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/statistics/unavailable-products"
    Then  the response status code should be 403

 Scenario: Not found unavailable product
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STATISTICS_GLOBAL_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/unavailable-products" with body:
      """
      {
      "filters": {
          "_or": [
            {"date": {"_eq": "2023-07-30"}}
          ]
        }
      }
      """
    Then  the response status code should be 404

  Scenario: Get list of unavailable product
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STATISTICS_GLOBAL_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/unavailable-products" with body:
      """
      {
        "filters": {
          "_or": [
            {"date": {"_eq": "2023-08-01"}},
            {"date": {"_eq": "2023-08-08"}}
          ]
        }
      }
      """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON should be equal to:
      """
        {
            "status": "success",
            "data": {
                "statistics": [
                    {
                        "date": "2023-08-01",
                        "total_quantity": 30,
                        "turnover": 400,
                        "sku_number": 2
                    },
                     {
                        "date": "2023-08-08",
                        "total_quantity": 170,
                        "turnover": 1800,
                        "sku_number": 1
                    }
                ],
                "_request": {
                    "filters": {
                        "_or": [
                            {"date": {"_eq": "2023-08-01"}},
                            {"date": {"_eq": "2023-08-08"}}
                        ]
                    }
                }
            }
        }
      """