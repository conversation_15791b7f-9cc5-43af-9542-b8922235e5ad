Feature: Get list of payments from the v1 API
  As someone identified in the ERP
  I want to see the users and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "customer_order/cpost_customer_order_payments.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-payments"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-payments"
    Then  the response status code should be 401

  Scenario: Get list of customer order payments without filters
    # Customer order payments requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-payments"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->payments" should have 3 elements

  Scenario: Get list of customer order payments filtered by a customer order id
    # Customer order payments requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-payments" with body:
    """
    {
      "where": {
        "id_commande": {
          "_eq": 2
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->payments" should have 2 elements
    And   the JSON node "data->payments[0]" should have the following keys and values
    """
    {
      "id": 102,
      "id_commande": 2,
      "id_unique": 1,
      "id_paiement": 11,
      "type": "paiement",
      "creation_date": "2018-09-11 14:36:23",
      "creation_usr": "admin",
      "creation_montant": 249,
      "creation_justificatif": null,
      "creation_origine": "son-video.com",
      "acceptation_date": "2019-10-08 19:15:40",
      "acceptation_usr": "admin",
      "acceptation_montant": 249,
      "acceptation_justificatif": "XXXXXX",
      "annulation_date": "2019-10-08 19:15:40",
      "annulation_usr": "admin",
      "annulation_montant": 249,
      "demande_remise_date": null,
      "demande_remise_usr": null,
      "remise_date": null,
      "remise_usr": null,
      "remise_montant": 0,
      "remise_justificatif": null,
      "remise_bon": null,
      "remise_taux": 1,
      "impaye_date": null,
      "impaye_usr": null,
      "impaye_montant": 0,
      "auto_statut": null,
      "auto_statut_detail": null,
      "auto_garantie": null,
      "auto_garantie_detail": null,
      "pays_ip": null,
      "pays_origine": null,
      "description": "Carte terminal de paiement"
    }
    """
