Feature: Get list of supplier orders
  As someone identified in the ERP
  I want to see the details of supplier orders

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/cpost_supplier_order_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders"
    Then  the response status code should be 401

  Scenario: Api grant access without special permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders" with body:
    """
    {
      "where": {
        "_and": [{ "supplier_order_id": { "_eq": 1 } }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"

  Scenario: Get list of supplier orders without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders" with body:
    """
    {
      "where": {
        "_and": [{ "supplier_order_id": { "_in": [1, 2] } }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->supplier_orders" should have 2 elements
    # supplier orders should be ordered by id (by default)
    And   the JSON node "data->supplier_orders[0]->supplier_order_id" should be equal to "1"
    And   the JSON node "data->supplier_orders[1]->supplier_order_id" should be equal to "2"
    # full check
    And   the JSON node "data->supplier_orders[0]" should have the following keys and values
    """
    {
        "supplier_order_id": 1,
        "supplier_id": 162,
        "supplier_name": "PPL",
        "warehouse_id": 1,
        "created_at": "2019-01-16",
        "status": "en cours",
        "comment": "",
        "sent_at": "2023-05-15 14:46:39",
        "ordered_quantity": 30,
        "delivered_quantity": 10,
        "updated_at": "2023-01-02 12:00:00",
        "supplier_order_products": []
    }
    """

  Scenario: Get list of supplier orders filtered on a status
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders" with body:
    """
    {
      "where": {
        "_and": [
          {
            "status": {
              "_eq": "en cours"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_orders" should have 1 element

  Scenario: Get supplier orders with only specified fields
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders" with body:
    """
    {
      "fields": ["supplier_order_id", "supplier_id", "status"],
      "where": {
        "_and": [
          {
            "supplier_order_id": {
              "_eq": "1"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_orders" should have 1 element
    And   the JSON node "data->supplier_orders[0]" should have the following keys and values
    """
    {
        "supplier_order_id": 1,
        "supplier_id": 162,
        "status": "en cours"
    }
    """

  Scenario: Get supplier orders with products details
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders" with body:
    """
    {
      "where": {
        "_and": [
          {
            "supplier_order_id": {
              "_eq": "1"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_orders" should have 1 element
    And   the JSON node "data->supplier_orders[0]->supplier_order_products" should have 3 elements

  Scenario: Get supplier orders with not billed filter
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-orders" with body:
    """
    {
      "where": {
        "_and": [
          {
            "billed": {
              "_eq": false
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->supplier_orders" should have 2 element
