Feature: Get list of supplier order products
  As someone identified in the ERP
  I want to see the products of a supplier order with their expected deliveries

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/cpost_supplier_order_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products"
    Then  the response status code should be 401

  Scenario: Api grant access without special permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products" with body:
    """
    {
      "where": {
        "_and": [{ "supplier_order_id": { "_eq": 1 } }, { "is_delivered": { "_eq": false } }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"

  Scenario: Get a supplier order products
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products" with body:
    """
    {
      "where": {
        "_and": [{ "supplier_order_id": { "_eq": 2 } }, { "is_delivered": { "_eq": false } }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_order_products" should have 1 elements
    # full check on the product (it has expected products)
    And   the JSON node "data->supplier_order_products[0]->supplier_order_product_id" should be equal to the number "4"
    And   the JSON node "data->supplier_order_products[0]->supplier_order_id" should be equal to the number "2"
    And   the JSON node "data->supplier_order_products[0]->article_id" should be equal to the number "81079"
    And   the JSON node "data->supplier_order_products[0]->supplier_reference" should be equal to "RBLINK2"
    And   the JSON node "data->supplier_order_products[0]->ordered_quantity" should be equal to the number "10"
    And   the JSON node "data->supplier_order_products[0]->delivered_quantity" should be equal to the number "9"
    And   the JSON node "data->supplier_order_products[0]->is_delivered" should be false
    And   the JSON node "data->supplier_order_products[0]->sku" should be equal to "ARCAMRBLINKNR2"
    And   the JSON node "data->supplier_order_products[0]->description" should be equal to "Arcam rBlink v2"
    And   the JSON node "data->supplier_order_products[0]->article_name" should be equal to "rBlink version 2"
    And   the JSON node "data->supplier_order_products[0]->supplier_name" should be equal to "PPL"
    And   the JSON node "data->supplier_order_products[0]->article_image" should be null
    # expected deliveries
    And   the JSON node "data->supplier_order_products[0]->expected_deliveries" should have 1 element
    # full content check of a delivery
    And   the JSON node "data->supplier_order_products[0]->expected_deliveries[0]->expected_delivery_id" should be equal to the number "3"
    And   the JSON node "data->supplier_order_products[0]->expected_deliveries[0]->supplier_order_product_id" should be equal to the number "4"
    And   the JSON node "data->supplier_order_products[0]->expected_deliveries[0]->expected_delivery_date" should be equal to "2011-01-01 00:00:00"
    And   the JSON node "data->supplier_order_products[0]->expected_deliveries[0]->expected_quantity" should be equal to the number "1"

  Scenario: Get a list of supplier order products
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products" with body:
    """
    {
      "where": {
        "_and": [{ "supplier_order_id": { "_eq": 1 } }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->supplier_order_products" should have 3 elements

  Scenario: Apply some filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products" with body:
    """
    {
      "where": {
        "_and": [
          { "supplier_id": { "_in": [162] } }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"

  Scenario: Order by backordered products
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products" with body:
    """
    {
      "order_direction": "asc",
      "order_by": "backorder_status",
      "where": {
        "_and": [{
          "status": {
            "_in": [
              "TERMINEE"
            ]
          }
        }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "0"
    And   the JSON node "data->supplier_order_products" should have 0 elements

  Scenario: Get non-billed supplier order products
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order-products" with body:
    """
    {
      "where": {
        "_and": [{
          "billed": {
              "_eq": false
            }
        }]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->supplier_order_products" should have 3 elements
