Feature: Update an expected delivery on a supplier order product
  As someone identified in the ERP with proper right
  I want to be able to modify the quantity of an expected delivery

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/delete_expected_delivery.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/2" with body:
    """
    {
      "quantity": 1
    }
    """
    Then  the response status code should be 401

  Scenario: Api requires a permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/2" with body:
    """
    {
      "quantity": 1
    }
    """
    Then  the response status code should be 403

  Scenario: Modification works
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/2" with body:
    """
    {
      "quantity": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Modification fails on unknown expected delivery
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/9999" with body:
    """
    {
      "quantity": 1
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should contain 'Expected delivery not found'

  Scenario: Does not allow negative quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/2" with body:
    """
    {
      "quantity": 0
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "error->exception[0]->message" should contain 'Parameter "quantity" of value "0" violated a constraint'
    And   the JSON node "error->exception[0]->message" should contain 'This value should be positive'

  Scenario: Does not allow to set a quantity lower to already delivered on the date
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/4" with body:
    """
    {
      "quantity": 1
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'Quantite de 2 deja livree a cette date'

  Scenario: Does not allow to set a quantity upper than ordered
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/2" with body:
    """
    {
      "quantity": 100
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'Le maximum autorise est de 10'

  Scenario: Does not allow to set a quantity upper than ordered (with other expected deliveries, including 2011)
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/expected-delivery/6" with body:
    """
    {
      "quantity": 5
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'Le maximum autorise est de 3'
