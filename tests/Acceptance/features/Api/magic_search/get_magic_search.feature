Feature: Retrieve all data from elasticSearch
  In order to obtain datas through the API
  As a user

  @clear-database
  Scenario: Test without authorization
    And   I send a "GET" request to "/api/v1/magic-search"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/magic-search"
    Then  the response status code should be 401

  Scenario: Test that params are good
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/magic-search?search_terms=eph100"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->_request->search_terms" should be equal to the string "eph100"
    And   the JSON node "data->_request->context" should be equal to the string "all"
    And   the JSON node "data->_request->from" should be equal to "0"
    And   the JSON node "data->_request->size" should be equal to "15"
