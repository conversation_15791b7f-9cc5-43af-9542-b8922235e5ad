Feature: Get a secured metabase dashboard url
  As someone identified in the ERP
  I want to see metabase embed dashboards

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/metabase/embed-dashboard-url"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/metabase/embed-dashboard-url"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/metabase/embed-dashboard-url" with body:
    """
    {
      "dashboard": 1,
      "params": {}
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->url" should contain "https://metabase.lxc/embed/dashboard/"
