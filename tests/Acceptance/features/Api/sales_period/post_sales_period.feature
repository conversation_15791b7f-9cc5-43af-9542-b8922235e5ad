Feature: Create sales period

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_period/cpost_sales_period.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-period"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-period"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-period" with body:
    """
    {
      "start_at":"2022-04-15 00:00:00",
      "end_at":"2022-04-16 23:59:59"
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test success
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-period" with body:
    """
    {
      "start_at":"2022-04-15 00:00:00",
      "end_at":"2022-04-16 23:59:59"
    }
    """
    And   the JSON node "status" should be equal to the string "success"
