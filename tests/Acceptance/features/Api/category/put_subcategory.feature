Feature: Update subcategory
  As a user with enough rights
  I need to be able to edit subcategories

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "category/categories.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/subcategory/144"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/subcategory/144"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/subcategory/144"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Fails when no changes happens
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/subcategory/144" with body:
    """
    {
    "data": {
      "subcategory_id": 144,
      "name": "Pieds d'enceintes",
      "parent_category_id": 59,
      "warranty_type": "SON",
      "outsize": false,
      "bbac_subtype_id": 8,
      "charged_delivery": 9.99,
      "subcategory_type": "HIFI",
      "seller_commission_config": {"ldw_exclude": false, "cultural_product": false},
      "custom_code": "********",
      "ecotax_code": "55555"
      }
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "No changes occurred"

  Scenario: Failed on mismatching subcategory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/subcategory/144" with body:
    """
    {
    "data": {
      "subcategory_id": 999,
      "name": "Amplis casque",
      "parent_category_id": 96,
      "warranty_type": "VIDEO",
      "outsize": true,
      "bbac_subtype_id": 8,
      "charged_delivery": 8.88,
      "subcategory_type": "IMAGE",
      "seller_commission_config": {"ldw_exclude": false, "cultural_product": false},
      "custom_code": "********",
      "ecotax_code": "55555"
      }
    }
    """
    Then  the response status code should be 422
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "subcategory_id mismatch"

  Scenario: Successfully update the subcategory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/subcategory/144" with body:
    """
    {
    "data": {
      "subcategory_id": 144,
      "name": "Amplis casque",
      "parent_category_id": 96,
      "warranty_type": "SON",
      "outsize": true,
      "bbac_subtype_id": 8,
      "charged_delivery": 8,
      "subcategory_type": "IMAGE",
      "seller_commission_config": {"ldw_exclude": false, "cultural_product": false},
      "custom_code": "",
      "ecotax_code": "01210"
      }
    }
    """
    Then  the response status code should be 204