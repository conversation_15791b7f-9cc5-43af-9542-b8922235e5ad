Feature: Anonymize customer

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And  I load mysql fixtures from file "prospect.sql"
    And   I send a "PUT" request to "/api/v1/customer/anonymize/25"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/anonymize/25"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And I send a "PUT" request to "/api/v1/customer/anonymize/25"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test fail prospect not exist
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I expect RPC call to "bo-cms" "customer:anonymize" to respond with:
    """
    {
      "status": true
    }
    """
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/anonymize/25"
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Le prospect n'existe pas
    """

  Scenario: Check successful response for customers anonymize API
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I expect RPC call to "bo-cms" "customer:anonymize" to respond with:
    """
    {
      "status": true
    }
    """
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/anonymize/1"
    Then  the response status code should be 204

