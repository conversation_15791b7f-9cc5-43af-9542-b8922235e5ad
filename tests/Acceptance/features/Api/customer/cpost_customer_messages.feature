Feature: Get list of customers messages
  As someone identified in the ERP
  I want to see the customers messages and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "customer/customer_messages.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-messages"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-messages"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/customer-messages"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for customers API v1 where all column are casted as strings
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-messages" with body:
    """
    {
      "where": {
        "_and": [
          {
            "customer_id": {
              "_eq": 2
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "5"
    And   the JSON node "data->messages" should have 2 elements
    And   the JSON node "data->messages[0]" should have the following keys and values
    """
    {
      "id": 1,
      "created_at": "2009-01-09 12:06:37",
      "subject": "Un problème sur le site ?",
      "message": "Ceci est un test Florian pour l'équipe informatique :-)",
      "origin": "client",
      "sender_email": "<EMAIL>",
      "linked_messages": [
        {
          "id": 3,
          "created_at": "2009-01-09 12:25:26",
          "subject": "Un problème sur le site ?",
          "message": "Ceci est un test",
          "origin": "svd",
          "sender_email": "<EMAIL>",
          "linked_messages": []
        }
      ]
    }
    """
    And   the JSON node "data->messages[0]->linked_messages[0]" should have the following keys and values
    """
    {
      "id": 3,
      "created_at": "2009-01-09 12:25:26",
      "subject": "Un problème sur le site ?",
      "message": "Ceci est un test",
      "origin": "svd",
      "sender_email": "<EMAIL>",
      "linked_messages": []
    }
    """
