Feature: Get list of customers
  As someone identified in the ERP
  I want to see the customers and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I load mysql fixtures from file "customer/cpost_customers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customers"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customers"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/customers"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for customers API v1 where all column are casted as strings
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customers" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "5"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->customers" should have 1 elements
    And   the JSON node "data->customers[0]" should be identical to
    """
    {
        "customer_id": "1",
        "email_address": "<EMAIL>",
        "type": "particulier",
        "civility": "M.",
        "firstname": "Arthur",
        "lastname": "Baudouin",
        "company_name": "The Entreprise",
        "address": "tmp",
        "zip_code": "44100",
        "country_id": "67",
        "phone": "0200112233",
        "mobile_phone": "0600112233",
        "customer_type": "particulier",
        "is_blacklisted": "0",
        "accept_marketing_emails": "1",
        "created_at": "1990-01-01 00:00:00",
        "modified_at": "1990-01-01 00:00:00",
        "birthdate": "2022-01-27",
        "computed_name": "M. ARTHUR BAUDOUIN",
        "country_name": "FRANCE",
        "country_code": "FR",
        "has_ongoing_premium_warranty": "0",
        "atradius": "Pas soumis",
        "balance_acceptance": "0",
        "classification": null,
        "encours_interne": "0",
        "encours_sfac": "0",
        "incoterm": null,
        "npai": "0",
        "tva_number": null
    }
    """

  Scenario: Attempt to load list but provide an incorrect dependency
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customers" with body:
    """
    {
      "included_dependencies": ["imposteur"]
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Optional column with "imposteur" does not exists, Available keys are : "customer_orders_aggregates", "credit_notes_aggregates", "newsletter".
    """

  Scenario: Check successful response for customers API v1 with additional optional columns (casted to string)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customers" with body:
    """
    {
      "included_dependencies": ["customer_orders_aggregates"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customers[0]" should be identical to
    """
    {
      "customer_id": "1",
      "email_address": "<EMAIL>",
      "type": "particulier",
      "civility": "M.",
      "firstname": "Arthur",
      "lastname": "Baudouin",
      "company_name": "The Entreprise",
      "address": "tmp",
      "zip_code": "44100",
      "country_id": "67",
      "phone": "0200112233",
      "mobile_phone": "0600112233",
      "customer_type": "particulier",
      "is_blacklisted": "0",
      "accept_marketing_emails": "1",
      "created_at": "1990-01-01 00:00:00",
      "modified_at": "1990-01-01 00:00:00",
      "birthdate": "2022-01-27",
      "computed_name": "M. ARTHUR BAUDOUIN",
      "country_name": "FRANCE",
      "country_code": "FR",
      "has_ongoing_premium_warranty": "0",
      "customer_orders_aggregates": {
        "customer_id": "1",
        "count": "3",
        "revenue_generated": "1485.99"
      },
      "atradius": "Pas soumis",
      "balance_acceptance": "0",
      "classification": null,
      "encours_interne": "0",
      "encours_sfac": "0",
      "incoterm": null,
      "npai": "0",
      "tva_number": null
    }
    """
    # v1 api does not strip the pivot column
    And   the JSON node "data->customers[0]->customer_orders_aggregates->customer_id" should contain "1"
    And   the JSON node "data->customers[0]->customer_orders_aggregates->count" should contain "3"
    And   the JSON node "data->customers[0]->customer_orders_aggregates->revenue_generated" should contain "1485.99"
