Feature: api to send a rpc to BO-CMS to change the customer's email

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And  I load mysql fixtures from file "prospect.sql"
    And   I send a "POST" request to "/api/v1/customer/ask-change-email" with body:
    """
    {
      "old_email": "<EMAIL>",
      "new_email": "<EMAIL>"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And I add "Content-Type" header equal to "application/json"
    And I send a "POST" request to "/api/v1/customer/ask-change-email" with body:
    """
    {
      "old_email": "<EMAIL>",
      "new_email": "<EMAIL>"
    }
    """
    Then  the response status code should be 401

  Scenario: Fails missing a required param
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And I add "Content-Type" header equal to "application/json"
    And I send a "POST" request to "/api/v1/customer/ask-change-email" with body:
    """
    {
      "old_email": "<EMAIL>"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And I expect RPC call to "bo-cms" "customer:ask_change_email" to respond with:
    """
    {
      "status": true
    }
    """
    And I add "Content-Type" header equal to "application/json"
    And I send a "POST" request to "/api/v1/customer/ask-change-email" with body:
    """
    {
      "old_email": "<EMAIL>",
      "new_email": "<EMAIL>"
    }
    """
    Then  the response status code should be 204

  Scenario: Test valid authorization
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I expect RPC call to "bo-cms" "customer:ask_change_email" to respond with:
    """
    {
      "status": true
    }
    """
    And I add "Content-Type" header equal to "application/json"
    And I send a "POST" request to "/api/v1/customer/ask-change-email" with body:
    """
    {
      "old_email": "<EMAIL>",
      "new_email": "<EMAIL>"
    }
    """
    Then  the response status code should be 204
