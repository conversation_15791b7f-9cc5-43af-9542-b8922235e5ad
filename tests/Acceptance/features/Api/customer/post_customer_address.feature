Feature: Create a customer address

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/12/address"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/12/address"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer_addresses:create" to respond with:
   """
    {
      "result": "success",
      "address": {
        "name": "PHC  Naoned",
        "company_name": "",
        "title": "Mr",
        "firstname": "<PERSON>",
        "lastname": "<PERSON><PERSON>d",
        "address": "38 rue de la ville en bois",
        "postal_code": "44000",
        "city": "NANTES",
        "country_code": "FR",
        "cellphone": "**********",
        "phone": ""
      },
      "index": 0,
      "action": "update",
      "status": true
    }
    """
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/12/address" with body:
    """
    {
      "address": {
        "postal_code" : "44240"
      }
    }
    """
    And   last RPC call service should be "bo-cms"
    And   last RPC call method should be "customer_addresses:create"
    # customer_id
    And   last RPC call args node "0" should be equal to the number "12"
    # address
    And   last RPC call args node "1" should be equal to the pystring
    """
      {"postal_code":"44240"}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 5 elements

  Scenario: Test creat address RPC fail
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer_addresses:create" to respond with:
    """
    {
      "status": false
    }
    """
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "post" request to "/api/v1/customer/12/address" with body:
    """
    {
      "address": {
        "name" : "mon adresse"
      }
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    fail to create address in CMS
    """