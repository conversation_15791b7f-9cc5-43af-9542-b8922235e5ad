Feature: Get list of task
  In order to manage tasks through API
  As a user
  I need to be able to get list of tasks

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "task/cpost_tasks.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/tasks"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/tasks"
    Then  the response status code should be 401

  Scenario: Get list of tasks
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/tasks" with body:
    """
    {
      "where": {
        "_and": [
          {
            "user_id": {
              "_eq": 1
            }
          }
        ]
      },
      "order_by": "task_id",
      "order_direction": "ASC"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->tasks" should have 2 elements
    # full test on the first task
    And   the JSON node "data->tasks[0]->task_id" should contain 44
    And   the JSON node "data->tasks[0]->customer_order_id" should contain 1
    And   the JSON node "data->tasks[0]->customer_id" should be null
    And   the JSON node "data->tasks[0]->parcel_id" should be null
    And   the JSON node "data->tasks[0]->parcel_number" should be null
    And   the JSON node "data->tasks[0]->article_id" should contain 1
    And   the JSON node "data->tasks[0]->sku" should contain "ARCAMRBLINKNR"
    And   the JSON node "data->tasks[0]->user_id" should contain 1
    And   the JSON node "data->tasks[0]->user_name" should contain "Seigneur ADMIN"
    And   the JSON node "data->tasks[0]->user_origin_id" should contain 1000
    And   the JSON node "data->tasks[0]->type_id" should contain 20
    And   the JSON node "data->tasks[0]->created_at" should contain "2018-01-05 15:06:08"
    And   the JSON node "data->tasks[0]->limited_at" should contain "2018-01-06 15:06:08"
    And   the JSON node "data->tasks[0]->finished_at" should contain "2018-09-20 12:19:29"
    And   the JSON node "data->tasks[0]->updated_at" should contain "2018-01-05 15:06:08"
    And   the JSON node "data->tasks[0]->is_expired" should be true
    And   the JSON node "data->tasks[0]->subject" should contain "Commentaire commande"
    And   the JSON node "data->tasks[0]->comment" should be null
    And   the JSON node "data->tasks[0]->description" should contain "Carte cadeau installation"
    And   the JSON node "data->tasks[0]->supplier_order_id" should be null
    And   the JSON node "data->tasks[0]->carrier_id" should be null
    And   the JSON node "data->tasks[0]->customer_order_id_from_delivery_note" should be null
    And   the JSON node "data->tasks[0]->customer_id_from_customer_order" should be equal to the number "1"
    And   the JSON node "data->tasks[0]->sent_by" should contain "Seigneur ADMIN"
    # description is properly taken from linked data in the second case
    And   the JSON node "data->tasks[1]->description" should contain "Un test de commentaire"
    And   the JSON node "data->tasks[1]->description" should contain "sur plusieurs"
    And   the JSON node "data->tasks[1]->description" should contain "lignes"
