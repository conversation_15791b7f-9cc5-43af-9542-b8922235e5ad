Feature: Remit a transaction on a customer order
  In order to remit an accepted transaction
  As a user
  I need to be able to submit a form with the required data

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "customer_order_payment/put_customer_order_payment_remit.sql"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit"
    Then  the response status code should be 401

  Scenario: Attempt to remit on a non existing transaction
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit" with body:
    """
    {
      "payments": [
        {
          "customer_order_payment_id": "1"
        }
      ]
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Could not load payment(s).
    """

  Scenario: Remit a simple transaction with a remit proof successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit" with body:
    """
    {
      "payments": [
        {
          "customer_order_payment_id": "3577209",
          "amount_to_remit": "249",
          "remit_proof": "1234568"
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should exist
    And   the JSON node "data->payments" should have 1 element
    And   the JSON node "data->payments[0]->customer_order_payment_id" should be equal to "3577209"
    And   the JSON node "data->payments[0]->can_be_remitted" should be false

  Scenario: Remit a simple transaction successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit" with body:
    """
    {
      "payments": [
        {
          "customer_order_payment_id": "3577208",
          "amount_to_remit": "155"
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should exist
    And   the JSON node "data->payments" should have 1 element
    And   the JSON node "data->payments[0]->customer_order_payment_id" should be equal to "3577208"
    And   the JSON node "data->payments[0]->can_be_remitted" should be false

  Scenario: Remit a simple transaction without any amount successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit" with body:
    """
    {
      "payments": [
        {
          "customer_order_payment_id": "3577208"
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should exist
    And   the JSON node "data->payments" should have 1 element
    And   the JSON node "data->payments[0]->customer_order_payment_id" should be equal to "3577208"
    And   the JSON node "data->payments[0]->can_be_remitted" should be false

  Scenario: Attempt to remit a non remittable transaction
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit" with body:
    """
    {
      "payments": [
        {
          "customer_order_payment_id": "3577209",
          "amount_to_remit": "2398.7"
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should exist
    And   the JSON node "data->payments" should have 1 element
    And   the JSON node "data->payments[0]->customer_order_payment_id" should be equal to "3577209"
    And   the JSON node "data->payments[0]->remit_error" should be equal to "La transaction ne peut être remisée"

  Scenario: Remit two payments successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/remit" with body:
    """
    {
      "payments": [
        {
          "customer_order_payment_id": "3574971"
        },
        {
          "customer_order_payment_id": "3575198"
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should exist
    And   the JSON node "data->payments" should have 2 elements
    And   the JSON node "data->payments[0]->customer_order_payment_id" should be equal to "3574971"
