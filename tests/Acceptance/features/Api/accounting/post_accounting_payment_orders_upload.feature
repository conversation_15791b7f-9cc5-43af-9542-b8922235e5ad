Feature: Upload a csv with orders to import and/or remit

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order/cpost_customer_order_payments.sql"
    And   I send a "POST" request to "/api/v1/accounting/payment/orders_upload"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/accounting/payment/orders_upload"
    Then  the response status code should be 401

  Scenario: Check response when no data has been provided
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounting/payment/orders_upload"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    "remit_file" key name was not found on the request payload.
    """

  Scenario: Check response when a csv has been successfully uploaded
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And I send a POST request to "/api/v1/accounting/payment/orders_upload" with parameters:
      | key  | value    |
      | remit_file | @../../fixtures/files/240911_BOULANGER.csv |
      | remit_payments | 0 |
      | type | CSV |
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->orders_not_found" should exist
    And   the JSON node "data->orders" should exist
    And   the JSON node "data->orders[0]" should be equal to the number 2
    And   the JSON node "data->payments" should exist
    And   the JSON node "data->total" should exist

  Scenario: Check response when a csv has incomplete headers
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And I send a POST request to "/api/v1/accounting/payment/orders_upload" with parameters:
      | key  | value    |
      | remit_file | @../../fixtures/files/240915_FNAC.csv |
      | remit_payments | 1 |
      | type | CSV |
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    CSVDataExtractor: The file header is invalid.
    """
