Feature: Retrieve a filtered list of shipments
  As someone identified in the ERP
  I want to list and filter the shipments available in the system

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "shipment/cpost_shipments.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipments"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipments"
    Then  the response status code should be 401

  Scenario: Test without premission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/shipments"
    Then  the response status code should be 403

  Scenario: Get list of shipments without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipments"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->shipments" should have 3 elements

  Scenario: Get list of shipments paginated
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipments" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->shipments" should have 1 elements

  Scenario: Get shipments with all columns
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipments" with body:
    """
    {      
      "limit": 2,
      "included_dependencies": ["shipment_delivery_notes"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "2"
    And   the JSON node "data->shipments" should have 2 elements
    And   the JSON node "data->shipments[0]" should have the following keys and values
    """
    {
      "shipment_id": 9018,
      "carrier_id": 20,
      "carrier_name": "DHL",
      "shipment_account": "EDNDHL",
      "slip_number": 889,
      "created_at": "2020-06-24 12:21:23",
      "closed_at": "2020-06-25 12:21:23",
      "status": 1,
      "parcel_quantity": 3,
      "shipment_delivery_notes": [
        {
          "delivery_note_id": 4403190,
          "status": 0,
          "have_all_tracking_numbers": true
        },
        {
          "delivery_note_id": 4403189,
          "status": 1,
          "have_all_tracking_numbers": true
        },
        {
          "delivery_note_id": 4403188,
          "status": 2,
          "have_all_tracking_numbers": false
        }
      ]
    }
    """

    

 Scenario: Get filtered shipments
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipments" with body:
    """
    {      
      "where": {
        "with_delivery_note": {
          "_eq": "4403190"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"


