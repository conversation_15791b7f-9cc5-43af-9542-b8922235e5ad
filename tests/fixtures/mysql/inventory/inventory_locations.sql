INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation, allowed_inventory_types)
VALUES
    (3, 'Champigny 3', 5, 1, 1, 1, 1, 1, '314 rue du Pr <PERSON>z', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur <PERSON>', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', null, 'Ch2', JSON_ARRAY('full')),
    (4, 'Champigny 4', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '04', null, 'Ch2', JSON_ARRAY('full', 'partial'))
;


INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (4, '03.04.c.02.01.01', 2, '03.04.C$02.01.01', 1),
    (5, '03.04.d.02.01.01', 2, '03.04.D$02.01.01', 1),
    (6, '03.04.e.02.01.01', 2, '03.04.E$02.01.01', 1),
    (7, '03.04.b.02.01.01', 2, '03.04.B$02.01.01', 1)
;

INSERT INTO backOffice.BO_INV_zone_location (zone_id, location_id)
VALUES
    (5, 7),
    (5, 4),
    (5, 5),
    (5, 6)
;

INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id, inv_id_utilisateur_validation, statut, type, name)
  VALUES
  (2, '2020-08-08 11:38:32', NULL, 1, 3, NULL, 'created', 'full', null),
  (3, '2020-08-08 11:38:32', NULL, 3, NULL, NULL, 'created', 'partial', 'ce qui compte c''est le comptage'),
  (4, '2020-08-08 11:38:32', NULL, 3, NULL, NULL, 'on-going', 'partial', 'ce qui compte c''est le comptage'),
  (5, '2020-08-08 11:38:32', NULL, 3, 5, NULL, 'on-going', 'partial', null)
;

INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
VALUES
    (1, 1, 15435753311264, 81079, 'SONYVPLXW7000NR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 15435753311564, 81080, 'SONYVPLXW7000NB', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80)
;

INSERT INTO backOffice.BO_INV_collecte (id, BO_INV_inventaire_id, collecte_type, numero)
VALUES
  (3, 2, 'global', 1),
  (4, 2, 'historic', 2),
  (5, 5, 'global', 2)
;

INSERT INTO backOffice.BO_INV_differential (inventory_id, product_id, brand_id, buy_price, expected_quantity, expected_total_value, counted_quantity, counted_total_value, computed_stock_difference, computed_total_value_difference, is_validated_manually, is_validated_automatically)
VALUES
  (2, 81078, 262, 131.58, 11, 1052.64, 0, 0.00, 0, 0.00, 0, 0),
  (2, 81079, 262, 131.58, 8, 1052.64, 8, 0.00, 0, 0.00, 0, 1),
  (4, 81080, 262, 131.58, 9, 1052.64, 8, 0.00, 0, 0.00, 0, 0)
  ;


INSERT INTO backOffice.BO_INV_initial_state (inventory_id, product_id, location_id, quantity)
VALUES
    (2, 81078, 1, 11),
    (2, 81079, 1, 8),
    (4, 81080, 1, 8)
;

INSERT INTO backOffice.BO_INV_inventory_location (inventory_id, location_id, scanned_empty_at)
VALUES
    (4, 1, null),
    (4, 2, null),
    (5, 7, null),
    (5, 4, null),
    (5, 5, null),
    (5, 6, null),
    (5, 1, null),
    (5, 2, '2023-10-10')
;

INSERT INTO backOffice.BO_INV_collecte_article (id, BO_INV_collecte_id, id_produit, id_emplacement, sf_guard_user_id, quantite, quantite_stock, date_collecte)
VALUES
    (1, 5, 81078, 4, 1, 1, 1, '2023-10-10')
;
