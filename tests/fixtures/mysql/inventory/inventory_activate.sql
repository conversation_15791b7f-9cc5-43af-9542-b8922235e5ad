INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id, inv_id_utilisateur_validation, statut)
  VALUES
  (2, '2020-08-08 11:38:32', NULL, 1, 4, NULL, 'on-going'),
  (3, '2020-08-08 11:38:32', NULL, 2, null, NULL, 'created'),
  (4, '2020-08-08 11:38:32', NULL, 2, null, NULL, 'created'),
  (5, '2020-08-08 11:38:32', NULL, 2, 12, NULL, 'on-going')

;

INSERT INTO backOffice.BO_INV_collecte (id, BO_INV_inventaire_id, collecte_type, numero)
VALUES
  (3, 2, 'global', 1),
  (4, 2, 'produit', 2),
  (5, 2, 'produit', 3),
  (6, 3, 'global', 1),
  (7, 3, 'produit', 2),
  (8, 3, 'produit', 3),
  (9, 4, 'global', 1),
  (10, 4, 'produit', 2),
  (11, 4, 'produit', 3),
  (12, 5, 'global', 1),
  (13, 5, 'produit', 2),
  (14, 5, 'produit', 3)
;

INSERT INTO backOffice.BO_INV_differential (inventory_id, product_id, brand_id, buy_price, expected_quantity, expected_total_value, counted_quantity, counted_total_value, computed_stock_difference, computed_total_value_difference, is_validated_manually)
VALUES
  (2, 81078, 262, 131.58, 8, 1052.64, 0, 0.00, 0, 0.00, 0)
  ;
