INSERT INTO backOffice.paiement (id_paiement, moyen, actif, operation, avoir, paiement, description, surcout, attente_paiement, creation_distante, autorisation_distante, annulation_distante, demande_remise_distante, remboursement_distant, remise_directe, declaration_impaye, interrogation_etat_distante, statut_source, garantie_source, justificatif_creation, justificatif_creation_source, justificatif_creation_type, justificatif_acceptation, justificatif_acceptation_source, justificatif_acceptation_type, justificatif_acceptation_motif, justificatif_remise, justificatif_remise_source, justificatif_remise_type, justificatif_remise_motif, bon_remise, bon_remise_motif, compte_bancaire, journal, emport_depot)
  VALUES
  (2, 'CBS', 'N', 'paiement', 'N', 'CBOL', 'Carte de crédit en ligne sécurisée', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE', 'N'),
  (4, 'CHK', 'N', 'paiement,remboursement', 'N', 'Chèque', 'Chèque', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '^\\d{7}$', 'Y', 'manuel', 'No cheque', '^\\d{7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
  (5, 'AUR', 'N', 'paiement', 'N', 'Aurore', 'Carte Aurore', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (6, 'CRDT', 'N', '', 'N', 'Crédit personnalisé', 'Paiement en 3, 5, 10 fois ou plus', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (7, 'CR', 'N', 'paiement', 'N', 'CR', 'Paiement à la livraison', 10.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'No facture', 'Y', 'manuel', 'Confirmation CR', '^ok$', 'Y', 'manuel', 'No releve', '^\\d{1,64}$', 'N', '', '5121', 'CC', 'N'),
  (8, 'BKDO', 'Y', 'paiement', 'N', 'Bon Kado', 'Le Bon Kado', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (9, 'VIR', 'Y', 'paiement,remboursement', 'N', 'Virement', 'Virement bancaire', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'auto', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512010', 'BQ', 'Y'),
  (10, 'DIF3M', 'N', '', 'N', 'Différé 3 mois', 'Paiement différe à 3 mois (Réflexion 3)', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (11, 'CTPE', 'Y', 'paiement', 'N', 'CTPE', 'Carte terminal de paiement', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^(?:TPE-)?\\d{1,64}$', 'N', '', '512300', 'CE', 'Y'),
  (12, 'ESP', 'Y', 'paiement,remboursement', 'N', 'Espèces', 'Espèces', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', '', 'N', 'manuel', 'No cheque', '^[A-Z0-9]{9}$', 'N', 'manuel', 'No remise', '', 'N', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
  (13, 'MDT', 'N', '', 'N', 'Mandat', 'Mandat', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (15, 'TCMD', 'Y', 'remboursement', 'Y', 'Avoir (Transfert commande)', 'Avoir (Transfert commande)', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (16, 'RCRT', 'N', '', 'N', 'Recrédit carte', 'Recrédit sur carte', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (17, 'PANT', 'Y', 'paiement', 'Y', 'Avoir (Pmt antérieur)', 'Avoir (Paiement antérieur)', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (18, 'TIRG', 'Y', 'paiement', 'N', 'Tir Groupé', 'Tir Groupé', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '^[A-Z0-9]{12}$', 'Y', 'manuel', 'No cheque', '^[A-Z0-9]{12}$', 'N', '', '512000', 'BQ', 'Y'),
  (19, 'TRT30', 'N', 'paiement', 'N', 'Traite 30 j', 'Traite à 30 jours', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No SIREN', '^\\d{9}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512000', 'BQ', 'Y'),
  (20, 'CHKF', 'Y', 'paiement', 'N', 'Chèque à récept facture', 'Chèque à réception de facture', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No SIREN', '^\\d{9}$', 'Y', 'manuel', 'No cheque', '^\\d{7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
  (22, 'PSM', 'N', '', 'N', 'PSM', 'Paiement sur mobile', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (23, 'PMR', 'Y', 'remboursement', 'N', 'PriceMinister', 'PriceMinister', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande PriceMinister', 'Y', 'auto', 'No commande PriceMinister', '', 'Y', 'auto', 'No commande PriceMinister', '', 'N', '', '512000', 'BQ', 'N'),
  (24, 'SFCO', 'N', '', 'N', 'sofinco', 'crédit sofinco', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (25, 'SFCO2', 'N', '', 'N', 'Sofinco FdP', 'Sofinco Foire de Paris', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (26, 'AMZN', 'Y', 'remboursement', 'N', 'Amazon', 'commande passée sur Amazon', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Amazon', 'Y', 'auto', 'No commande Amazon', '', 'Y', 'auto', 'No commande Amazon', '', 'N', '', '512000', 'BQ', 'N'),
  (30, '1EC', 'N', 'paiement', 'N', '1euro.com', 'crédit 1euro.com', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '[0-9]{1,64}', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'N'),
  (31, 'EKDO', 'Y', 'paiement', 'N', 'eKDO', 'eKDO', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No cheque', '^\\d{7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'N'),
  (32, 'CGA', 'N', 'paiement', 'N', 'Cofinoga', 'Carte Cofinoga', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '[0-9]{1,64}', 'Y', 'auto', 'No remise', '[0-9]{1,64}', 'N', '', '512000', 'BQ', 'N'),
  (33, 'AMX', 'N', 'paiement', 'N', 'Amex', 'Carte Amex Spplus', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'AMX', 'N'),
  (34, 'MDTC', 'Y', 'paiement', 'N', 'Mandat Cash', 'Mandat Cash', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No mandat', '^\\w{1,64}$', 'N', 'manuel', 'No mandat', '^\\w{1,64}$', 'N', '', '5121', 'CC', 'N'),
  (35, 'MDTA', 'Y', 'paiement', 'N', 'Mandat Admin', 'Mandat Administratif', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No mandat', '^\\w{1,64}$', 'Y', 'manuel', 'No mandat', '^\\w{1,64}$', 'N', '', '512000', 'BQ', 'Y'),
  (36, 'CRTAT', 'N', 'paiement', 'N', 'CR TAT', 'Paiement à la livraison TAT', 10.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', '', 'Y', 'manuel', 'Confirmation CR', '^ok$', 'Y', 'manuel', 'No cheque', '^\\d{1,7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'N'),
  (37, '2XMC', 'N', 'remboursement', 'N', '2xMoinsCher', '2xMoinsCher', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande 2xMoinsCher', 'Y', 'auto', 'No commande 2xMoinsCher', '', 'Y', 'auto', 'No commande 2xMoinsCher', '', 'N', '', '512000', 'BQ', 'N'),
  (38, 'RAP', 'N', 'paiement', 'N', 'RAP', 'Kwixo Comptant', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'Y', 'manuel', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (39, 'RAPC', 'N', 'paiement', 'N', 'RAP', 'Kwixo Crédit', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'Y', 'manuel', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (40, 'CBS-P', 'N', 'paiement', 'N', 'CBOL-Paybox', 'Carte de crédit en ligne sécurisée Paybox', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Z0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Z0-9]{1,64}$', 'N', '', '512300', 'CE', 'N'),
  (41, 'AMX-P', 'N', 'paiement', 'N', 'Amex-Paybox', 'Carte American Express Paybox', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'AMX', 'N'),
  (42, 'AUR-P', 'N', 'paiement', 'N', 'Aurore-Paybox', 'Carte Aurore Paybox', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9\\-]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (43, 'CGA-P', 'N', 'paiement', 'N', 'Cofinoga-Paybox', 'Carte Cofinoga Paybox', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (44, 'PPAL-P', 'Y', 'paiement', 'N', 'Paypal-Paybox', 'Paypal Paybox', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^[A-Z0-9\\-]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9\\-]{1,64}$', 'N', '', '512020', 'PP', 'Y'),
  (45, 'EBAY', 'Y', 'remboursement', 'N', 'Ebay-Paypal', 'Ebay-Paypal', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Ebay', 'Y', 'manuel', 'No transaction', '^[A-Z0-9]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9]{1,64}$', 'N', '', '512020', 'PP', 'N'),
  (46, 'PIXM', 'Y', 'remboursement', 'N', 'Pixmania', 'Pixmania', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Pixmania', 'Y', 'auto', 'No commande Pixmania', '', 'Y', 'auto', 'No commande Pixmania', '', 'N', '', '512000', 'BQ', 'N'),
  (47, 'RUEDC', 'Y', 'remboursement', 'N', 'Rue du Commerce', 'Rue du Commerce', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Rue du Commerce', 'Y', 'auto', 'No commande Rue du Commerce', '', 'Y', 'auto', 'No commande Rue du Commerce', '', 'N', '', '512000', 'BQ', 'N'),
  (48, 'DCT', 'Y', 'remboursement', 'N', 'Décote', 'Décote', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (49, 'SVDCC', 'Y', 'paiement', 'N', 'Carte Cadeau SVD', 'Carte Cadeau Son-Vidéo.com', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No Carte', '^[0-9]{1,64}$', 'Y', 'manuel', 'No Carte', '^[0-9]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
  (50, 'TSVDCC', 'N', 'remboursement', 'Y', 'Avoir (Carte Cadeau SVD)', 'Avoir (Carte Cadeau SVD)', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (51, 'CHAVAS', 'N', 'paiement', 'N', 'Chèque Havas', 'Chèque Havas', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (52, 'ENOE', 'Y', 'paiement', 'N', 'Chèque Enfants Noë', 'Chèque Enfants Noë', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (53, 'CADHOC', 'N', 'paiement', 'N', 'Chèque CADHOC', 'Chèque CADHOC', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (54, 'BES', 'N', 'paiement', 'N', 'Chèque Bonus Euro System', 'Chèque Bonus Euro System', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (55, 'TTCR', 'N', 'paiement', 'N', 'Ticket Compliment Référence', 'Ticket Compliment Référence', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (56, 'TTCB', 'N', 'paiement', 'N', 'Ticket Compliment Bienvenue', 'Ticket Compliment Bienvenue', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (57, 'TTI', 'Y', 'paiement', 'N', 'Ticket Infini', 'Ticket Infini', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (58, 'TTH', 'Y', 'paiement', 'N', 'Ticket Horizon', 'Ticket Horizon', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
  (59, 'CBS-O', 'N', 'paiement', 'N', 'CBOL-Ogone-3DS', 'Carte de crédit en ligne sécurisée Ogone 3DS', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
  (60, 'AMX-O', 'N', 'paiement', 'N', 'Amex-Ogone', 'Carte American Express Ogone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'manuel', 'No remise', '^[A-Za-z0-9_\\/]{1,64}$', 'N', '', '512000', 'AMX', 'Y'),
  (61, 'AUR-O', 'N', 'paiement', 'N', 'Aurore-Ogone', 'Carte Aurore Ogone', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\-\\/]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\-\\/]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (62, 'CGA-O', 'N', 'paiement', 'N', 'Cofinoga-Ogone', 'Carte Cofinoga Ogone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (63, 'PPAL-O', 'N', 'paiement', 'N', 'Paypal-Ogone', 'Paypal Ogone', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'N', 'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^[A-Z0-9_\\-]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9_\\-\\.]{1,64}$', 'N', '', '512020', 'PP', 'Y'),
  (64, 'CBS-OT', 'Y', 'paiement', 'N', 'CBOL-Ogone-Tél', 'Carte de crédit en ligne sécurisée Ogone Téléphone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
  (65, 'AMX-OT', 'Y', 'paiement', 'N', 'Amex-Ogone-Tél', 'Carte American Express Ogone Téléphone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'manuel', 'No remise', '^[A-Za-z0-9_\\/]{1,64}$', 'N', '', '512000', 'AMX', 'Y'),
  (66, 'AUR-OT', 'Y', 'paiement', 'N', 'Aurore-Ogone-Tél', 'Carte Aurore Ogone Téléphone', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\-]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (67, 'CGA-OT', 'Y', 'paiement', 'N', 'Cofinoga-Ogone-Tél', 'Carte Cofinoga Ogone Téléphone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (68, 'PPAL', 'N', 'paiement', 'N', 'Paypal', 'Paypal', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^[A-Z0-9_\\-]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9_\\-\\.]{1,64}$', 'N', '', '512020', 'PP', 'Y'),
  (69, 'CBS-O2', 'N', 'paiement', 'N', 'CBOL-Ogone', 'Carte de crédit en ligne sécurisée Ogone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (70, 'EMPT', 'Y', 'paiement', 'N', 'Emport dépôt', 'Emport dépôt', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No commande', '^\\d{1,9}$', 'Y', 'manuel', 'No cheque', '^a2gH7em9p$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
  (71, 'PRESTO', 'Y', 'paiement', 'N', 'Cetelem Presto', 'crédit Cetelem Presto', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '[0-9]{1,64}', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'N'),
  (72, 'CBS-EMPT', 'Y', 'paiement', 'N', 'CBOL-Ogone-emport', 'Carte de crédit en ligne sécurisée Ogone - réservation emport', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'manuel', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
  (73, 'FNAC', 'Y', 'remboursement', 'N', 'Fnac', 'Fnac', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Fnac', 'Y', 'auto', 'No commande FNAC', '^[A-Z0-9]{1,64}$', 'Y', 'auto', 'No commande FNAC', '^[A-Z0-9]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
  (74, 'ECLNG', 'Y', 'paiement,remboursement', 'N', 'Ecranlounge', 'Ecranlounge', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Ecranlounge', 'Y', 'auto', 'No commande Ecranlounge', '', 'Y', 'auto', 'No commande Ecranlounge', '', 'N', '', '512000', 'BQ', 'N'),
  (75, 'TSAV', 'Y', 'remboursement', 'Y', 'Avoir (SAV)', 'Avoir (SAV)', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (76, 'RETANT', 'Y', 'paiement', 'N', 'Retour SAV anticipé', 'Retour SAV anticipé', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No SIREN', '', 'Y', 'manuel', 'No cheque', '', 'Y', '', '512000', 'BQ', 'Y'),
  (77, 'AMZNUK', 'Y', 'remboursement', 'N', 'Amazon UK', 'commande passée sur Amazon UK', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Amazon', 'N', 'manuel', 'No commande Amazon', '', 'Y', 'auto', 'No commande Amazon', '', 'N', '', '512000', 'UK', 'N'),
  (78, 'RDTE', 'Y', 'remboursement', 'N', 'Redoute', 'commande passée sur Redoute', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'auto', 'No commande Redoute', 'N', 'manuel', 'No commande Redoute', '', 'N', 'auto', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (79, '', 'Y', 'paiement', 'N', '', '', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'auto', 'No facture', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
  (80, 'CBS-COT', 'Y', 'paiement', 'N', 'CBOL-Ogone-COT', 'Carte de crédit en ligne sécurisée Ogone - réservation', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'manuel', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE', 'N'),
  (81, 'COT', 'Y', 'paiement', 'N', 'Demande de cotation', 'Demande de cotation', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No commande', '^\\d{1,9}$', 'Y', 'manuel', 'No cheque', '^a2gH7em9p$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'N'),
  (82, 'CTPE-PNF', 'Y', 'paiement', 'N', 'CTPE PNF', 'Carte terminal de paiement PNF', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512300', 'CE', 'Y'),
  (83, 'CTPE-AMX', 'Y', 'paiement', 'N', 'CTPE Amex', 'Carte terminal de paiement AMEX', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512000', 'BQ', 'Y'),
  (84, 'NXCB', 'Y', 'paiement', 'N', 'Cetelem NxCB', 'Paiement en 3 ou 4 fois par carte bancaire', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '[0-9]{1,64}', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'Y'),
  (85, 'NXCB-CB', 'Y', 'paiement', 'N', 'NxCB CB', 'Paiement CB du NxCB', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'Y'),
  (86, 'NXCB-CET', 'Y', 'paiement', 'N', 'NxCB Cetelem', 'Paiement Cetelem du NxCB', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'Y'),
  (87, 'NXCB-RMB', 'Y', 'remboursement', 'N', 'NxCB Remboursement', 'Remboursement de paiement NxCB', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'N'),
  (88, 'CDSCNT', 'Y', 'remboursement', 'N', 'Cdiscount', 'commande passée sur Cdiscount', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Cdiscount', 'Y', 'auto', 'No commande Cdiscount', '', 'Y', 'auto', 'No commande Cdiscount', '', 'N', '', '512000', 'BQ', 'N'),
  (89, 'DARTY', 'Y', 'remboursement', 'N', 'Darty', 'commande passée sur Darty', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Darty', 'Y', 'auto', 'No commande Darty', '', 'Y', 'auto', 'No commande Darty', '', 'N', '', '512300', 'BQ', 'N'),
  (90, 'FULLCB3X', 'N', 'paiement', 'N', 'FullCB 3x', 'Cetelem FullCB 3x', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No remise', '', 'N', '', '512000', 'BQ', 'Y'),
  (91, 'FULLCB4X', 'N', 'paiement', 'N', 'FullCB 4x', 'Cetelem FullCB 4x', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No remise', '', 'N', '', '512000', 'BQ', 'Y'),
  (92, 'CILO', 'Y', 'remboursement', 'N', 'Cilo', 'Commande chez Cilo', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande', 'Y', 'auto', 'No commande', '', 'Y', 'auto', 'No commande', '', 'N', '', '512000', 'BQ', 'N')
;

