-- DEBUG_QUERY
INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
VALUES
    (3, 'alain-terrieur', 'none', '', '', now(), now(), 1, 1),
    (4, 'alex-terrieur', 'none', '', '', now(), now(), 1, 1)
;

-- DEBUG_QUERY
INSERT INTO backOffice.sf_guard_user_profile (id, societe, site, usr_societe, titre, civilite, nom, prenom, email, poste, poste_sda, employe, signature)
VALUES
    (3, 'Son Video Distribution', 'Champigny', 1, 'Responsable Call Center', 'M.', 'Terrieur', 'Alain', '<EMAIL>', '6666', 1, 1, 'Le roi du téléphone'),
    (4, 'Son Video Distribution', 'Champigny', 1, 'Vendeur Call Center', '<PERSON>.', '<PERSON><PERSON><PERSON>', '<PERSON>', '<EMAIL>', '', 0, 1, 'Le roi du bateau')
;

INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES
    (0, 5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 7, 'CHRON', 'Chronopost', 'oui', 1, 0, 1, 'chronopost ; tarif national', '', 'France métropolitaine', 0.000, 30.000, '24 Heures', 0.000, 0, 'http://www.chronotrace.com/servletTransform?xslURL=%2Fapplications%2Fquicksuivi%2Fsuiviclient.xsl&xmlURL=%2FservletSuiviXML%3Flangue%3Dfr_FR%26listeNumerosLT%3D', '', 5),
    (0, 20, 'DHL', 'DHL', 'oui', 1, 15, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, 'http://www.dhl.fr/content/fr/fr/dhl_express/suivi_expedition.shtml?brand=DHL&AWB=', '', 1),
    (0, 25, 'MOREL', 'Mondial Relay', 'oui', 0, 52, 1, 'points relais', '', 'france', 0.000, 0.000, '0', 0.000, 0, 'https://www.mondialrelay.fr/suivi-de-colis/?NumeroExpedition=', '', 1)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type, mono_colis, spidy_tracking_number_mask)
VALUES
    (9, 20, 'I1', 'DHL Express 24h', 1, 'Livraison entre 1 et 5 jours (selon le pays) partout dans le monde.', 'expressiste', 0, null),
    (15, 7, '13', 'Chronopost livraison avant 13H', 1, 'Livraison J1 garantie partout en france.', 'expressiste', 1, null),
    (24, 25, 'RL', 'Relais L', 1, 'Livraison en relais, 30kg / 120cm / 0,125 m3 max par colis', 'messagerie', 1, null)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation)
VALUES
    (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
    (2, 'Champigny 2', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '04', 6100907, 'Cha')
;

UPDATE backOffice.sf_guard_user_profile SET warehouse_id = 1 WHERE id = 1;
UPDATE backOffice.sf_guard_user_profile SET warehouse_id = 2 WHERE id = 2;

INSERT INTO data_warehouse.seller_commission_context (seller_commission_context_id, description)
VALUES
    ('B2C_HIFI', 'Produit issue d''une commande B2C'),
    ('B2C_IMAGE', 'Produit issue d''une commande B2C - Image IT'),
    ('B2B', 'Produit issue d''une commande B2B'),
    ('TEAM_BONUS', 'Prime équipe par mois'),
    ('MANAGER_BONUS', 'Prime manager par mois')
;

INSERT INTO data_warehouse.monthly_seller_commission_retail_store_user (user_id, retail_store_id, computed_at, role, level)
VALUES
    (1, 1, '2022-07-01', 'RETAIL_STORE_MANAGER', 1),
    (2, 2, '2022-07-01', 'RETAIL_STORE_SELLER', 1),
    (3, 1, '2022-07-01', 'CALL_CENTER_MANAGER', 1),
    (4, 1, '2022-07-01', 'CALL_CENTER_SELLER', 1)
;

INSERT INTO data_warehouse.monthly_seller_commission_retail_store (retail_store_id, computed_at, turnover_tax_excluded, margin_tax_excluded, turnover_tax_excluded_b2c_commissionable, margin_tax_excluded_b2c_commissionable, turnover_objective)
VALUES
    (1, '2022-07-01', 134332.44, 22, 15, 11, 98340),
    (2, '2022-07-01', 1234.22, 25, 17, 13, 98360)
;

INSERT INTO data_warehouse.monthly_seller_commission_bonus (user_id, seller_commission_context_id, computed_at, amount, turnover_tax_excluded, turnover_tax_excluded_commissionable, margin_tax_excluded, margin_tax_excluded_commissionable, margin_rate_threshold)
VALUES
    (1, 'B2C_HIFI', '2022-07-01', 691, 60195, 44, 23032, 45, 36),
    (1, 'B2C_IMAGE', '2022-07-01', 0, 1905, 66, 297, 77, 20),
    (1, 'B2B', '2022-07-01', 102, 11787, 88, 3395, 99, 23),
    (1, 'MANAGER_BONUS', '2022-07-01', 250, NULL, NULL, NULL, NULL, NULL),
    (2, 'B2C_HIFI', '2022-07-01', 436, 37226, 37226, 14519, 14519, 36),
    (2, 'B2C_IMAGE', '2022-07-01', 0, 3131, 3131, 563, 563, 20),
    (2, 'TEAM_BONUS', '2022-07-01', 150, NULL, NULL, NULL, NULL, NULL),
    (3, 'B2C_HIFI', '2022-07-01', 318, 28908, 28908, 10613, 10613, 36),
    (3, 'B2C_IMAGE', '2022-07-01', 71, 9888, 9888, 2354, 2354, 20),
    (3, 'B2B', '2022-07-01', 0, 0, 0, 0, 0, 23),
    (3, 'MANAGER_BONUS', '2022-07-01', 616, NULL, NULL, NULL, NULL, NULL),
    (4, 'B2C_HIFI', '2022-07-01', 230, 20462, 20462, 7669, 7669, 36),
    (4, 'B2C_IMAGE', '2022-07-01', 0, 1740, 1740, 272, 272, 20),
    (4, 'TEAM_BONUS', '2022-07-01', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO data_warehouse.monthly_seller_long_duration_warranty_bonus (user_id, computed_at, amount, turnover_tax_excluded, quantity_warranty_sold, quantity_warranty_sold_commissionable, quantity_eligible_products, quantity_eligible_products_commissionable, attachment_rate_threshold)
VALUES
    (1, '2022-07-01', 150, 338, 4, 3, 38, 20, 15),
    (2, '2022-07-01', 0, 0, 0, 1, 28, 20, 15),
    (3, '2022-07-01', 0, 0, 4, 1, 16, 20, 15),
    (4, '2022-07-01', 0, 0, 0, 1, 19, 20, 15);

INSERT INTO data_warehouse.monthly_seller_commission_long_duration_warranty (user_id, computed_at, quantity_warranty_sold, quantity_warranty_sold_commissionable, quantity_eligible_products, quantity_eligible_products_commissionable, percent_warranty_sold, attachment_rate_min_threshold)
VALUES
    (1, '2022-07-01', 10, 9, 100, 70, 10, 10),
    (2, '2022-07-01', 10, 9, 100, 70, 10, 10),
    (3, '2022-07-01', 10, 9, 100, 70, 10, 10),
    (4, '2022-07-01', 10, 9, 100, 70, 10, 10);

INSERT INTO data_warehouse.monthly_seller_commission_bonus_catch_up (user_id, seller_commission_context_id, computed_at, catch_up_type, amount, turnover_tax_excluded, turnover_tax_excluded_commissionable, margin_tax_excluded, margin_tax_excluded_commissionable, margin_rate_threshold)
VALUES
    (1, 'B2C_IMAGE', '2022-07-01', 'three_months', 42.26, 12628.62, 789, 3138.41, 123, 20.00),
    (1, 'B2C_HIFI', '2022-07-01', 'three_months', 0.00, 61493.38, 123, 19981.88, 456, 36.00),
    (1, 'B2B', '2022-07-01', 'three_months', 37.84, 14006.25, 111, 3744.82, 222, 23.00),
    (1, 'MANAGER_BONUS', '2022-07-01', 'three_months', 12.34, null, null, null, null, null),
    (1, 'team_BONUS', '2022-07-01', 'three_months', 0.22, null, null, null, null, null)
;

INSERT INTO data_warehouse.monthly_seller_long_duration_warranty_bonus_catch_up (user_id, computed_at, catch_up_type, amount, turnover_tax_excluded, quantity_warranty_sold, quantity_warranty_sold_commissionable, quantity_eligible_products, quantity_eligible_products_commissionable, attachment_rate_threshold)
VALUES
    (1, '2022-07-01', 'three_months', 0.00, 0.00, 0, 0, 25, 15, 15.00)
;

INSERT INTO data_warehouse.monthly_seller_commission_long_duration_warranty_catch_up (user_id, computed_at, catch_up_type, amount, turnover_tax_excluded, quantity_warranty_sold, quantity_warranty_sold_commissionable, quantity_eligible_products, quantity_eligible_products_commissionable, percent_warranty_sold, attachment_rate_min_threshold)
VALUES
    (1, '2022-07-01', 'three_months', 0.00, 0.00, 0, 0, 25, 15, 15.00, 10)
;

INSERT INTO data_warehouse.monthly_seller_commission_retail_store_catch_up (retail_store_id, computed_at, catch_up_type, turnover_tax_excluded, margin_tax_excluded, turnover_tax_excluded_b2c_commissionable, margin_tax_excluded_b2c_commissionable, turnover_objective)
VALUES
    (1, '2022-07-01', 'three_months', 444444, 333333, 22222, 111111, 987000)
;

INSERT INTO data_warehouse.invoice_product (user_id, seller_commission_context_id, invoice_id, invoice_created_at, invoice_type, customer_order_id, article_id, turnover_tax_excluded, turnover_tax_excluded_commissionable, turnover_tax_excluded_commissionable_b2c, quantity, margin_tax_excluded, margin_tax_excluded_commissionable, margin_tax_excluded_commissionable_b2c, is_eligible_to_ldw, is_used_to_ldw_calculations, is_av_industry, ldw_amount_tax_excluded)
VALUES
    (1, 'B2C_HIFI', 1882656, '2022-07-24', 'facture', 2602065, 170138, 115.83, 115.83, 115.83, 1, 31.28, 31.28, 31.28, 0, 1, 0, 0.00),
    (2, 'B2C_HIFI', 1832347, '2022-07-15', 'facture', 2474716, 153949, 22.71, 0.00, 0.00, 1, 4.26, 0.00, 0.00, 0, 1, 0, 0.00),
    (5, 'B2C_HIFI', 1771129, '2023-03-20', 'facture', 2431160, 158174, 1424.17, 1424.17, 0.00, 1, 125.17, 125.17, 0.00, 1, 1, 0, 0.00)
;
