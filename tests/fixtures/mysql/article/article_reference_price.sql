-- insert dumb values for validate foreign keys integrity

INSERT INTO backOffice.pays (id_pays, code_postal_commentaire) VALUES
    (67, 'code_postal_commentaire')
;

INSERT INTO backOffice.couleur (id_couleur) VALUES
    (1)
;

INSERT INTO backOffice.marque (id_marque, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, public, type_distribution, avis, a_savoir, keyword, garanti, meta_description) VALUES
    (1, 'histoire', 'url_source_doc', 'url_source_image', 'specialite', 'produit_de_reference', 'public', 'type_distribution', 'avis', 'a_savoir', 'keyword', 'garanti', 'meta_description')
;

INSERT INTO backOffice.fournisseur (id_fournisseur, marque_disponible, marque_en_vente, commentaire) VALUES
    (1, 'marque_disponible', 'marque_en_vente', 'commentaire')
;

INSERT INTO backOffice.warranty_type (type, label, description) VALUES
    ('warranty_type', 'label', 'description')
;

INSERT INTO backOffice.CTG_TXN_domaine (id, meta_description) VALUES
    (1, 'meta_description')
;

INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, dft_domaine_id) VALUES
    (1, 1)
;

INSERT INTO backOffice.CTG_TXN_souscategorie (id, dft_categorie_id, warranty_type) VALUES
    (1, 1, 'warranty_type')
;

INSERT INTO backOffice.produit (id_produit, reference, V_id_categorie, id_souscategorie, V_id_domaine) VALUES 
    (1, 'TOTO', 1, 1, 1),
    (2, 'TITI', 1, 1, 1),
    (3, 'TATA', 1, 1, 1),    
    (4, 'TETE', 1, 1, 1)
;

INSERT INTO backOffice.article (id_produit, prix_vente, id_couleur, description_panier, description_courte, status) VALUES
   (1, 100.0, 1, 'description_panier', 'description_courte', 'oui'),
   (2, 200.0, 1, 'description_panier', 'description_courte', 'oui'),
   (3, 300.0, 1, 'description_panier', 'description_courte', 'oui'),
   (4, 400.0, 1, 'description_panier', 'description_courte', 'oui')
;

TRUNCATE backOffice.sales_channel_price_history;

-- order is important, simulates price recording over time
INSERT INTO backOffice.sales_channel_price_history (product_id, sales_channel_id, activated_at, selling_price) VALUES
    (1, 1, NOW() - INTERVAL 20 DAY, 50.0),
    (1, 1, NOW() - INTERVAL 10 DAY, 150.0),
    (1, 1, NOW(), 100.0),
    (2, 1, NOW() - INTERVAL 45 DAY, 150.0),
    (2, 1, NOW(), 200.0),
    (3, 1, '2024-01-01', 50.0),
    (3, 1, NOW() - INTERVAL 45 DAY, 100.0),
    (3, 1, NOW() - INTERVAL 15 DAY, 200.0),
    (3, 1, NOW(), 300.0),
    (4, 1, NOW() - INTERVAL 60 DAY, 500.0),
    (4, 1, NOW() - INTERVAL 50 DAY, 400.0)

;
