INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
    (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;

INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page, menu, comprendre_pour_choisir, meta_description, presentation)
VALUES
    (3, 1, 0, 'Haute-fidélité', 0, 'Audio Vidéo', 2, '/Conseil/Hifi/Hifi.html', 1, 1, '', null),
    (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', ''),
    (13, 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null),
    (15, 1, 0, 'Enceintes', 0, 'tmp', 3, '/Enceintes', 1, 1, '', '')
;

INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_2, garantie_5, garantie_5_bis, garantie_vc_1, garantie_vc_2, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
VALUES
    (59, 1, 0, 1, 0, 0, 1, 'Meubles et supports', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 15.00, 0.200, 'oui', '', '', '', 'marque', '1', 'home cinema', 'TV, DVD, Home Cinéma', 'Y', 0, '85299041', '/Rayons/Accessoires/MeubleHifi.html', 3760, 0, 0, '610', '8529904900'),
    (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 11, 9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080', '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080'),
    (137, 1, 1, 1, 1, 0, 1, 'Enceintes', '', 15, 'oui', 'oui', 0, 'non', 'non', 'non', 'non', 1, 0.00, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'Y', 17, null, '/Rayons/Hifi/Enceintes/CatEnceintes1.html', null, 0, 0, '613', '85182200'),
    (18, 1, 0, 1, 0, 0, 1, 'Câbles audio', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 7.90, 0.200, 'oui', '', '', '', 'marque', '1', 'câble', 'MP3, Audio portable, Hi-fi', 'N', null, '85444991', '/Rayons/Cables-audio.html', 118, 0, 1, '610', '85444991')
;

INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;

INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture, port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav, id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay, pixmania_segment_id, hors_gabarit, illustration, redoute_nomenclature_node)
VALUES
    (95, 1, 1, 'Enceintes encastrables', '/Rayons/HomeCinema/EnceintesAV/Inwall.html', 7.99, 0.200, 137, 1, 'MC-11226', '/Parts/Nav/NavR_EnceintesEncastrees.html', 3, 13, '93382', '3', 106, 0, 'http://www.son-video.com/images/dynamic/Enceintes_encastrables/articles/Artsound/ARTSFL101/Artsound-FL101_P_140.jpg', ''),
    (144, 1, 1, 'Pieds d''enceintes', '/Rayons/Accessoires/PiedsEnceinte.html', 9.99, 0.200, 59, 1, 'MC-5335', '/Parts/Nav/NavR_Pied_Enceinte.html', 3, 13, '137923', '3', 3764, 0, 'http://www.son-video.com/images/dynamic/Supports/articles/NorStone/NORSTSTYLUM2NR/NorStone-Stylum-2-Noir_P_180.jpg', ''),
    (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null, null, null, null, '79323', '1', 9630, 0, 'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg', null),
    (56, 1, 1, 'Câbles d''enceintes', '/Rayons/Cables/EspaceCable/CablesA_Enceintes.html', 5.99, 0.200, 18, 1, 'MC-4702', '/Parts/Nav/NavR_Cable_Enceintes.html', 3, 13, '137917', '1', 118, 0, 'http://www.son-video.com/images/dynamic/Cables_audio/composes/NORSTCL40010M/NorStone-CL400-Classic-2-x-4-mm2-10-m-_P_180.jpg', '')
;

INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
VALUES
    (1, 1, 154357533114364, 81078, 'ARCAMRBLINKNR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 617301053011139, 81123, 'LBCLD25BP', 'article', '2019-07-25 19:02:14', 144, 59, 6, 0.200, 0.838, 0.456, 147.82),
    (1, 1, 848803876758370, 128416, 'BWCCM74', 'article', '2018-06-19 16:48:33', 95, 137, 15, 0.200, 0.818, 0.450, 337.50),
    (1, 1, 57384874725141, 13895, 'NORSTCL81123M', 'compose', '2019-09-03 20:04:50', 56, 18, 6, 0.200, 1.214, 0.548, 20.33)
;

INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte, id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte, encours_maximum, encours_consomme, marque_disponible, marque_en_vente, siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax, siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse, siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile, commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile, comptabilite_email, technique_contact, technique_telephone, technique_mobile, technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen, frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape, hors_delai_auto, reliquat_attente_auto)
VALUES
    (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
    (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
    (943050784937153, 400, 'LA BOITE CONCEPT', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;

INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, gamme_qualite, public, type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif, prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis, V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES
    (864984545730971, 262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
    (720253785811601, 292, 'B&W', 'http://www.son-video.com/images/static/marques/Bowers_et_Wilkins.gif', 'oui', '', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'false', 27828, 246, 4.736, 239, 0, '', '', ''),
    (485762893674674, 959, 'La Boite Concept', 'http://www.son-video.com/images/static/marques/La-boite-concept.gif', 'oui', 'La Boite Concept', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
    (940157217849192, 520, 'NorStone', 'http://www.son-video.com/images/static/marques/Norstone.gif', 'oui', 'Inovadis', '', '', 'Eric', '', '', 5, '', '', '', '', 67, 0.000, 0.000, 'oui', 'true', 2789, 1451, 4.597, 1418, 0, 'norstone,câble,audio,vidéo,HDMI,meuble,hi-fi,TV,support,pieds,cablage,câblage', '', 'Tous les produits NorStone : câble enceinte, câble audio vidéo, HDMI, meuble hi-fi, meuble vidéo, meuble TV avec support, support TV mural et pieds d''enceinte.')
;

INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES
    (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41'),
    (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1, '2019-03-01 10:35:41')
;

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, V_delai_lvr_ferme, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main)
VALUES
    ('2019-09-03 01:06:38', 1, 0, 81078, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
    ('2019-07-26 00:34:00', 1, 0, 81123, '2013-02-21', 'oui', 195.00, 176.35, 0.00, 389.00, 389.00, 0.00, 0.00, 0.00, 8.450, 'N', 1, 1, 'N', null, 'LD-F-25mm-N-P', 400, 959, 'Pieds Noir laqué pour station HiFi LD120 / LD130', 'Boîte Concept Pieds LD120 LD130', 1, 0.00, 2, 0, 'a', null, 2, 0, 1, null, null, null, null, 0, 0, 0, 0, 0, 2, '', 'http://www.son-video.com/Rayons/Enceintes-Multimedia/La-Boite-Concept-pieds-LD120-LD130.html', 'http://www.son-video.com/images/dynamic/Enceintes/articles/La_Boite_Concept/LBCLD25BP/La-Boite-Concept-Pieds-pour-station-HiFi-LD120-Noir-laque_P_180.jpg', 'oui', 'oui', 'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130', 'Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)', 0, '', 0.000, 0.000, 0, 0, 0.00, 389.00, '2014-10-14', 67, '85182200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, '', 0, 23785814, null, null, null, 0, 0.0, 1, 0, 0, 0, 2, 0, 1, 1),
    ('2018-06-20 00:34:00', 1, 0, 128416, '2018-06-19', 'tmp', 412.50, null, 0.00, 900.00, 900.00, 0.00, 0.00, 0.00, 4.000, 'Y', 1, 1, 'N', null, null, 1, 292, 'BWCCM74', null, 1, 0.00, 0, 0, 'a', null, 0, null, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'non', 'Enceinte encastrable BW CCM 7.4', 'Arcam BW CCM', 0, '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1),
    ('2019-09-04 00:34:01', 1, 0, 13895, '2003-09-22', 'oui', 16.75, 16.75, 0.00, 45.00, 62.50, 0.50, 0.00, 0.00, 2.000, 'N', 1, 1, 'N', null, null, 1, 520, 'CL250 Classic 2,5 mm2 (25 m)', null, 1, 25.00, 235, 0, 'a', null, 183, 0, 1, null, null, null, null, 0, 0, 0, 0, 0, 0, '', 'http://www.son-video.com/Rayons/Cables/EspaceCable/CablesA-Enceintes-NorStone-CL250-Classic.html', 'http://www.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg', 'non', 'oui', 'Câble d''enceintes Norstone Audio CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 25 m', 'NorStone CL250 Classic (25 m)', 0, null, 0.000, 0.000, 0, 0, 0.00, 0.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 1, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 2, 0, 1, 1)
;

INSERT INTO backOffice.transporteur (id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES
    (1, 'IDFN', 'Indéfini', 'oui', 0, 999, 0, 'Indéfini', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1, 'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000, 0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', 'Au depart de champ', 5),
    (3, 'TAT', 'FedEx', 'oui', 0, 5, 1, 'Fedex domicile / domicile ; vers métropole', '', 'Europe', 0.000, 100.999, '24 heures', 0.000, 0, 'https://france.fedex.com/te/webapp25?&trans=tesow350&action=recherche_complete&NUM_COLIS=', 'N° compte 175773010 pour fedex international', 5),
    (4, 'UPS', 'UPS', 'non', 0, 12, 0, 'Pour collecte internationnale', '', 'Monde', 0.000, 25.000, '24 heures', 0.000, 0, null, 'N° compte pour expe point relais :  000099AV56', 5),
    (5, 'EMPT', 'Emport Champigny', 'oui', 0, 1, 1, 'Emport Depot Champigny', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (6, 'LVR', 'Livraison', 'oui', 0, 7, 0, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (7, 'CHRON', 'Chronopost', 'oui', 1, 0, 1, 'chronopost ; tarif national', '', 'France métropolitaine', 0.000, 30.000, '24 Heures', 0.000, 0, 'http://www.chronotrace.com/servletTransform?xslURL=%2Fapplications%2Fquicksuivi%2Fsuiviclient.xsl&xmlURL=%2FservletSuiviXML%3Flangue%3Dfr_FR%26listeNumerosLT%3D', 'N° Compte : 52520101', 5),
    (8, 'RECMD', 'Recommandé', 'non', 0, 30, 0, '', '0101010101', 'France metropolitaine', 0.000, 50.000, '15 jours', 0.000, 0, null, null, 5),
    (9, 'SRNM', 'Sernam', 'non', 0, 32, 0, 'Sernam', '', 'Europe', 0.000, 0.000, '24 heures', 0.000, 0, null, null, 5),
    (10, 'FDP', 'Emport FdP', 'non', 0, 34, 0, 'Emport Foire de Paris', '', 'Event', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (11, 'SCEXP', 'sicexpress', 'non', 0, 36, 0, 'sicexpress', '', 'france metropolitaine', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (12, 'EMAIL', 'Email', 'non', 0, 38, 0, 'Envoi par email', '', 'Europe', 0.000, 0.100, '24 heures', 0.000, 0, null, null, 5),
    (13, 'NOVEA', 'Novea', 'oui', 0, 3, 1, 'Novea', '', 'Europe', 0.000, 100.999, '24 heures', 0.000, 0, 'https://websuite.novea.fr/track.aspx?tr=', 'pour les courses  01.46.69.84.20 0', 5),
    (14, 'EDP8', 'Emport Depot Paris 8', 'oui', 0, 2, 0, 'Emport Depot Paris 8', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (15, 'SOCOL', 'So Colissimo', 'non', 0, 42, 0, 'So Colissimo', '', '', 0.000, 25.000, '0', 0.000, 0, null, null, 5),
    (16, 'SORDV', 'So Colissimo Rendez-vous', 'non', 0, 44, 0, 'So Colissimo Rendez-vous', '', 'France', 0.000, 25.000, '0', 0.000, 0, null, null, 5),
    (17, 'SOREL', 'So Colissimo Relais', 'non', 0, 46, 0, '?', '', '', 0.000, 25.000, '0', 0.000, 0, null, null, 5),
    (18, 'CALBR', 'CALBR', 'oui', 0, 23, 1, 'Calberson', '', 'France métropolitaine', 0.000, 100.999, '0', 0.000, 0, null, null, 5),
    (19, 'HAVRE', 'Le Havre', 'oui', 0, 50, 1, 'Expédition du havre', '', 'depart Havre', 0.000, 0.000, '0', 0.000, 0, null, 'http://espace.geodis.com/client/executeLogin.do#/PREPA.EXPE', 5),
    (20, 'DHL', 'DHL', 'oui', 1, 15, 1, 'international', '', 'Monde', 0.000, 0.000, '0', 0.000, 0, 'http://www.dhl.fr/content/fr/fr/dhl_express/suivi_expedition.shtml?brand=DHL&AWB=', 'N° compte droit et taxe : ***********', 5),
    (21, 'ANC', 'ANC', 'oui', 0, 19, 0, 'Anc transpsort maritime', '', 'DOM-TOM', 0.000, 0.000, '0', 0.000, 0, null, 'premier M3 indivisible.', 5),
    (22, 'SIFA', 'SIFA', 'oui', 0, 21, 0, 'Dom-tom maritime', '', 'Dom--tom', 0.000, 0.000, '0', 0.000, 0, null, 'premier M3 indivisible.', 5),
    (23, 'CPRIV', 'Colis Privé', 'non', 0, 13, 0, 'mono colis point relais', '', 'france metropole', 0.000, 0.000, '0', 0.000, 0, 'https://www.colisprive.com/moncolis/pages/detailColis.aspx?numColis=', null, 5),
    (24, 'TNT', 'TNT', 'non', 0, 17, 0, 'inter', '', 'Monde', 0.000, 30.000, '24/48 heures', 0.000, 0, null, null, 5),
    (25, 'MOREL', 'Mondial Relay', 'oui', 0, 52, 1, 'points relais', '', 'france', 0.000, 0.000, '0', 0.000, 0, 'https://www.mondialrelay.fr/suivi-de-colis/?NumeroExpedition=', 'http://connect.mondialrelay.com/YETI/Account/LogOn', 5),
    (26, 'DACH', 'DACHSER', 'non', 0, 0, 0, 'international', '', 'Monde', 0.000, 0.000, '4 semaines', 0.000, 0, null, 'Utilisé pour les imports chine.', 5),
    (27, 'SCHK', 'DB Schenker', 'non', 0, 0, 0, 'Au départ du déport schenker', '', 'France', 0.000, 0.000, '0', 0.000, 0, null, 'https://tracing.joyau.fr/Portail_Externe/', 5),
    (28, 'DIMOT', 'Dimotrans', 'non', 0, 0, 0, '?', '', '?', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (29, 'EVDN', 'Envoi du net', 'oui', 0, 0, 0, 'international mono colis', '', 'Monde', 0.000, 0.000, '0', 0.000, 0, null, 'http://www.envoidunet.com/fr', 5),
    (30, 'FREXP', 'France Express', 'oui', 0, 53, 1, 'Geodis / France Express', '', 'France', 0.000, 0.000, '48 heures', 0.000, 0, null, 'Compte n° : 056704', 5),
    (31, 'EDNA', 'Emport Depot Nantes', 'oui', 0, 2, 0, 'Emport Depot Nantes', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (32, 'EDLY', 'Emport Depot Lyon', 'oui', 0, 2, 0, 'Emport Depot Lyon', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (33, 'COLIB', 'COLIBOU', 'non', 0, 1, 0, 'livraison entre 20h et minuit paris', '', 'Paris', 0.000, 30.000, '0', 0.000, 0, null, '07 64 09 41 60', 5),
    (34, 'EDSG', 'Emport Dépôt St Germain en Laye', 'oui', 0, 2, 0, 'Emport Dépôt St Germain en Laye', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (35, 'CHNOE', 'Noel Chronopost ', 'non', 1, 0, 0, 'Chronopost tarif special noel', '', '', 0.000, 0.000, '0', 0.000, 0, null, 'N° compte 14352203', 5),
    (36, 'EDSA', 'Emport Dépôt Salon', 'oui', 0, 11, 1, 'Emport Dépôt Salon', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (37, 'EDLI', 'Emport Dépôt Lille', 'oui', 0, 2, 0, 'Emport Dépôt Lille', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (38, 'EDGR', 'Emport Dépôt Grenoble', 'oui', 0, 2, 0, 'Emport Dépôt Grenoble', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (39, 'ITINS', 'ITinSell', 'non', 0, 0, 0, 'Indemnisation colissimo', '', '', 0.000, 0.000, '0', 0.000, 0, null, 'https://bo.itinsell.com/#/FR/Accueil', 5),
    (40, 'EDAN', 'Emport Depot Antibes', 'oui', 0, 2, 0, 'Emport Depot Antibes', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (41, 'EDBO', 'Emport Depot Bordeaux', 'oui', 0, 2, 0, 'Emport Depot Bordeaux', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (42, 'EDMA', 'Emport Depot Marseille PDC', 'oui', 0, 2, 0, 'Emport Depot Marseille PDC', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (43, 'EDAV', 'Emport Depot Avignon', 'oui', 0, 2, 0, 'Emport Depot Avignon', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (44, 'EDP7', 'Emport Depot Paris 7', 'oui', 0, 2, 0, 'Emport Depot Paris 7', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (45, 'EDTO', 'Emport Depot Toulouse', 'oui', 0, 2, 0, 'Emport Depot Toulouse', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (46, 'EDMO', 'Emport Depot Montpellier', 'oui', 0, 2, 0, 'Emport Depot Montpellier', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (47, 'EDTH', 'Emport Depot Thonon-les-Bains', 'oui', 0, 2, 0, 'Emport Depot Thonon-les-Bains', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (48, 'PHCN', 'PHC Nantes', 'oui', 0, 11, 1, 'PHC Nantes', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (49, 'REF', 'Refacturation', 'oui', 0, 7, 0, 'Refacturation commande', '', '', 0.000, 0.000, '0', 0.000, 0, null, 'Refacturation commande', 5),
    (50, 'GLS', 'GLS', 'oui', 0, 0, 1, 'france metropole', '', 'France', 0.000, 0.000, '0', 0.000, 0, 'https://gls-group.eu/FR/fr/suivi-colis?match=', 'www.gls-group.eu', 5),
    (51, 'EDST', 'Emport Depot Strasbourg', 'oui', 0, 2, 0, 'Emport Depot Strasbourg', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (52, 'EDCH2', 'Emport Depot Champigny 2', 'oui', 0, 1, 1, 'Emport Depot Champigny 2', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (53, 'PHCCB', 'Champigny Boutique', 'oui', 0, 11, 1, 'Transporteur pour la boutique de Champigny', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 5),
    (54, 'TSVD', 'Transport SVD', 'oui', 0, 1, 1, 'Transport par Son-Vidéo', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, 'Utilisé pour les transports manuels réalisés par les équipes Son-Vidéo. Exemple : Transfert entre le dépôt et la boutique de Champigny.', 5)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type, mono_colis, spidy_tracking_number_mask)
VALUES
    (1, 2, '6C', 'Colissimo domicile', 1, 'Livraison sous 48/72h au domicile remis contre signature', 'messagerie', 1, null),
    (2, 2, '6J', 'mon espace cityssimo', 0, 'Livraison sous 48/72h en espace cityssimo remis contre signature', 'messagerie', 1, null),
    (3, 2, '6H', 'mon bureau de poste', 1, 'Livraison sous 48/72h au bureau de poste choisi remis contre signature', 'messagerie', 1, null),
    (4, 2, '6M', 'mon commerçant', 1, 'Livraison sous 48/72h au commerçant choisi remis contre signature', 'messagerie', 1, null),
    (5, 2, '6K', 'mon rendez-vous', 0, 'Livraison en soirée pour 3 euros de plus. Sur la Paris uniquement', 'messagerie', 1, null),
    (6, 2, 'CB', 'mon domicile avec signature', 1, 'Livraison sous 48/72h au domicile (Belgique) remis contre signature', 'messagerie', 1, null),
    (7, 2, 'CI', 'mon bureau de poste', 1, 'Livraison sous 48/72h au bureau de poste choisi (Belgique) remis contre signature', 'messagerie', 1, null),
    (8, 2, 'CM', 'mon commerçant', 1, 'Livraison sous 48/72h au commerçant choisi  (Belgique) remis contre signature', 'messagerie', 1, null),
    (9, 20, 'I1', 'DHL Express 24h', 1, 'Livraison entre 1 et 5 jours (selon le pays) partout dans le monde.', 'expressiste', 0, null),
    (10, 20, 'F1', 'DHL Domestic Express', 1, 'Livraison entre 1 et 5 jours en France.', 'expressiste', 0, null),
    (11, 3, 'H', 'FedEx@Home', 1, 'Livraison sur prise de rendez-vous', 'messagerie', 0, null),
    (12, 3, 'FE', 'FedEx Economy', 1, 'Livraison 24h/48h pour la belgique (monocolis)', 'expressiste', 1, null),
    (13, 13, 'EX', 'Express', 1, 'Livraison pour Ile de France', 'messagerie', 0, null),
    (14, 13, 'RE', 'Régulier', 1, 'Livraison hors Ile de france. A utiliser pour envoi à assurer disposant d''une grande valeur de marchandise.', 'messagerie', 0, null),
    (15, 7, '13', 'Chronopost livraison avant 13H', 1, 'Livraison J1 garantie partout en france.', 'expressiste', 1, null),
    (16, 7, 'CR', 'Chrono Relais', 1, 'Livraison J+1 en point relais', 'expressiste', 1, null),
    (17, 23, 'EA', 'Easy', 0, 'Livraison 48/72h au domicile', 'messagerie', 1, null),
    (18, 23, 'HZ', 'Hors Zone (Corse)', 0, 'Livraison 48/72h au domicile', 'messagerie', 1, null),
    (19, 21, 'AM', 'ANC Maritime', 1, 'Livraison par bateau pour la Guadeloupe, Martinique (2 semaines env.) et la Guyane (3 semaines env.).', 'messagerie', 0, null),
    (20, 22, 'SM', 'SIFA Maritime', 1, 'Livraison par bateau pour la Réunion et Mayotte et pour les TOM (4 semaines env.).', 'messagerie', 0, null),
    (21, 3, 'OP', 'Optimum', 1, 'Livraison sous 24h sans prise de rendez-vous. A utiliser pour les livraisons de sociétés.', 'messagerie', 0, null),
    (22, 2, 'EI', 'Expert Inter', 1, 'Livraison à l''international', 'messagerie', 1, null),
    (23, 2, 'OM', 'Export Outre-Mer', 1, 'Livraison dans tous les Dom-Tom', 'messagerie', 1, null),
    (24, 25, 'RL', 'Relais L', 1, 'Livraison en relais, 30kg / 120cm / 0,125 m3 max par colis', 'messagerie', 1, null),
    (25, 25, 'XL', 'Relais XL', 0, 'Livraison en relais, 50kg / 240cm / 0,5 m3 max par colis ', 'messagerie', 0, null),
    (26, 25, 'XX', 'Relais XXL', 0, 'Livraison en relais, 70kg / 240cm / 0,5 m3 max par colis', 'messagerie', 0, null),
    (27, 25, 'H1', 'Home 1', 0, 'Livraison sur rendez-vous sous 48/72h', 'messagerie', 0, null),
    (28, 24, 'TE', 'TNT Express', 0, 'Livraison sous 24/48h', 'expressiste', 1, null),
    (29, 19, 'HM', 'Calberson Messagerie', 1, 'Livraison sur rendez-vous depuis le dépôt du havre', 'messagerie', 0, '^[0-9a-zA-Z]{17}([0-9a-zA-Z]{8})'),
    (30, 19, 'HE', 'Calberson Express', 1, 'Livraison 24h sur la partie nord de la France depuis le dépôt du havre', 'messagerie', 0, '^[0-9a-zA-Z]{17}([0-9a-zA-Z]{8})'),
    (31, 5, 'ED', 'Emport Dépôt', 0, null, null, 0, null),
    (32, 14, 'EP', 'Emport Paris 8', 1, null, null, 0, null),
    (33, 6, 'LI', 'Livraison', 1, null, null, 0, null),
    (34, 30, 'XPK', 'Express', 1, 'France Express Livraison le lendemain partout en France. Vous pouvez changer le jour de livraison en répondant au mail/sms de notre transporteur.', 'messagerie', 0, null),
    (35, 30, 'ODX', 'Express', 1, 'France Express - 120 kg sur rendez-vous', 'messagerie', 0, null),
    (36, 30, 'CXI', 'Express', 1, 'France Express Livraison le lendemain partout en France. Vous pouvez changer le jour de livraison en répondant au mail/sms de notre transporteur.', 'expressiste', 0, null),
    (37, 31, 'ENA', 'Emport Nantes', 1, null, null, 0, null),
    (38, 32, 'ELY', 'Emport Lyon', 1, null, null, 0, null),
    (39, 33, 'SR', 'Livraison soir entre 20h00 - 22h / 22h00 - 00h00', 0, 'Livraison soir entre 20h00 - 22h / 22h00 - 00h00 sur rendez vous par mail.', 'messagerie', 1, null),
    (40, 7, 'FD', 'Fret Dom', 1, 'Chronopost Fret Dom', 'messagerie', 1, null),
    (41, 34, 'ESG', 'Emport Dépôt St Germain en Laye', 1, null, null, 0, null),
    (42, 7, '13N', 'Chrono 13N', 0, 'Contrat à tarif spécial pour fin d''année', 'expressiste', 1, null),
    (43, 35, 'NO', 'Chrono 13 Noel', 1, 'Livraison J1 garantie partout en France (Pendant Noel)', 'messagerie', 1, null),
    (44, 36, 'ESA', 'Emport Salon', 1, null, null, 0, null),
    (45, 37, 'ELI', 'Emport Lille', 1, null, null, 0, null),
    (46, 38, 'EGR', 'Emport Grenoble', 1, null, null, 0, null),
    (47, 40, 'EAN', 'Emport Dépôt Antibes', 1, null, null, 0, null),
    (48, 19, '6C', 'Colissimo Mon domicile', 1, 'Livraison sous 48/72h au domicile remis contre signature', 'messagerie', 1, null),
    (49, 19, '6H', 'Colissimo Mon bureau de poste', 1, 'Livraison sous 48/72h au bureau de poste choisi remis contre signature', 'messagerie', 1, null),
    (50, 19, '6M', 'Colissimo Mon commerçant', 1, 'Livraison sous 48/72h au commerçant choisi remis contre signature', 'messagerie', 1, null),
    (51, 19, 'C13', 'Chronopost 13', 1, 'Livraison J1 garantie partout en france.', 'expressiste', 1, null),
    (52, 19, 'RL', 'Relais L (Havre)', 1, 'Livraison en relais, 30kg / 120cm / 0,125 m3 max par colis', 'messagerie', 0, '^[0-9a-zA-Z]{2}([0-9a-zA-Z]{8})'),
    (53, 19, 'XL', 'Relais XL (Havre)', 0, 'Livraison en relais, 50kg / 240cm / 0,5 m3 max par colis ', 'messagerie', 0, '^[0-9a-zA-Z]{2}([0-9a-zA-Z]{8})'),
    (54, 19, 'XX', 'Relais XXL (Havre)', 0, 'Livraison en relais, 70kg / 240cm / 0,5 m3 max par colis', 'messagerie', 0, '^[0-9a-zA-Z]{2}([0-9a-zA-Z]{8})'),
    (55, 41, 'EBO', 'Emport Dépôt Bordeaux', 1, null, null, 0, null),
    (56, 42, 'EMA', 'Emport Dépôt Marseille PDC', 1, null, null, 0, null),
    (57, 43, 'EAV', 'Emport Dépôt Avignon', 1, null, null, 0, null),
    (58, 44, 'EP7', 'Emport Depot Paris 7', 1, null, null, 0, null),
    (59, 45, 'ETO', 'Emport Depot Toulouse', 1, null, null, 0, null),
    (60, 46, 'EMO', 'Emport Depot Montpellier', 1, null, null, 0, null),
    (61, 47, 'ETH', 'Emport Dépot Thonon-les-Bains', 1, null, null, 0, null),
    (62, 48, 'PHN', 'Emport PHC Nantes', 1, null, null, 0, null),
    (63, 51, 'EST', 'Emport Dépôt Strasbourg', 1, null, null, 0, null),
    (64, 19, 'DF', 'DHL (Havre)', 1, 'Livraison sur rendez-vous depuis le dépôt du havre', 'messagerie', 0, null),
    (65, 50, 'FDS', 'FlexDeliveryService', 1, 'Livraisons aux particuliers en France et sur certains pays d''Europe', 'messagerie', 1, null),
    (66, 19, 'FDH', 'FlexDeliveryService (Havre)', 1, 'Livraisons aux particuliers en France et sur certains pays d''Europe depuis le dépot du Havre', 'messagerie', 1, '^([0-9a-zA-Z]{8})'),
    (67, 19, 'CHR', 'Chronopost relais', 1, 'Livraison J+1 en point relais', 'expressiste', 1, null),
    (68, 7, 'CPR', 'Chrono Precise RDV', 1, 'Livraisons aux particuliers sur prise de RDV', 'expressiste', 1, null),
    (69, 7, 'CC', 'Chrono Classic', 1, 'Chrono Classic', 'messagerie', 1, null),
    (70, 19, 'CHP', 'Chrono Precise RDV', 1, 'Livraisons aux particuliers sur prise de RDV', 'expressiste', 1, null),
    (71, 19, 'CHC', 'Chrono Classic', 1, 'Chrono Classic', 'messagerie', 1, null),
    (72, 52, 'EC2', 'Emport Dépôt Champigny 2', 1, null, null, 0, null),
    (73, 53, 'ECB', 'Emport Champigny Boutique', 0, null, 'messagerie', 1, null),
    (74, 54, 'SVD', 'Transport SVD', 1, 'Transport manuel par les équipes Son-Vidéo', null, 0, null),
    (99, 1, 'COT', 'Cotation', 1, 'Cotation de transport', null, 0, null),
    (100, 49, 'REF', 'Refacturation', 1, 'Refacturation commande', null, 0, null)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation)
VALUES
    (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
    (2, 'Paris 8e', 14, 0, 0, 0, 0, 0, '1 Avenue de Friedland', '75008', 'Paris', 67, '0155091888', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Paris 8e)', '1 avenue de Friedland', 'Paris 8e', 'https://goo.gl/maps/jvuXoxj8gms', 'ouvert du lundi au vendredi, 10h - 19h', 99, 'du lundi au vendredi', 'de 10h &agrave; 19h', null, 1, 1, '02', 6230679, 'Par8'),
    (3, 'Havre', null, 1, 1, 0, 0, 0, 'RD 910 Le Montcriquet', '76210', 'Saint Jean de la Neuville', 67, '0155091830', '<EMAIL>', 1, null, null, null, null, null, 'RD 910 Le Montcriquet', null, null, null, 2, null, null, null, 1, 1, '01', null, 'Hav'),
    (4, 'Schenker', null, 0, 0, 0, 0, 0, 'Parc du Hode route industrielle BP263', '76430', 'St Vigor d''Ymonville', 67, '0155091080', '<EMAIL>', 1, null, null, null, null, null, 'Parc du Hode route industrielle BP263', null, null, null, 99, null, null, null, 0, 0, '04', null, 'Sch'),
    (5, 'Nantes', 31, 0, 0, 0, 0, 0, '9 place de la Bourse', '44100', 'Nantes', 67, '0249442402', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Nantes)', '9 place de la Bourse', 'Nantes', 'https://goo.gl/maps/Cucfvro99n22', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi', 'de 10h &agrave; 19h', null, 1, 1, '05', 6297732, 'Nan')
;

UPDATE backOffice.sf_guard_user_profile
SET warehouse_id = 2 WHERE id=1;

UPDATE backOffice.BO_STK_produit_depot
SET quantite_stock = 25
WHERE id_depot = 1
  AND id_produit = 81078;

UPDATE backOffice.BO_STK_produit_depot
SET quantite_stock = 25
WHERE id_depot = 3
  AND id_produit = 81078;

-- ADD CUSTOMERS
INSERT INTO backOffice.prospect (id_prospect, identifiant, mot_passe, mot_passe_crypte, date_creation, date_modification, prescripteur, origine, origine_date, cnt_type, cnt_email, cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax, cnt_numero_tva, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite, cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville, cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile, cnt_lvr_fax, cnt_lvr_numero_tva, site_web, blacklist, date_naissance, profession_id, envoi_email, email, type, societe, civilite, nom, prenom, envoi_identifiants, encours_interne, encours_sfac, id_mode_paiement, classification, acceptation_relicat, franco, RIB, nom_banque, ville_banque, BIC, IBAN, atradius, siren, incoterm, is_premium, passion, installation, style_musique, genre_cinema, musiques_preferees, cinemas_preferes, npai)
VALUES
    (1500003, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:15:03', '2019-09-03 08:32:21', null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', 0, 0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null, null, 0)
;

-- ADD CUSTOMER ORDERS
INSERT INTO backOffice.commande (trigger_actif, trigger_actif2, id_commande, no_commande_origine, creation_origine, date_creation, id_boutique, vendeur, flux, V_statut_traitement, en_attente_de_livraison, facturation_mode, ip, ip_pays, validite_fianet, rappel_client, emport_depot, lvr_particulier, lvr_assurance, detaxe_export, cmd_intragroupe, id_prospect, id_devis, commentaire_facture, expedition_diff_date, cloture_date, cloture_usr, tradedoubler_id, nombre_visite, clef, date_export_status, compteur_paiement, cnt_fct_type, cnt_fct_email, cnt_fct_societe, cnt_fct_civilite, cnt_fct_nom, cnt_fct_prenom, cnt_fct_adresse, cnt_fct_code_postal, cnt_fct_ville, cnt_fct_id_pays, cnt_fct_telephone, cnt_fct_telephone_bureau, cnt_fct_mobile, cnt_fct_fax, cnt_fct_numero_tva, cnt_fct_no_tva_validite, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite, cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville, cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile, cnt_lvr_fax, cnt_lvr_numero_tva, V_montant_ttc, V_montant_ht, V_trcns_montant, V_trcns_montant_accepte, V_trcns_montant_remise, V_pmts_montant, V_pmts_montant_accepte, V_pmts_montant_remise, V_rmbts_montant, V_rmbts_montant_accepte, V_rmbts_montant_remise, V_date_lvr_prevue_max, email_confirmation, sms_confirmation, promotion_id, entete_svd, emport_depot_paris, rdv_socol, id_transporteur, id_pdt_transporteur, tpt_option_code, atradius, relance_compta, id_commande_mere, refacturation, depot_emport, sms_emport, last_modified_at, sales_channel_id)
VALUES
    (1, 1, 1712826, '1', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null, 'traitement', 'trcn_acceptation_directe', 1, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non', 0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99, 590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null, 0, 0, null, 0, null, null, '2019-09-26 11:18:33', 1),
    (1, 1, 1712827, '2', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null, 'traitement', 'trcn_acceptation_directe', 0, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non', 0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99, 590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null, 0, 0, null, 0, 3, null, '2019-09-26 11:18:33', 1),
    (1, 1, 1712828, '3', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null, 'traitement', 'trcn_acceptation_directe', 1, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non', 0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99, 590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null, 0, 0, null, 0, null, null, '2019-09-26 11:18:33', 1),
    (1, 1, 1712829, '4', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null, 'traitement', 'trcn_acceptation_directe', 1, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non', 0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99, 590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null, 0, 0, null, 0, null, null, '2019-09-26 11:18:33', 1)
;

INSERT INTO backOffice.produit_commande (id, id_commande, id_produit, id_unique, tva, quantite, prix_vente, prix_achat, prix_achat_nc, description, prix_ecotaxe, duree_garantie_ext, prix_garantie_ext, tva_garantie_ext, commission_garantie_ext, tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc, remise_type, remise_montant, remise_description, groupe_type, groupe_description, id_bon_livraison, reservation, prix_sorecop)
VALUES
    (10, 1712826, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
    (11, 1712827, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, 'Y', null),
    (12, 1712828, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
    (13, 1712829, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
    (14, 1712829, 81123, 2, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null)
;

-- [TRANSFERT] Must be created in "tmp" status due to trigger constraint
INSERT INTO backOffice.BO_STK_transfert (id, id_depot_depart, id_depot_arrivee, id_commande, date_cloture, utilisateur_creation, statut, id_pdt_transporteur, date_creation)
VALUES
    (100, 3, 2, null, null, 'admin', 'tmp', 34, '2050-08-08'),
    (101, 1, 2, null, null, 'admin', 'tmp', 34, '2050-08-08'),
    (102, 1, 2, null, null, 'admin', 'tmp', 34, '2050-08-08'),
    (103, 1, 5, null, null, 'admin', 'tmp', 34, '2050-08-08'),
    (104, 3, 1, null, null, 'admin', 'tmp', 29, '2050-08-08'),
    (105, 3, 1, null, null, 'admin', 'tmp', 29, '2050-08-08');

-- [TRANSFERT] Then update the status after the row is inserted
UPDATE backOffice.BO_STK_transfert SET statut = 'au depart' WHERE id = 100;
UPDATE backOffice.BO_STK_transfert SET statut = 'cloture' WHERE id = 101;
UPDATE backOffice.BO_STK_transfert SET statut = 'expedie' WHERE id = 103;
UPDATE backOffice.BO_STK_transfert SET statut = 'au depart' WHERE id = 104;
UPDATE backOffice.BO_STK_transfert SET statut = 'cloture' WHERE id = 105;

-- [TRANSFERT] ADD PRODUCTS
INSERT INTO backOffice.BO_STK_produit_transfert (id_transfert, id_produit, quantite, quantite_livree, ajouter_au_bl)
VALUES
    (100, 81078, 1, 0, 1),
    (101, 81078, 2, 0, 1),
    (102, 81078, 4, 0, 1),
    (103, 81078, 4, 1, 1),
    (104, 81078, 5, 0, 1),
    (105, 81078, 7, 0, 1)
;

-- ADD DELIVERY NOTES
INSERT INTO backOffice.bon_livraison (id_bon_livraison, id_depot, id_commande, id_transfert, id_facture, is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation, status, detaxe_export, id_transporteur, id_pdt_transporteur, date_export_transporteur, utilisateur_export_transporteur, date_validation, utilisateur_validation, email_validation, sms_validation, numero_enquete, date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition, impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax, cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd, scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni, numero_kbis, numero_dbc)
VALUES
    (789, 1, 1712826, 100, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'expedie', 'non', 2, 1, '2019-08-29 11:41:10', 'admin', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null),
    (790, 1, 1712829, null, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'expedie', 'non', 2, 1, '2019-08-29 11:41:10', 'admin', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null)
;

UPDATE backOffice.produit_commande pc SET pc.id_bon_livraison = 790 WHERE pc.id = 13;

INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite)
VALUES
    (100, 790, 81078, 1, 'partial delivery', 1)
;

-- ADD SUPPLIER ORDERS
INSERT INTO backOffice.commande_fournisseur (id_commande_fournisseur, id_fournisseur, id_depot, date_creation, status, id_paiement_fournisseur, id_delai_paiement_fournisseur, escompte, commentaire)
VALUES
    (1, 162, 1, '2050-06-06', 'annulee', 1, 1, 'non', ''),
    (2, 162, 1, '2050-06-06', 'en cours', 1, 1, 'non', ''),
    (3, 162, 1, '2050-06-06', 'en preparation', 1, 1, 'non', ''),
    (4, 162, 1, '2050-06-06', 'en cours', 1, 1, 'non', ''),
    (5, 162, 3, '2050-06-06', 'en cours', 1, 1, 'non', '')

;

INSERT INTO backOffice.produit_commande_fournisseur (id, id_commande_fournisseur, id_produit, tva, quantite_commandee, quantite_livree, prix_achat, date_livraison_prevue, date_livraison_effective)
VALUES
    (1, 1, 81078, 0.200, 1, 1.00, 48.54, '2050-06-06', null),
    (2, 2, 81078, 0.200, 1, 0, 48.54, '2050-06-06', '2050-06-06'),
    (3, 3, 81078, 0.200, 6, 3, 48.54, '2050-06-06', '2050-06-06'),
    (4, 4, 81078, 0.200, 2, 2, 48.54, '2050-06-06', '2050-06-06'),
    (5, 5, 81078, 0.200, 10, 2, 48.54, '2050-06-06', '2050-06-06') -- date_livraison_prevue will be overwritten by livraison_produit_commande_fournisseur's trigger
;


INSERT INTO backOffice.livraison_produit_commande_fournisseur (id, id_produit_commande_fournisseur, date_livraison_prevue, quantite_livraison_prevue)
VALUES
    (10, 5, '2050-01-15', 2), -- delivered
    (11, 5, '2011-01-01', 4), -- default date
    (12, 5, '2050-01-19', 3)  -- to be delivered
    -- 1 product w/o delivery planned
;
