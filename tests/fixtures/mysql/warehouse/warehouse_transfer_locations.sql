-- Data dependencies
INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES
    (20030702191206, 5, 'EMPT', 'Emport Champigny', 'oui', 0, 1, 1, 'Emport Depot Champigny', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 14, 'EDP8', 'Emport Depot Paris 8', 'oui', 0, 2, 0, 'Emport Depot Paris 8', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 31, 'EDNA', 'Emport Depot Nantes', 'oui', 0, 2, 0, 'Emport Depot Nantes', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 32, 'EDLY', 'Emport Depot Lyon', 'oui', 0, 2, 0, 'Emport Depot Lyon', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 34, 'EDSG', 'Emport Dépôt St Germain en Laye', 'oui', 0, 2, 0, 'Emport Dépôt St Germain en Laye', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 36, 'EDSA', 'Emport Dépôt Salon', 'oui', 0, 11, 1, 'Emport Dépôt Salon', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 37, 'EDLI', 'Emport Dépôt Lille', 'oui', 0, 2, 0, 'Emport Dépôt Lille', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 38, 'EDGR', 'Emport Dépôt Grenoble', 'oui', 0, 2, 0, 'Emport Dépôt Grenoble', '', 'SVD MAG', 0.000, 0.000, '0', 0.000, 0, null, null, 1)
;

INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
VALUES
    (1000, 'backoffice', 'none', '', '', now(), now(), 1, 1)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation, use_auto_picking, fiche_url, fiche_url_courte)
VALUES
    (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au vendredi, 9h/13h - 14h/18h', 100, 'du lundi au vendredi', 'de 9h &agrave; 13h et de 14h &agrave; 18h.', '', 0, 0, '03', 6100907, 'Cha', 0, 'https://www.son-video.com/point-retrait/champigny', 'http://sv.hn/champigny'),
    (2, 'Paris 8e', 14, 0, 0, 0, 0, 0, '1 Avenue de Friedland', '75008', 'Paris', 67, '0155091888', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Paris 8e)', '1 avenue de Friedland', 'Paris 8e', 'https://goo.gl/maps/jvuXoxj8gms', 'ouvert du lundi au vendredi, 10h - 19h', 99, 'du lundi au vendredi', 'de 10h &agrave; 19h', null, 1, 1, '02', 6230679, 'Par8', 1, 'https://www.son-video.com/magasin-hifi-home-cinema/paris-8', 'http://sv.hn/paris8'),
    (3, 'Havre', null, 1, 1, 0, 0, 0, 'RD 910 Le Montcriquet', '76210', 'Saint Jean de la Neuville', 67, '0155091830', '<EMAIL>', 1000, null, null, null, null, null, 'RD 910 Le Montcriquet', null, null, null, 2, null, null, null, 1, 1, '01', null, 'Hav', 1, null, null),
    (4, 'Schenker', null, 0, 0, 0, 0, 0, 'Parc du Hode route industrielle BP263', '76430', 'St Vigor d''Ymonville', 67, '0155091080', '<EMAIL>', 1000, null, null, null, null, null, 'Parc du Hode route industrielle BP263', null, null, null, 99, null, null, null, 0, 0, '04', null, 'Sch', 1, null, null),
    (5, 'Nantes', 31, 0, 0, 0, 0, 0, '9 place de la Bourse', '44100', 'Nantes', 67, '0249442402', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Nantes)', '9 place de la Bourse', 'Nantes', 'https://goo.gl/maps/Cucfvro99n22', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi', 'de 10h &agrave; 19h', null, 1, 1, '05', 6297732, 'Nan', 1, 'https://www.son-video.com/magasin-hifi-home-cinema/nantes', 'http://sv.hn/nantes'),
    (6, 'Lyon', 32, 0, 0, 0, 0, 0, '1 place Louis Chazette', '69001', 'Lyon', 67, '0155091798', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Lyon)', '1 place Louis Chazette', 'Lyon', 'https://goo.gl/maps/WVgFcvkT3kz', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi', 'de 10h &agrave; 19h', null, 1, 1, '06', 6297729, 'Lyo', 1, 'https://www.son-video.com/magasin-hifi-home-cinema/lyon', 'http://sv.hn/lyon'),
    (7, 'St Germain', 34, 0, 0, 0, 0, 0, '30 rue du Vieil Abreuvoir', '78100', 'Saint-Germain-en-Laye', 67, '0175584491', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (St Germain en Laye)', '30 rue du Vieil Abreuvoir', 'Saint-Germain-en-Laye', 'https://goo.gl/maps/YGsMrmtkLak', 'ouvert du mardi au samedi, 10h/13h30 - 14h30/19h', 99, 'du mardi au samedi', 'de 10h &agrave; 13h30 et de 14h30 &agrave; 19h', null, 1, 1, '07', 6320610, 'StG', 1, 'https://www.son-video.com/magasin-hifi-home-cinema/saint-germain-en-laye', 'http://sv.hn/saintgermain'),
    (8, 'Salon', 36, 0, 0, 0, 0, 0, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1000, null, null, null, 'Son-Vidéo.com - Salon', null, '', null, null, null, 99, null, null, null, 0, 1, '08', null, 'Sal', 1, null, null),
    (9, 'Lille', 37, 0, 0, 0, 0, 0, '141 Rue du Molinel', '59800', 'Lille', 67, '0155091779', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Lille)', '141 Rue du Molinel', 'Lille', 'https://goo.gl/maps/xSbGQCEDQBp', 'ouvert du mardi au samedi, 10h/13h30 - 14h30/19h', 99, 'du mardi au samedi', 'de 10h &agrave; 13h30 et de 14h30 &agrave; 19h.', null, 0, 1, '09', 6010205, 'Lil', 1, 'https://www.son-video.com/magasin-hifi-home-cinema/lille', 'http://sv.hn/lille'),
    (10, 'Grenoble', 38, 0, 0, 0, 0, 0, '2 boulevard Gambetta', '38000', 'Grenoble', 67, '0155091779', '<EMAIL>', 1000, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Grenoble)', '2 boulevard Gambetta', 'Grenoble', 'https://goo.gl/maps/PALSzBQ9woM2', 'ouvert du mardi au samedi, 10h/13h30 - 14h30/19h', 99, 'du mardi au samedi', 'de 10h &agrave; 13h30 et de 14h30 &agrave; 19h.', null, 0, 1, '10', 6010210, 'Gre', 1, 'https://www.son-video.com/magasin-hifi-home-cinema/grenoble', 'http://sv.hn/grenoble')
;

INSERT INTO backOffice.WMS_area_type (area_type_id, label)
VALUES
    (10, 'transfer')
;

INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES
    (1, 10, 1, '03.transfer', 'Zone de préparation Champigny'),
    (2, 10, 2, '02.transfer', 'Zone de préparation Paris 8')
;

-- Data of interest
INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (1, '03.transfer.nantes', 1, 'Nantes', 1),
    (2, '03.transfer.lyon', 1, 'Lyon', 1),
    (3, '03.transfer.shenker', 1, 'Shenker (Désactivé)', 0),
    (4, '02.transfer.champigny', 2, 'Champigny', 1) -- just for test purpose
;

INSERT INTO backOffice.WMS_transfer_location (location_id, destination_warehouse_id)
VALUES
(1, 5),
(2, 6),
(3, 4),
(4, 1)
;
