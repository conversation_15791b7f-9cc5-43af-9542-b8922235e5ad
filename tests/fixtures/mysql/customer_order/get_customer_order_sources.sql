INSERT INTO `commande_taxonomy_tag` (`tag_id`, `label`, `meta`) VALUES 
('amazon_business', 'Amazon Business', '{}'),
('amazon_prime', 'Amazon Prime', '{}'),
('site_checkout_v2', 'Commande site - tunnel v2', '{}'),
('source.easylounge.callcenter', 'Callcenter EasyLounge', '{"source": "CallCenter EL", "source_group": "EasyLounge.com"}'),
('source.easylounge.web', 'Web EasyLounge', '{"source": "Web EL", "source_group": "EasyLounge.com"}'),
('source.intragroup.intragroup', 'commande intra', '{"source": "Intragroupe", "source_group": "Intragroupe"}'),
('source.marketplace.amazon_easylounge', 'Amazon EasyLounge', '{"source": "Amazon EL", "source_group": "Marketplaces"}'),
('source.marketplace.amazon_sonvideo', 'Amazon SonVideo', '{"source": "Amazon SV", "source_group": "Marketplaces"}'),
('source.marketplace.boulanger_sonvideo', 'Boulanger SonVideo', '{"source": "Boulanger SV", "source_group": "Marketplaces"}'),
('source.marketplace.cdiscount_easylounge', 'Cdiscount EasyLounge', '{"source": "Cdiscount EL", "source_group": "Marketplaces"}'),
('source.marketplace.cdiscount_sonvideo', 'Cdiscount SonVideo', '{"source": "Cdiscount SV", "source_group": "Marketplaces"}'),
('source.marketplace.darty_sonvideo', 'Darty SonVideo', '{"source": "Darty SV", "source_group": "Marketplaces"}'),
('source.marketplace.easylounge', 'Marketplace EasyLounge', '{"source": "Marketplace EL", "source_group": "Marketplaces"}'),
('source.marketplace.fnac_easylounge', 'Fnac EasyLounge', '{"source": "Fnac EL", "source_group": "Marketplaces"}'),
('source.marketplace.fnac_sonvideo', 'Fnac SonVideo', '{"source": "Fnac SV", "source_group": "Marketplaces"}'),
('source.marketplace.sonvideo', 'Marketplace son-video.com', '{"source": "Marketplace SV", "source_group": "Marketplaces"}'),
('source.marketplace.source.marketplace.rueducommerce_sonvideo', 'Rue du commerce SonVideo', '{"source": "Rue du commerce SV", "source_group": "Marketplaces"}'),
('source.marketplace.ubaldi_easylounge', 'Ubaldi EasyLounge', '{"source": "Ubaldi EL", "source_group": "Marketplaces"}'),
('source.other.other', 'Autre', '{"source": "Autres", "source_group": "Autres"}'),
('source.shop.sonvideo', 'Magasin SonVideo', '{"source": "Magasin SV", "source_group": "Magasins"}'),
('source.shop.sonvideo_pro', 'Magasin Pro SonVideo', '{"source": "Magasin Pro SV", "source_group": "Magasins"}'),
('source.sonvideo.callcenter', 'Callcenter SonVideo', '{"source": "CallCenter SV", "source_group": "Son-Video.com"}'),
('source.sonvideo.web', 'Web Son-Video', '{"source": "Web SV", "source_group": "Son-Video.com"}'),
('source.sonvideo_pro.business_department', 'Département B2B SonVideo', '{"source": "Département Pro", "source_group": "Pro.Son-Video.com"}'),
('source.sonvideo_pro.callcenter', 'Callcenter Pro SonVideo', '{"source": "CallCenter Pro SV", "source_group": "Pro.Son-Video.com"}'),
('source.sonvideo_pro.web', 'Web Son-Video pro', '{"source": "Web PRO SV", "source_group": "Pro.Son-Video.com"}');
