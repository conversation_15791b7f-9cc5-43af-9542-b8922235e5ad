-- Data dependencies
INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
VALUES
    (1234, 'jean.dupont', 'none', '', '', now(), now(), 1, 1),
    (9876, 'toto', 'none', '', '', now(), now(), 1, 1)
;
INSERT INTO backOffice.sf_guard_user_profile (id, societe, site, usr_societe, titre, civilite, nom, prenom, email, poste, poste_sda, employe, signature)
VALUES
    (1234, 'Son Video Distribution', 'Champigny', 1, 'Employe', 'M.', '<PERSON><PERSON>', '<PERSON>', '<EMAIL>', '', 0, 1, 'J.D'),
    (9876, 'Son Video Distribution', 'Champigny', 1, 'Employe', 'M.', 'Toto', 'Toto', '<EMAIL>', '', 0, 1, 'Toto')
;
INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES
    (20030702191206, 5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1)
;
INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation)
VALUES
    (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha')
;
INSERT INTO backOffice.WMS_area_type (area_type_id, label)
VALUES
    (1, 'sav'),
    (2, 'entrée '),
    (3, 'sortie'),
    (4, 'stock'),
    (5, 'prépa'),
    (6, 'transit'),
    (7, 'magasin'),
    (8, 'produit  virtuel')
;
INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES
    (1, 5, 1, '03.01', 'Moyen stock'),
    (2, 1, 1, '03.02', 'Petit stock bas'),
    (3, 1, 1, '03.03', 'SAV'),
    (4, 1, 1, '03.04', 'Petit stock haut'),
    (5, 4, 1, '03.05', 'Quai'),
    (6, 1, 1, '03.06', 'Mezzanine 1'),
    (7, 1, 1, '03.07', 'Mezzanine 2'),
    (9, 6, 1, '03.user', 'Zone utilisateur')
;

-- fixtures data
INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (1, '03.01.a.01.01.01', 1, '03.01.A$01.00.01', 1),
    (2, '03.01.a.01.01.02', 1, '03.01.A$01.00.02', 1),
    (3, '03.01.a.01.01.03', 1, '03.01.A$01.00.03', 1),
    (4, '03.01.a.01.01.04', 1, '03.01.A$01.00.04', 1),
    (5, '03.01.a.01.01.05', 1, '03.01.A$01.00.05', 1),
    (6, '03.02.a.01.01.01', 2, '03.02.A$01.00.01', 1),
    (7, '03.02.a.01.01.02', 2, '03.02.A$01.00.02', 1),
    (8, '03.02.a.01.01.03', 2, '03.02.A$01.00.03', 1),
    (9, '03.02.a.01.01.04', 2, '03.02.A$01.00.04', 1),
    (10, '03.02.a.01.01.05', 2, '03.02.A$01.00.05', 1),
    (11, '03.03.a.01.01.01', 2, '03.03.A$01.00.01', 1),
    (12, '03.03.a.01.01.02', 2, '03.03.A$01.00.02', 1),
    (13, '03.03.a.01.01.03', 2, '03.03.A$01.00.03', 1),
    (14, '03.09.SAV.01.00.01', 3, '03.09.SAV$01.00.01', 1),
    (15, '03.09.SAV.01.01.01', 3, '03.09.SAV$01.01.01', 1),
    (16, '03.03.b.01.01.01', 4, '03.03.B$01.00.01', 1),
    (17, '03.03.b.01.01.02', 4, '03.03.B$01.00.02', 1),
    (18, '03.03.b.01.01.03', 4, '03.03.B$01.00.03', 1),
    (19, '03.03.b.01.01.04', 4, '03.03.B$01.00.04', 1),
    (20, '03.03.b.01.01.05', 4, '03.03.B$01.00.05', 1),
    (21, '03.04.q.01.01.01', 5, '03.04.Q$01.00.01', 1),
    (22, '03.04.q.01.01.02', 5, '03.04.Q$01.00.02', 1),
    (23, '03.04.q.01.01.03', 5, '03.04.Q$01.00.03', 1),
    (24, '03.04.q.01.01.04', 5, '03.04.Q$01.00.04', 1),
    (25, '03.04.q.01.01.05', 5, '03.04.Q$01.00.05', 1),
    (26, '03.06.m1.01.01.01', 6, '03.06.M1$01.00.01', 1),
    (27, '03.06.m1.01.01.02', 6, '03.06.M1$01.00.02', 1),
    (28, '03.06.m1.01.01.03', 6, '03.06.M1$01.00.03', 1),
    (29, '03.06.m1.01.01.04', 6, '03.06.M1$01.00.04', 1),
    (30, '03.06.m1.01.01.05', 6, '03.06.M1$01.00.05', 1),
    (31, '03.07.m2.01.01.01', 7, '03.07.M2$01.00.01', 1),
    (32, '03.07.m2.01.01.02', 7, '03.07.M2$01.00.02', 1),
    (33, '03.07.m2.01.01.03', 7, '03.07.M2$01.00.03', 1),
    (34, '03.07.m2.01.01.04', 7, '03.07.M2$01.00.04', 1),
    (35, '03.07.m2.01.01.05', 7, '03.07.M2$01.00.05', 1),
    (37, '03.user.1234', 9, '03.USER.1234', 1)
;
