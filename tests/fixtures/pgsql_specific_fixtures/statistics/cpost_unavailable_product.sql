INSERT INTO data.unavailable_product (date, customer_order_line_id, quantity)
VALUES
    ('2023-08-01', 1, 10),
    ('2023-08-01', 2, 20),
    ('2023-08-03', 3, 30),
    ('2023-08-04', 4, 40),
    ('2023-08-05', 5, 50),
    ('2023-08-06', 6, 60),
    ('2023-08-07', 7, 70),
    ('2023-08-08', 8, 80),
    ('2023-08-08', 9, 90)
;


INSERT INTO data.customer_order_line (customer_order_line_id, reference, quantity, total_gross_excl_tax, total_discount_excl_tax, total_guarantee_excl_tax, total_net_excl_tax, customer_order_id)
VALUES
    (1,'A', 1, 10, 0, 0, 15, 1),
    (2,'B', 2, 20, 0, 0, 25, 2),
    (3,'C', 3, 30, 0, 0, 35, 3),
    (5,'D', 5, 50, 0, 0, 55, 5),
    (6,'E', 6, 60, 0, 0, 65, 6),
    (7,'F', 7, 70, 0, 0, 75, 7),
    (8,'G', 8, 80, 0, 0, 85, 8),
    (9,'G', 9, 90, 0, 0, 95, 9)
;
