-- Customer order
INSERT INTO "data"."customer_order" (
        "customer_order_id",
        "created_at",
        "created_by",
        "origin",
        "source",
        "source_group",
        "sales_location",
        "postal_code",
        "country",
        "is_btob",
        "is_internal",
        "last_modified_at",
        "customer_id"
    )
VALUES (
        1, -- customer_order_id
        '2023-08-01 12:00:00', -- created_at
        'moi', -- created_by
        '', -- origin
        NULL, -- source
        NULL, -- source_group
        NULL, -- sales_location
        '44000', -- postal_code
        'FRANCE', -- country
        FALSE, -- is_btob
        FALSE, -- is_internal
        '2023-08-01 12:00:00', -- last_modified_at
        1 -- customer_id
    ),
    (
        2, -- customer_order_id
        '2023-08-01 12:00:00', -- created_at
        'moi', -- created_by
        '', -- origin
        NULL, -- source
        NULL, -- source_group
        NULL, -- sales_location
        '44000', -- postal_code
        'FRANCE', -- country
        FALSE, -- is_btob
        FALSE, -- is_internal
        '2023-08-01 12:00:00', -- last_modified_at
        1 -- customer_id
    ),
    (
        3, -- customer_order_id
        '2023-08-01 12:00:00', -- created_at
        'moi', -- created_by
        '', -- origin
        NULL, -- source
        NULL, -- source_group
        NULL, -- sales_location
        '44000', -- postal_code
        'FRANCE', -- country
        FALSE, -- is_btob
        FALSE, -- is_internal
        '2023-08-01 12:00:00', -- last_modified_at
        1 -- customer_id
    ),
    (
        4, -- customer_order_id
        '2023-08-01 12:00:00', -- created_at
        'moi', -- created_by
        '', -- origin
        NULL, -- source
        NULL, -- source_group
        NULL, -- sales_location
        '44000', -- postal_code
        'FRANCE', -- country
        FALSE, -- is_btob
        FALSE, -- is_internal
        '2023-08-01 12:00:00', -- last_modified_at
        1 -- customer_id
    );
-- Customer order line
INSERT INTO "data"."customer_order_line" (
        "customer_order_line_id",
        "order_status",
        "ean_code",
        "reference",
        "model",
        "brand",
        "supplier",
        "quantity",
        "total_purchase_cost",
        "total_gross_excl_tax",
        "total_discount_excl_tax",
        "total_net_excl_tax",
        "total_guarantee_excl_tax",
        "total_margin",
        "subcategory",
        "category",
        "domain",
        "invoiced_at",
        "customer_order_id"
    )
VALUES (
        1000, -- customer_order_line_id
        'Payée', -- order_status
        'EAN', -- ean_code
        'ref', -- reference
        'model', -- model
        'brand', -- brand
        'supplier', -- supplier
        1, -- quantity
        10, -- total_purchase_cost
        50, -- total_gross_excl_tax
        10, -- total_discount_excl_tax
        40, -- total_net_excl_tax
        0, -- total_guarantee_excl_tax
        30, -- total_margin
        'subcategory', -- subcategory
        'category', -- category
        'domain', -- domain
        NULL, -- invoiced_at
        1 -- customer_order_id
    ),
    (
        2000, -- customer_order_line_id
        'Facturée', -- order_status
        'EAN', -- ean_code
        'ref', -- reference
        'model', -- model
        'brand', -- brand
        'supplier', -- supplier
        1, -- quantity
        10, -- total_purchase_cost
        50, -- total_gross_excl_tax
        10, -- total_discount_excl_tax
        40, -- total_net_excl_tax
        0, -- total_guarantee_excl_tax
        30, -- total_margin
        'subcategory', -- subcategory
        'category', -- category
        'domain', -- domain
        '2023-07-10', -- invoiced_at
        2 -- customer_order_id

    ),
    (
        2001, -- customer_order_line_id
        'Facturée', -- order_status
        'EAN', -- ean_code
        'ref', -- reference
        'model', -- model
        'brand', -- brand
        'supplier', -- supplier
        1, -- quantity
        10, -- total_purchase_cost
        50, -- total_gross_excl_tax
        10, -- total_discount_excl_tax
        40, -- total_net_excl_tax
        0, -- total_guarantee_excl_tax
        30, -- total_margin
        'subcategory', -- subcategory
        'category', -- category
        'domain', -- domain
        '2023-07-01', -- invoiced_at
        2 -- customer_order_id

    ),
    (
        3001, -- customer_order_line_id
        'Annulée', -- order_status
        'EAN', -- ean_code
        'ref', -- reference
        'model', -- model
        'brand', -- brand
        'supplier', -- supplier
        1, -- quantity
        15, -- total_purchase_cost
        50, -- total_gross_excl_tax
        30, -- total_discount_excl_tax
        20, -- total_net_excl_tax
        0, -- total_guarantee_excl_tax
        5, -- total_margin
        'subcategory', -- subcategory
        'category', -- category
        'domain', -- domain
        NULL, -- invoiced_at
        3 -- customer_order_id

    ),
    (
        4001, -- customer_order_line_id
        'Annulée', -- order_status
        'EAN', -- ean_code
        'ref', -- reference
        'model', -- model
        'brand', -- brand
        'supplier', -- supplier
        1, -- quantity
        10, -- total_purchase_cost
        50, -- total_gross_excl_tax
        10, -- total_discount_excl_tax
        40, -- total_net_excl_tax
        0, -- total_guarantee_excl_tax
        30, -- total_margin
        'subcategory', -- subcategory
        'category', -- category
        'domain', -- domain
        NULL, -- invoiced_at
        4 -- customer_order_id
    );

INSERT INTO data.data_parameter (key, value)
VALUES
    ('synchronizer.customer_order.last_update', '2023-09-01 12:00:00')
;