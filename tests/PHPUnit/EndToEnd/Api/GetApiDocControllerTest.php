<?php

namespace PHPUnit\EndToEnd\Api;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetApiDocControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_get_default_doc_with_no_specific_authorization(): void
    {
        $this->client->request('GET', '/api/doc/default.json', [], [], ['HTTP_CONTENT_TYPE' => 'application/json']);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_content = $this->client->getResponse()->getContent();
        self::assertJson($response_content);

        $response_data = json_decode($response_content, true);
        self::assertIsArray($response_data);
        self::assertArrayHasKey('openapi', $response_data);
        self::assertArrayHasKey('info', $response_data);
        self::assertArrayHasKey('paths', $response_data);
        self::assertArrayHasKey('components', $response_data);
    }

    public function test_get_ezl_doc_with_no_specific_authorization(): void
    {
        $this->client->request('GET', '/api/doc/ezl.json', [], [], ['HTTP_CONTENT_TYPE' => 'application/json']);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_content = $this->client->getResponse()->getContent();
        self::assertJson($response_content);

        $response_data = json_decode($response_content, true);
        self::assertIsArray($response_data);
        self::assertArrayHasKey('openapi', $response_data);
        self::assertArrayHasKey('info', $response_data);
        self::assertArrayHasKey('paths', $response_data);
        self::assertArrayHasKey('components', $response_data);
    }
}
