<?php

namespace PHPUnit\EndToEnd\Api\Article;

use App\Tests\Utils\Database\MySqlDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;

class PostArticleMediaUploadControllerTest extends WebTestCase
{
    private $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
        MySqlDatabase::loadSpecificFixtures(['sales_channel/sales_channels.sql']);
        MySqlDatabase::loadSpecificFixtures(['article/post_article_media_upload.sql']);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request('POST', '/api/v1/article/123/media/upload');

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/123/media/upload',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_check_response_when_no_data_has_been_provided(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/81123/media/upload',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_INTERNAL_SERVER_ERROR);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals(
            [
                'status' => 'error',
                'message' => '"file" key name was not found on the request payload',
                'code' => 500,
                'data' => [],
            ],
            $response_content
        );
    }

    public function test_check_response_when_article_does_not_exist(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.jpg';

        $this->client->request(
            'POST',
            '/api/v1/article/666/media/upload',
            ['type' => 'IMAGE'],
            [
                'file' => new UploadedFile($file_path, 'test.jpg', 'image/jpeg', null, true),
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals(
            [
                'status' => 'error',
                'message' => 'Article not found with id or sku "666".',
                'code' => 404,
                'data' => [],
            ],
            $response_content
        );
    }

    public function test_check_response_when_an_image_has_been_successfully_uploaded_via_an_article_id(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.jpg';

        $this->client->request(
            'POST',
            '/api/v1/article/81123/media/upload',
            ['type' => 'IMAGE'],
            [
                'file' => new UploadedFile($file_path, 'test.jpg', 'image/jpeg', null, true),
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);

        // Check that URL contains the expected path
        // don't test the entirety on the string since the end of the file is dynamically generated with a random string
        self::assertStringContainsString(
            '/images/article/la-boite-concept/LBCLD25BP/pieds-noir-laque-pour-station-hifi-',
            $response_content['data']['file']['url']
        );

        // Check that width and height exist
        self::assertArrayHasKey('width', $response_content['data']['file']);
        self::assertArrayHasKey('height', $response_content['data']['file']);
    }

    public function test_check_response_when_an_image_has_been_successfully_uploaded_via_an_article_sku(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.jpg';

        $this->client->request(
            'POST',
            '/api/v1/article/NORSTCL81123M/media/upload',
            ['type' => 'IMAGE'],
            [
                'file' => new UploadedFile($file_path, 'test.jpg', 'image/jpeg', null, true),
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);

        // Check that URL contains the expected path
        // don't test the entirety on the string since the end of the file is dynamically generated with a random string
        self::assertStringContainsString(
            '/images/article/norstone/NORSTCL81123M/cl250-classic-25-mm2-25-m-',
            $response_content['data']['file']['url']
        );

        // Check that width and height exist
        self::assertArrayHasKey('width', $response_content['data']['file']);
        self::assertArrayHasKey('height', $response_content['data']['file']);
    }

    public function test_check_response_when_a_document_has_been_successfully_uploaded_via_an_article_sku(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.pdf';

        $this->client->request(
            'POST',
            '/api/v1/article/NORSTCL81123M/media/upload',
            ['type' => 'DOCUMENT'],
            [
                'file' => new UploadedFile($file_path, 'test.pdf', 'application/pdf', null, true),
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);

        // Check that URL contains the expected path
        // don't test the entirety on the string since the end of the file is dynamically generated with a random string
        self::assertStringContainsString(
            '/images/documents/article/norstone/NORSTCL81123M/cl250-classic-25-mm2-25-m-',
            $response_content['data']['file']['url']
        );

        // Check that width and height do not exist
        self::assertArrayNotHasKey('width', $response_content['data']['file']);
        self::assertArrayNotHasKey('height', $response_content['data']['file']);
    }
}
