<?php

namespace PHPUnit\EndToEnd\Api\Quote;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractCPostBasicWebTestCase;
use Symfony\Component\HttpFoundation\Response;

class CpostQuotesControllerTestAbstract extends AbstractCPostBasicWebTestCase
{
    protected const URL_ENDPOINT = '/api/v1/quotes';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the first scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/cpost_quotes.sql',
        ]);
    }

    public function test_with_token_linked_to_an_account_which_do_not_have_permission(): void
    {
        $this->request->post(static::URL_ENDPOINT);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_check_successful_response_for_quotes_api_with_proper_type_casting(): void
    {
        $json = <<<JSON
        {
            "limit": 1
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals('success', $response_content['status']);
        self::assertEquals(5, $response_content['data']['_pager']['total']);
        self::assertEquals(1, $response_content['data']['_pager']['limit']);
        self::assertCount(1, $response_content['data']['quotes']);

        $expected_quote = [
            'quote_id' => 10,
            'type' => 'quotation',
            'locked' => true,
            'created_at' => '2021-02-02 16:00:00',
            'created_by' => 2,
            'created_by_name' => 'Gérard MANVUSSA',
            'modified_at' => '2021-03-04 16:00:00',
            'customer_id' => 1,
            'customer_name' => 'Toto Alaplage',
            'customer_email' => '<EMAIL>',
            'intra_community_vat' => null,
            'sent_at' => null,
            'expired_at' => '2021-04-04 16:00:00',
            'status' => 'ordered',
            'quote_subtype' => 'CLASSIQUE',
            'valid_until' => 30,
            'message' => 'ceci est un message',
            'billing_address' => [
                'foo' => 'bar',
            ],
            'shipping_address' => [
                'foo' => 'bar',
            ],
            'shipment_method' => [
                'cost' => 4.99,
                'is_retail_store' => false,
                'shipment_method_id' => 10,
            ],
            'vat_rate' => 0.2,
            'quote_line_aggregates' => [
                [
                    'data' => [
                        'delay' => 0,
                        'product' => [
                            'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                            'ecotax_price' => 0.15,
                            'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                            'product_id' => 81078,
                            'purchase_price' => 131.58,
                            'selling_price_tax_included' => 249,
                            'short_description' => 'Arcam rBlink',
                            'sku' => 'ARCAMRBLINKNR',
                            'sorecop_price' => 0,
                            'type' => 'article',
                            'vat' => 0.2,
                        ],
                        'product_id' => 81078,
                        'quantity' => 2,
                        'quote_line_product_id' => 1,
                        'selected_warranties' => [],
                        'status' => 'oui',
                        'stock' => 3,
                        'unit_discount_amount' => 0,
                        'group_brand' => 'AV Industry',
                        'unbasketable_reason' => 'DISCONTINUED',
                    ],
                    'display_order' => 1,
                    'quote_line_id' => 1,
                    'type' => 'product',
                ],
            ],
            'customer_order_aggregates' => [
                [
                    'customer_order_id' => 1,
                    'quote_id' => 10,
                ],
                [
                    'customer_order_id' => 2,
                    'quote_id' => 10,
                ],
            ],
            'remaining_steps_to_convert_to_quotation' => [
                [
                    'error' => 'expired',
                    'message' => 'The quote has expired',
                ],
            ],
            'remaining_steps_to_convert_to_offer' => [
                [
                    'error' => 'expired',
                    'message' => 'The quote has expired',
                ],
            ],
            'remaining_steps_to_convert_to_customer_order' => [
                [
                    'error' => 'expired',
                    'message' => 'The quote has expired',
                ],
            ],
            'total_tax_included' => 502.99,
        ];

        self::assertEquals($expected_quote, $response_content['data']['quotes'][0]);
    }

    public function test_attempt_to_load_list_but_provide_an_incorrect_dependency(): void
    {
        $json = <<<JSON
        {
            "included_dependencies": ["imposteur"]
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_INTERNAL_SERVER_ERROR);

        $response_content = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        self::assertEquals(
            'Optional column with "imposteur" does not exists, Available keys are : "quote_line_aggregates", "customer_order_aggregates".',
            $response_content['message']
        );
    }

    public function test_retrieve_a_quote_list_with_its_dependencies(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/cpost_quotes.sql',
        ]);

        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 11}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response_content['data']['quotes'][0];

        self::assertEquals(11, $quote['quote_id']);
        self::assertEquals('offer', $quote['type']);
        self::assertTrue($quote['locked']);
        self::assertEquals('2022-02-03 16:10:00', $quote['created_at']);
        self::assertEquals(6, $quote['created_by']);
        self::assertEquals('jaja', $quote['created_by_name']);
        self::assertEquals('2022-02-03 16:10:00', $quote['modified_at']);
        self::assertEquals(1, $quote['customer_id']);
        self::assertEquals('Toto Alaplage', $quote['customer_name']);
        self::assertEquals('<EMAIL>', $quote['customer_email']);
        self::assertNull($quote['intra_community_vat']);
        self::assertNull($quote['sent_at']);
        self::assertEquals('2022-01-01 16:10:00', $quote['expired_at']);
        self::assertEquals('expired', $quote['status']);
        self::assertEquals('INTRAGROUP', $quote['quote_subtype']);
        self::assertEquals(15, $quote['valid_until']);
        self::assertNull($quote['message']);
        self::assertEquals(0.2, $quote['vat_rate']);
        self::assertEquals(498, $quote['total_tax_included']);
    }

    public function test_retrieve_a_quote_list_by_status_expired(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/cpost_quotes.sql',
        ]);

        $json = <<<JSON
        {
            "where": {
                "_and": [
                    {
                        "_and": [
                            {"is_ordered": {"_eq": false}},
                            {"expired_at": {"_null": 0}},
                            {"expired_at": {"_lte": "2024-11-20 13:53:13"}}
                        ]
                    }
                ]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response_content['data']['quotes'][0];

        self::assertEquals(11, $quote['quote_id']);
        self::assertEquals('offer', $quote['type']);
        self::assertTrue($quote['locked']);
        self::assertEquals('2022-02-03 16:10:00', $quote['created_at']);
        self::assertEquals(6, $quote['created_by']);
        self::assertEquals('jaja', $quote['created_by_name']);
        self::assertEquals('2022-02-03 16:10:00', $quote['modified_at']);
        self::assertEquals(1, $quote['customer_id']);
        self::assertEquals('Toto Alaplage', $quote['customer_name']);
        self::assertEquals('<EMAIL>', $quote['customer_email']);
        self::assertNull($quote['intra_community_vat']);
        self::assertNull($quote['sent_at']);
        self::assertEquals('2022-01-01 16:10:00', $quote['expired_at']);
        self::assertEquals('expired', $quote['status']);
        self::assertEquals('INTRAGROUP', $quote['quote_subtype']);
        self::assertEquals(15, $quote['valid_until']);
        self::assertNull($quote['message']);
        self::assertEquals(0.2, $quote['vat_rate']);
        self::assertEquals(498, $quote['total_tax_included']);
    }

    public function test_retrieve_a_quote_list_by_status_ordered(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [{"_and": [{"is_ordered": {"_eq": true}}]}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response_content['data']['quotes'][0];

        self::assertEquals(10, $quote['quote_id']);
        self::assertEquals('quotation', $quote['type']);
        self::assertTrue($quote['locked']);
        self::assertEquals('2021-02-02 16:00:00', $quote['created_at']);
        self::assertEquals(2, $quote['created_by']);
        self::assertEquals('Gérard MANVUSSA', $quote['created_by_name']);
        self::assertEquals('2021-03-04 16:00:00', $quote['modified_at']);
        self::assertEquals(1, $quote['customer_id']);
        self::assertEquals('Toto Alaplage', $quote['customer_name']);
        self::assertEquals('<EMAIL>', $quote['customer_email']);
        self::assertNull($quote['intra_community_vat']);
        self::assertNull($quote['sent_at']);
        self::assertEquals('2021-04-04 16:00:00', $quote['expired_at']);
        self::assertEquals('ordered', $quote['status']);
        self::assertEquals('CLASSIQUE', $quote['quote_subtype']);
        self::assertEquals(30, $quote['valid_until']);
        self::assertEquals('ceci est un message', $quote['message']);
        self::assertEquals(0.2, $quote['vat_rate']);
        self::assertEquals(502.99, $quote['total_tax_included']);
    }

    public function test_retrieved_quote_lines_dependency_has_expected_structure(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 11}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];
        $quote_lines = $quote['quote_line_aggregates'];

        // Check quote line aggregates
        self::assertCount(3, $quote_lines);

        // Check the first quote line (product type)
        $product_line = $quote_lines[0];
        self::assertEquals(2, $product_line['quote_line_id']);
        self::assertEquals(1, $product_line['display_order']);
        self::assertEquals('product', $product_line['type']);

        // Check product data
        $product_data = $product_line['data']['product'];
        self::assertEquals(81078, $product_data['product_id']);
        self::assertEquals('ARCAMRBLINKNR', $product_data['sku']);
        self::assertEquals(
            '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
            $product_data['image']
        );
        self::assertEquals('Récepteur Audio Bluetooth APTX Arcam rBlink', $product_data['description']);
        self::assertEquals('Arcam rBlink', $product_data['short_description']);
        self::assertEquals(249, $product_data['selling_price_tax_included']);
        self::assertEquals(0.15, $product_data['ecotax_price']);
        self::assertEquals(0, $product_data['sorecop_price']);
        self::assertEquals(0.2, $product_data['vat']);
        self::assertEquals(131.58, $product_data['purchase_price']);
        self::assertEquals('article', $product_data['type']);

        // Check the second quote line (section type)
        $section_line = $quote_lines[1];
        self::assertEquals(4, $section_line['quote_line_id']);
        self::assertEquals(2, $section_line['display_order']);
        self::assertEquals('section', $section_line['type']);
        self::assertEquals(100, $section_line['data']['quote_line_section_id']);
        self::assertEquals('Ceci est une section', $section_line['data']['label']);
    }

    public function test_customer_orders_can_be_retrieved_as_dependency(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 10}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];
        $customer_orders = $quote['customer_order_aggregates'];

        // Check customer order aggregates
        self::assertCount(2, $customer_orders);

        // Check first customer order
        $first_order = $customer_orders[0];
        self::assertEquals(1, $first_order['customer_order_id']);
        self::assertEquals(10, $first_order['quote_id']);

        // Check second customer order
        $second_order = $customer_orders[1];
        self::assertEquals(2, $second_order['customer_order_id']);
        self::assertEquals(10, $second_order['quote_id']);
    }

    public function test_check_locked_and_status_on_quotation_type(): void
    {
        // Test for a locked quotation with ordered status
        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 10}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];

        self::assertTrue($quote['locked']);
        self::assertEquals('ordered', $quote['status']);

        // Test for a quotation without an expired date (inactive status)
        $this->client->request(
            'POST',
            '/api/v1/quotes',
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_Authorization' => 'Bearer user2-token-without-permission',
            ],
            <<<JSON
            {
                "where": {
                    "_and": [{"quote_id": {"_eq": 12}}]
                }
            }
            JSON
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];

        self::assertFalse($quote['locked']);
        self::assertEquals('inactive', $quote['status']);
    }

    public function test_check_locked_and_status_on_offer_type(): void
    {
        // Test for an offer with an expired date (expired status)
        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 11}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];

        self::assertTrue($quote['locked']);
        self::assertEquals('2022-01-01 16:10:00', $quote['expired_at']);
        self::assertEquals('expired', $quote['status']);

        // Load fixtures for an offer with future expiration date (inactive status)
        MySqlDatabase::loadSpecificFixtures(['quote/cpost_quotes_offer_locked.sql']);

        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 11}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];

        self::assertFalse($quote['locked']);
        self::assertEquals('2099-01-01 00:00:00', $quote['expired_at']);
        self::assertEquals('inactive', $quote['status']);
    }

    public function test_filter_quotes_by_sku(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [{"sku": {"_like": "ARBIT%"}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quotes = $response['data']['quotes'];

        self::assertCount(1, $quotes);
        self::assertEquals(13, $quotes[0]['quote_id']);
        self::assertEquals('ARBITRARYSKU', $quotes[0]['quote_line_aggregates'][0]['data']['product']['sku']);
    }

    public function test_check_remaining_steps_to_convert_to_quotation(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 14}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $quote = $response['data']['quotes'][0];

        self::assertCount(1, $quote['remaining_steps_to_convert_to_quotation']);
        self::assertEquals('shipment_method_cotation', $quote['remaining_steps_to_convert_to_quotation'][0]['error']);
        self::assertEquals(
            'The quote is only eligible with the cotation shipment_method',
            $quote['remaining_steps_to_convert_to_quotation'][0]['message']
        );
        self::assertCount(0, $quote['remaining_steps_to_convert_to_offer']);
    }

    public function test_retrieve_ordered_quote(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [{"quote_id": {"_eq": 13}}]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        self::assertCount(1, $response['data']['quotes']);
        self::assertEquals('ordered', $response['data']['quotes'][0]['status']);
    }
}
