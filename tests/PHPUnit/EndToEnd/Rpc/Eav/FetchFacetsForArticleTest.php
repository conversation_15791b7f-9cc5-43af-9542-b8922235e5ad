<?php

namespace PHPUnit\EndToEnd\Rpc\Eav;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class FetchFacetsForArticleTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'eav:fetch_facets_for_article';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/get_article_by_id_or_sku.sql',
        ]);

        PgDatabase::reloadFixtures();
        PgDatabase::loadSpecificFixtures(['eav/rpc_eav_fetch_facets_for_article.sql']);
    }

    public function test_rpc_with_no_payload(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, []);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '-32602', with message ===
        Could not fetch unexistent argument 0 (sku).
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_non_existing_sku(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['NONEXISTING']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Could not load product information's from sku : "NONEXISTING"
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_sku_existing_in_legacy_db_but_not_in_pg_db(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['BWCCM74']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertNull($response_data['result']['article']['eav_facets']);
        self::assertEquals('BWCCM74', $response_data['result']['article']['sku']);
    }

    public function test_try_to_fetch_facets_for_a_sku_with_no_active_facets()
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['NORSTCL81123M']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals('NORSTCL81123M', $response_data['result']['article']['sku']);
        self::assertNull($response_data['result']['article']['eav_facets']);
    }

    public function test_successfully_retrieve_eav_facets_for_an_active_article(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['ARCAMRBLINKNR']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals('ARCAMRBLINKNR', $response_data['result']['article']['sku']);
        self::assertNotNull($response_data['result']['article']['eav_facets']);
        self::assertCount(3, $response_data['result']['article']['eav_facets']);
        self::assertEquals(908, $response_data['result']['article']['eav_facets'][0]['attribute_id']);
        self::assertCount(1, $response_data['result']['article']['eav_facets'][0]['attribute_values']);
        self::assertEquals(23334, $response_data['result']['article']['eav_facets'][0]['attribute_values'][0]);
    }
}
