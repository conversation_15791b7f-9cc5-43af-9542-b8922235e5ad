<?php

namespace PHPUnit\EndToEnd\Rpc\Quote;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class FetchOneTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'quote:fetch_one';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/quote_for_rpc.sql',
        ]);
    }

    public function test_fails_to_fetch_a_quote_that_does_not_exist(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, [9]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);
        self::assertEquals(-32603, $response_data['error']['code']);
        self::assertStringContainsString(
            '[QUOTE] Failed to fetch one quote with id "9"',
            $response_data['error']['message']
        );
    }

    public function test_successfully_fetch_a_quote(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, [10]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        $this->assertEquals($response_data['result'], [
            'quote_id' => 10,
            'type' => 'quotation',
            'status' => 'inactive',
            'created_at' => '2022-03-03 16:20:00',
            'customer_id' => 2,
            'expired_at' => null,
            'message' => 'Best of the best',
            'billing_address' => [
                'city' => 'NANTES',
                'name' => 'Bureau',
                'phone' => '',
                'address' => '1 rue des fleurs',
                'country' => [
                    'name' => 'FRANCE',
                    'country_id' => 67,
                    'country_code' => 'FR',
                ],
                'civility' => 'M.',
                'lastname' => 'TERIEUR',
                'cellphone' => '0606060606',
                'firstname' => 'Alain',
                'created_at' => '2020-03-04 9:55:15',
                'postal_code' => '44100',
                'company_name' => 'La Cie',
            ],
            'shipping_address' => [
                'city' => 'NANTES',
                'name' => 'Bureau',
                'phone' => '',
                'address' => '1 rue des fleurs',
                'country' => [
                    'name' => 'FRANCE',
                    'country_id' => 67,
                    'country_code' => 'FR',
                ],
                'civility' => 'M.',
                'lastname' => 'TERIEUR',
                'cellphone' => '0606060606',
                'firstname' => 'Alain',
                'created_at' => '2020-03-04 9:55:15',
                'postal_code' => '44100',
                'company_name' => 'La Cie',
            ],
            'shipment_method' => [
                'cost' => 4.99,
                'shipment_method_id' => 10,
            ],
            'prices' => [
                'total_discount_tax_excluded' => 750,
                'total_discount_tax_included' => 900,
                'total_price_tax_excluded' => 6056.658333333334,
                'total_vat' => 1211.3316666666667,
                'total_price_tax_included' => 7267.99,
                'computed_vat_rate' => 1.2,
            ],
            'lines' => [
                [
                    'display_order' => 1,
                    'type' => 'product',
                    'data' => [
                        'product' => [
                            'product_id' => 128416,
                            'sku' => 'QACOQ3050INRMT',
                            'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                            'short_description' => 'Arcam rBlink',
                            'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                            'selling_price_tax_included' => 1200,
                            'ecotax_price' => 0,
                            'sorecop_price' => 0,
                            'vat' => 0.2,
                            'type' => 'article',
                        ],
                        'quantity' => 1,
                        'selling_price_tax_excluded' => 1000,
                        'selling_price_tax_included' => 1200,
                        'unit_discount_amount_abs_tax_included' => 300,
                        'total_discount_amount' => 300,
                        'total_price' => 900,
                        'selected_warranties' => [],
                    ],
                ],
                [
                    'display_order' => 2,
                    'type' => 'product',
                    'data' => [
                        'product' => [
                            'product_id' => 81123,
                            'sku' => 'ELIPSPLANETMSUBBC',
                            'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                            'short_description' => 'Arcam rBlink',
                            'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                            'selling_price_tax_included' => 600,
                            'ecotax_price' => 0.5,
                            'sorecop_price' => 1,
                            'vat' => 0.2,
                            'type' => 'article',
                        ],
                        'quantity' => 4,
                        'selling_price_tax_excluded' => 500,
                        'selling_price_tax_included' => 600,
                        'unit_discount_amount_abs_tax_included' => 50,
                        'total_discount_amount' => 200,
                        'total_price' => 2200,
                        'selected_warranties' => [],
                    ],
                ],
                [
                    'display_order' => 3,
                    'type' => 'section',
                    'data' => [
                        'label' => 'Ceci est une section',
                    ],
                ],
                [
                    'display_order' => 4,
                    'type' => 'product',
                    'data' => [
                        'product' => [
                            'product_id' => 81078,
                            'sku' => 'QACOQ3050IBCMT',
                            'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                            'short_description' => 'Arcam rBlink',
                            'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                            'selling_price_tax_included' => 1200,
                            'ecotax_price' => 0,
                            'sorecop_price' => 0,
                            'vat' => 0.2,
                            'type' => 'article',
                        ],
                        'quantity' => 2,
                        'selling_price_tax_excluded' => 1000,
                        'selling_price_tax_included' => 1200,
                        'unit_discount_amount_abs_tax_included' => 200,
                        'total_discount_amount' => 400,
                        'total_price' => 2000,
                        'selected_warranties' => [
                            [
                                'duration' => 5,
                                'label' => 'Extension de garantie 5 ans',
                                'type' => 'extension',
                                'unit_selling_price_tax_included' => 79.5,
                            ],
                            [
                                'duration' => 2,
                                'label' => 'Garantie vol-casse 2 ans',
                                'type' => 'theft_breakdown',
                                'unit_selling_price_tax_included' => 1002,
                            ],
                        ],
                    ],
                ],
                [
                    'display_order' => 4,
                    'type' => 'product_warranty',
                    'data' => [
                        'type' => 'extension',
                        'label' => 'Extension de garantie 5 ans',
                        'duration' => 5,
                        'quantity' => 2,
                        'unit_selling_price_tax_included' => 79.5,
                        'price_tax_excluded' => 66.25,
                        'total_price_tax_included' => 159.0,
                        'total_price_tax_excluded' => 132.5,
                    ],
                ],
                [
                    'display_order' => 4,
                    'type' => 'product_warranty',
                    'data' => [
                        'type' => 'theft_breakdown',
                        'label' => 'Garantie vol-casse 2 ans',
                        'duration' => 2,
                        'quantity' => 2,
                        'unit_selling_price_tax_included' => 1002.0,
                        'price_tax_excluded' => 835.0,
                        'total_price_tax_included' => 2004.0,
                        'total_price_tax_excluded' => 1670.0,
                    ],
                ],
            ],
        ]);
    }
}
