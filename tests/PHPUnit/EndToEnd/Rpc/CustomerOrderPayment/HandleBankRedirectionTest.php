<?php

namespace PHPUnit\EndToEnd\Rpc\CustomerOrderPayment;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class HandleBankRedirectionTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'customer_order_payment:handle_bank_redirection';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order_payment/rpc/handle_bank_redirection.sql',
        ]);
    }

    public function test_rpc_with_no_payload(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, []);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '-32602', with message ===
        Could not fetch unexistent argument 0 (payload).
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_wrong_payload(): void
    {
        $payload = [
            'customer_order_id' => '',
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Invalid request parameters : [customer_order_id]: This value should not be blank.
         [customer_order_id]: This value should be of type integer.
         [request_uri]: This field is missing.
         [articles]: This field is missing.
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_successfully_call_rpc_get_empty_result_on_non_existing_customer_order(): void
    {
        $payload = [
            'customer_order_id' => 666,
            'request_uri' => 'http://dummy',
            'articles' => [
                [
                    'sku' => 'DUMMY',
                    'unit_selling_price' => 1,
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        $this->assertEquals(false, $response_data['result']['action']);
    }

    public function test_successfully_call_rpc_get_form(): void
    {
        $payload = [
            'customer_order_id' => 1,
            'request_uri' => 'http://dummy',
            'articles' => [
                [
                    'sku' => 'DUMMY',
                    'unit_selling_price' => 1,
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        $this->assertArrayHasKey('auto_form', $response_data['result']['action']);
    }

    public function test_successfully_call_rpc_get_empty_result_on_already_paid_customer_order(): void
    {
        $payload = [
            'customer_order_id' => 2,
            'request_uri' => 'http://dummy',
            'articles' => [
                [
                    'sku' => 'DUMMY',
                    'unit_selling_price' => 1,
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        $this->assertEquals(false, $response_data['result']['action']);
    }
}
