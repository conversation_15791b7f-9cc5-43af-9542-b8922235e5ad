<?php

namespace PHPUnit\EndToEnd\Rpc\CustomerOrder;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class CreateTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'customer_order:create';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/creator.sql',
        ]);
    }

    public function test_rpc_with_no_payload(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, []);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '-32602', with message ===
        Could not fetch unexistent argument 0 (payload).
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_payload_who_is_not_a_json(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['toto']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Payload must be an array.
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_wrong_payload(): void
    {
        $payload = [
            'customer_order_id' => '',
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Invalid request parameters : origin: This value should not be blank.
         created_at: This value should not be blank.
         billing_address: This value should not be blank.
         shipping_address: This value should not be blank.
         shipment_method: This value should not be blank.
         products: This value should not be blank.
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_successfully_create_customer_order(): void
    {
        $payload = [
            'original_customer_order_id' => '123',
            'origin' => 'son-video.com',
            'created_at' => '2022-01-01 00:00:00',
            'estimated_delivery_date' => '2022-05-27',
            'customer_id' => 1,
            'quote_id' => 10,
            'warehouse_id' => null,
            'ip_address' => '***********',
            'billing_address' => [
                'company_name' => 'La Cie',
                'civility' => 'M.',
                'firstname' => 'Alain',
                'lastname' => 'TERIEUR',
                'address' => '1 rue des fleurs',
                'postal_code' => '44100',
                'city' => 'NANTES',
                'country_code' => 'FR',
                'phone' => '0606060606',
                'cellphone' => '0606060606',
                'email' => '<EMAIL>',
            ],
            'shipping_address' => [
                'company_name' => 'La Cie',
                'civility' => 'M.',
                'firstname' => 'Alain',
                'lastname' => 'TERIEUR',
                'address' => '1 rue des fleurs',
                'postal_code' => '44100',
                'city' => 'NANTES',
                'country_code' => 'FR',
                'phone' => '0606060606',
                'cellphone' => '0606060606',
                'email' => '<EMAIL>',
            ],
            'shipment_method' => [
                'cost' => 4.99,
                'shipment_method_id' => 1,
            ],
            'payments' => [
                [
                    'payment_mean' => 'CBS',
                    'created_at' => '2022-01-01 00:00:00',
                    'amount' => 1104.99,
                ],
            ],
            'products' => [
                [
                    'sku' => 'ARCAMRBLINKNR',
                    'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                    'ecotax_price' => 0,
                    'sorecop_price' => 0,
                    'quantity' => 1,
                    'selling_price_tax_included' => 1200,
                    'unit_discount_amount' => -100,
                    'selected_warranties' => [],
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals(1234, $response_data['result']['customer_order_id']);
    }
}
