<?php

namespace PHPUnit\Unit\App\Formatter\Http;

use App\Formatter\Http\JSendFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class JSendFormatterTest extends KernelTestCase
{
    /** Tests the convertNumeric method with various inputs. */
    public function test_convert_numeric(): void
    {
        // Test with string representation of float
        $value = JSendFormatter::convertNumeric('10.2');
        $this->assertIsFloat($value);
        $this->assertEquals(10.2, $value);

        // Test with string representation of int
        $value = JSendFormatter::convertNumeric('99');
        $this->assertIsFloat($value);
        $this->assertEquals(99, $value);

        // Test with non-numeric string
        $value = JSendFormatter::convertNumeric('test10.2');
        $this->assertIsString($value);
        $this->assertEquals('test10.2', $value);

        // Test with float
        $value = JSendFormatter::convertNumeric(10.2);
        $this->assertIsFloat($value);
        $this->assertEquals(10.2, $value);

        // Test with recursive array
        $value = JSendFormatter::convertNumeric([
            'test1' => '99.9',
            'test2' => 'test99.9',
            'test3' => [
                'test3.1' => '99.9',
                'test3.2' => 'test99.9',
            ],
        ]);

        $this->assertEquals(
            [
                'test1' => 99.9,
                'test2' => 'test99.9',
                'test3' => [
                    'test3.1' => 99.9,
                    'test3.2' => 'test99.9',
                ],
            ],
            $value
        );
    }
}
