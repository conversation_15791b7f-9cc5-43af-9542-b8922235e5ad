<?php

namespace PHPUnit\Unit\App\Sql\Query\Operator;

use App\Sql\Query\Operator\ColumnComparisonQueryOperator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ColumnComparisonQueryOperatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ColumnComparisonQueryOperator. */
    protected function getTestedInstance(): ColumnComparisonQueryOperator
    {
        return self::$container->get(ColumnComparisonQueryOperator::class);
    }

    /** Tests the handle method with various inputs. */
    public function test_handle(): void
    {
        // Test all operators defined in the class
        foreach (ColumnComparisonQueryOperator::KEYS as $name => $sql_operator) {
            $result = $this->getTestedInstance()->handle('t.col', $name, 't.another_col', 'parameter_name');
            $this->assertSame(sprintf('t.col %s t.another_col', $sql_operator), $result);
        }
    }
}
