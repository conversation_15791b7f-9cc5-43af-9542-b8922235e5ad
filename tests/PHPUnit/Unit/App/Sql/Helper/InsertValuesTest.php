<?php

namespace PHPUnit\Unit\App\Sql\Helper;

use App\Sql\Helper\InsertValues;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class InsertValuesTest extends KernelTestCase
{
    /** Tests the default values of the class. */
    public function test_defaults(): void
    {
        $insert_values = new InsertValues();

        $this->assertEquals('', $insert_values->getColumnList());
        $this->assertEquals('', $insert_values->getSql());
        $this->assertEmpty($insert_values->getParams());
        $this->assertEquals($insert_values->getSql(), (string) $insert_values);
    }

    /** Tests the columns methods. */
    public function test_columns(): void
    {
        // Test setting columns through constructor
        $insert_values = new InsertValues(['column1', 'column2', 'column3']);
        $expected_list = 'column1, column2, column3';

        $this->assertEquals($expected_list, $insert_values->getColumnList());

        // Test setting columns through setColumns method
        $insert_values->setColumns(['name1', 'name2', 'name3']);
        $expected_list = 'name1, name2, name3';

        $this->assertEquals($expected_list, $insert_values->getColumnList());
    }

    /** Tests the values and params methods. */
    public function test_values_and_params(): void
    {
        $insert_values = new InsertValues(['column1', 'column2', 'column3']);

        $insert_values->addValues([
            'column1' => 'value1_1',
            'column2' => 'value1_2',
            'column3' => 'value1_3',
        ]);

        $insert_values->addValues([
            'column1' => 'value2_1',
            'column2' => 'value2_2',
            'column3' => 'value2_3',
        ]);

        $this->assertEquals(
            '(:column1__0, :column2__0, :column3__0), (:column1__1, :column2__1, :column3__1)',
            $insert_values->getSql()
        );
        $this->assertEquals(
            [
                'column1__0' => 'value1_1',
                'column2__0' => 'value1_2',
                'column3__0' => 'value1_3',
                'column1__1' => 'value2_1',
                'column2__1' => 'value2_2',
                'column3__1' => 'value2_3',
            ],
            $insert_values->getParams()
        );
    }

    /** Tests the exceptions thrown by setColumns with invalid types. */
    public function test_set_columns_with_invalid_types(): void
    {
        $insert_values = new InsertValues();

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage('Each column name must be a string.');

        $insert_values->setColumns([1]);
    }

    /** Tests the exceptions thrown by setColumns with array as column name. */
    public function test_set_columns_with_array_as_column_name(): void
    {
        $insert_values = new InsertValues();

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage('Each column name must be a string.');

        $insert_values->setColumns([[]]);
    }

    /** Tests the exceptions thrown by setColumns with duplicate column names. */
    public function test_set_columns_with_duplicate_column_names(): void
    {
        $insert_values = new InsertValues();

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage('Column "name" is present more than one time.');

        $insert_values->setColumns(['name', 'other', 'name']);
    }

    /** Tests the exceptions thrown by addValues without columns defined. */
    public function test_add_values_without_columns_defined(): void
    {
        $insert_values = new InsertValues();
        $insert_values->setColumns([]);

        $this->expectException(\LogicException::class);
        $this->expectExceptionMessage('Columns have not been defined.');

        $insert_values->addValues([]);
    }

    /** Tests the exceptions thrown by addValues with missing columns. */
    public function test_add_values_with_missing_columns(): void
    {
        $insert_values = new InsertValues();
        $insert_values->setColumns(['name', 'surname', 'other']);

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage('Some columns are missing: name, other.');

        $insert_values->addValues([
            'surname' => 'toto',
        ]);
    }
}
