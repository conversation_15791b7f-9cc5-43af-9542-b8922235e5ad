<?php

namespace PHPUnit\Unit\App\Sql\Helper;

use App\Sql\Helper\Where;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class WhereTest extends KernelTestCase
{
    /** Tests the default values of the class. */
    public function test_defaults(): void
    {
        $where = new Where();

        $this->assertEquals('', $where->getSql());
        $this->assertEmpty($where->getParams());
        $this->assertEquals($where->getSql(), (string) $where);

        // Test constants
        $this->assertEquals('_null_', Where::NULL_VALUE);
        $this->assertEquals('_not_null_', Where::NOT_NULL_VALUE);
    }

    /** Tests the addCondition method. */
    public function test_add_condition(): void
    {
        // Test simple condition
        $where = new Where();
        $where->addCondition('column1', 'query_param1', 'query_value1');
        $expected_sql = 'WHERE true AND column1 = :query_param1 ';

        $this->assertEquals($expected_sql, $where->getSql());
        $this->assertEquals(
            [
                'query_param1' => 'query_value1',
            ],
            $where->getParams()
        );
        $this->assertEquals($where->getSql(), (string) $where);

        // Test with non-string value and custom comparator
        $where->addCondition('column2', 'query_param2', 2, '>');
        $expected_sql .= 'AND column2 > :query_param2 ';

        $this->assertEquals($expected_sql, $where->getSql());
        $this->assertEquals(
            [
                'query_param1' => 'query_value1',
                'query_param2' => 2,
            ],
            $where->getParams()
        );

        // Test with NULL_VALUE
        $where->addCondition('column3', 'query_param3', Where::NULL_VALUE);
        $expected_sql .= 'AND column3 IS NULL ';

        $this->assertEquals($expected_sql, $where->getSql());
        $this->assertEquals(
            [
                'query_param1' => 'query_value1',
                'query_param2' => 2,
            ],
            $where->getParams()
        );

        // Test with NOT_NULL_VALUE
        $where->addCondition('column4', 'query_param4', Where::NOT_NULL_VALUE);
        $expected_sql .= 'AND column4 IS NOT NULL ';

        $this->assertEquals($expected_sql, $where->getSql());
        $this->assertEquals(
            [
                'query_param1' => 'query_value1',
                'query_param2' => 2,
            ],
            $where->getParams()
        );

        // Test with custom condition template
        $where->addCondition('column5', 'query_param5', '2019-12-22 13:37:00', '>=', 'date(:key)');
        $expected_sql .= 'AND column5 >= date(:query_param5) ';

        $this->assertEquals($expected_sql, $where->getSql());
        $this->assertEquals(
            [
                'query_param1' => 'query_value1',
                'query_param2' => 2,
                'query_param5' => '2019-12-22 13:37:00',
            ],
            $where->getParams()
        );
    }

    /** Tests the addInCondition method. */
    public function test_add_in_condition(): void
    {
        $where = new Where();
        $where->addInCondition('column1', 'param1', ['query_value1', 2]);
        $expected_sql = 'WHERE true AND column1 IN (:param1__0,:param1__1) ';

        $this->assertEquals($expected_sql, $where->getSql());
        $this->assertEquals(
            [
                'param1__0' => 'query_value1',
                'param1__1' => 2,
            ],
            $where->getParams()
        );
        $this->assertEquals($where->getSql(), (string) $where);
    }
}
