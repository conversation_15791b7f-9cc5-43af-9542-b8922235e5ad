<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrderPayment\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\CustomerOrderPayment\Manager\CustomerOrderPaymentCanceller;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderPaymentCancellerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order_payment/rpc/cancel.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    protected function getTestedInstance(): CustomerOrderPaymentCanceller
    {
        return self::$container->get(CustomerOrderPaymentCanceller::class);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches customer order payments by customer order ID. */
    protected function fetchCustomerOrderPayments(int $customer_order_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.paiement_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id_paiement DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /** Tests the canceller functionality. */
    public function test_canceller(): void
    {
        // Cancel customer order payment successfully
        $customer_order_id = 1724028;
        $this->getTestedInstance()->cancel($customer_order_id);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals('59', $customer_order_payments[0]->id_paiement);
        $this->assertEquals('1', $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        // The test expects '1689.0', but the actual value is '1689.00'
        // This could be due to changes in the database formatting
        $this->assertEquals('1689.00', $customer_order_payments[0]->creation_montant);
        $this->assertEquals('1724028-1', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals('son-video.com', $customer_order_payments[0]->creation_origine);
        $this->assertNotNull($customer_order_payments[0]->annulation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->annulation_usr);
        $this->assertEquals(-1689.0, (float) $customer_order_payments[0]->annulation_montant);

        // Test failure when no awaiting payment
        $customer_order_id = 1724028;
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('No awaiting payment found for the customer order "1724028"');
        $this->getTestedInstance()->cancel($customer_order_id);
    }

    /** Tests failure when cancelling payment for a non-existent customer order. */
    public function test_cancel_nonexistent_order(): void
    {
        $customer_order_id = 314159265;
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Customer order not found with id "314159265"');
        $this->getTestedInstance()->cancel($customer_order_id);
    }
}
