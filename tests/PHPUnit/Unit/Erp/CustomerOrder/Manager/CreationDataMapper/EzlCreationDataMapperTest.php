<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager\CreationDataMapper;

use App\Exception\NotFoundException;
use App\Tests\Mock\Erp\CustomerOrder\CustomerOrderPayload;
use App\Tests\Utils\ArrayHelper;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Exception\CustomerOrderRequestPayloadException;
use SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper\EzlCreationDataMapper as TestedClass;
use SonVideo\Erp\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Product;
use SonVideo\Erp\Referential\SalesChannel;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class EzlCreationDataMapperTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/creator.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests the mapping functionality. */
    public function test_convert(): void
    {
        $result = $this->getTestedInstance()->map(CustomerOrderPayload::getValidPayloadFromEzl());

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);
        $this->assertEquals(
            ArrayHelper::sortKeys($this->getPayloadMapped()),
            ArrayHelper::sortKeys($result->toArray())
        );
    }

    /** Tests that mapping fails if SKU is not found. */
    public function test_fail_mapping_if_sku_not_found(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $payload['products'][0]['sku'] = 'TOTO';

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Product not found with id or sku "TOTO".');
        $this->getTestedInstance()->map($payload);
    }

    /** Tests that mapping fails if carrier_id is not found. */
    public function test_fail_mapping_if_carrier_id_not_found(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $payload['shipment_method']['shipment_method_id'] = 666;

        $this->expectException(NotFoundException::class);
        $this->getTestedInstance()->map($payload);
    }

    /** Tests that mapping fails if source is not valid. */
    public function test_fail_mapping_if_source_not_valid(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $payload['source'] = 'Mont Gerbier-de-Jonc';

        $this->expectException(CustomerOrderRequestPayloadException::class);
        $this->getTestedInstance()->map($payload);
    }

    /** Tests that warranties are ignored. */
    public function test_ignore_warranties(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $payload['products'][0]['selected_warranties'] = [
            0 => [
                'type' => 'extension',
                'unit_selling_price_tax_included' => 79,
                'duration' => 5,
                'label' => 'Je suis une extension de garantie',
            ],
        ];
        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);
        $this->assertEquals(
            ArrayHelper::sortKeys($this->getPayloadMapped()),
            ArrayHelper::sortKeys($result->toArray())
        );
    }

    /** Tests mapping data successfully without carrier. */
    public function test_map_data_without_carrier(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $payload['shipment_method']['shipment_method_id'] = null;
        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);
        $this->assertNull($result->carrier_id);
        $this->assertNull($result->shipment_method_id);
        $this->assertNull($result->store_pickup_id);
    }

    private function getPayloadMapped(): array
    {
        return [
            'customer_order_id' => 123,
            'original_customer_order_id' => 'EC1234',
            'clone_customer_order_id' => null,
            'origin' => 'ecranlounge.com',
            'internal_status' => 'traitement',
            'created_at' => '2022-01-01 00:00:00',
            'estimated_delivery_date' => null,
            'customer_id' => 314559,
            'new_customer' => false,
            'quote_id' => null,
            'warehouse_id' => null,
            'invoice_comment' => '',
            'ip_address' => '*************',
            'billing_address_company_name' => '',
            'billing_address_civility' => 'M.',
            'billing_address_firstname' => 'Alain',
            'billing_address_lastname' => 'TERIEUR',
            'billing_address_address' => '1 rue des fleurs',
            'billing_address_postal_code' => '44100',
            'billing_address_city' => 'NANTES',
            'billing_address_country_id' => 67,
            'billing_address_phone' => '0606060606',
            'billing_address_cellphone' => '0606060607',
            'billing_address_email' => '<EMAIL>',
            'billing_vat_number' => null,
            'shipping_address_company_name' => '',
            'shipping_address_civility' => 'M.',
            'shipping_address_firstname' => 'Laure',
            'shipping_address_lastname' => 'DURE',
            'shipping_address_address' => '111 route de paris',
            'shipping_address_postal_code' => '44000',
            'shipping_address_city' => 'NANTES',
            'shipping_address_country_id' => 67,
            'shipping_address_phone' => '0707070707',
            'shipping_address_cellphone' => '0707070708',
            'shipping_address_email' => '<EMAIL>',
            'carrier_id' => 2,
            'shipment_method_id' => 1,
            'store_pickup_id' => null,
            'relay_id' => null,
            'svd_header' => false,
            'tags' => [
                [
                    'customer_order_id' => 123,
                    'tag_id' => CustomerOrderTag::SOURCE_WEB_EZL,
                    'meta' => null,
                ],
            ],
            'payments' => [
                [
                    'customer_order_id' => 123,
                    'payment_mean' => 'ECLNG',
                    'unique_id' => 1,
                    'created_at' => '2022-01-01 00:00:00',
                    'amount' => 1144.99,
                    'created_proof' => 'EC1234-1',
                    'origin' => 'ecranlounge.com',
                    'extra_data' => [],
                    'workflow' => 'legacy',
                ],
            ],
            'products' => [
                [
                    'customer_order_id' => 123,
                    'product_id' => 81078,
                    'type' => Product::TYPE_ARTICLE,
                    'quantity' => 1,
                    'selling_price_tax_included' => 1200.0,
                    'vat' => 0.2,
                    'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                    'discount_type' => 'commande',
                    'discount_amount' => -100.0,
                    'discount_description' => 'Remise exceptionnelle',
                    'ecotax_price' => 0.0,
                    'sorecop_price' => 0.0,
                    'group_type' => null,
                    'group_description' => null,
                    'warranty_duration_ext' => null,
                    'warranty_unit_selling_price_ext' => null,
                    'warranty_duration_tb' => null,
                    'warranty_unit_selling_price_tb' => null,
                    'promo_code' => null,
                ],
                [
                    'customer_order_id' => 123,
                    'product_id' => 81123,
                    'type' => Product::TYPE_ARTICLE,
                    'quantity' => 1,
                    'selling_price_tax_included' => 40.0,
                    'vat' => 0.2,
                    'description' => 'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
                    'discount_type' => null,
                    'discount_amount' => 0.0,
                    'discount_description' => null,
                    'ecotax_price' => 0.0,
                    'sorecop_price' => 0.0,
                    'group_type' => null,
                    'group_description' => null,
                    'warranty_duration_ext' => null,
                    'warranty_unit_selling_price_ext' => null,
                    'warranty_duration_tb' => null,
                    'warranty_unit_selling_price_tb' => null,
                    'promo_code' => null,
                ],
                [
                    'customer_order_id' => 123,
                    'product_id' => Product::SHIPMENT_PRODUCT_ID,
                    'type' => Product::TYPE_GENERIQUE,
                    'quantity' => 1,
                    'selling_price_tax_included' => 4.99,
                    'vat' => 0.2,
                    'description' => 'Frais de port facturés',
                    'discount_type' => null,
                    'discount_amount' => 0.0,
                    'discount_description' => null,
                    'ecotax_price' => 0.0,
                    'sorecop_price' => 0.0,
                    'group_type' => null,
                    'group_description' => null,
                    'warranty_duration_ext' => null,
                    'warranty_unit_selling_price_ext' => null,
                    'warranty_duration_tb' => null,
                    'warranty_unit_selling_price_tb' => null,
                    'promo_code' => null,
                ],
            ],
            'chrono_precise_appointment' => null,
            'is_intragroup' => false,
            'is_b2b' => false,
            'quote_creator_username' => null,
            'is_excluding_tax' => 'non',
            'sales_channel_id' => SalesChannel::EASYLOUNGE,
            'promotion_id' => null,
        ];
    }
}
