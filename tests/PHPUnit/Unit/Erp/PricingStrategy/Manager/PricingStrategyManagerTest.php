<?php

namespace PHPUnit\Unit\Erp\PricingStrategy\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\LegacyPdo;
use App\Tests\Mock\Erp\PricingStrategy\PricingStrategyFixtures;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\PricingStrategy\Dto\CreationContext\PricingStrategyCreationContextDto;
use SonVideo\Erp\PricingStrategy\Manager\PricingStrategyManager;
use SonVideo\Erp\Referential\PricingStrategyStatus;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class PricingStrategyManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'pricing_strategy/pricing_strategy.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();

        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);
    }

    protected function getTestedInstance(): PricingStrategyManager
    {
        return self::$container->get(PricingStrategyManager::class);
    }

    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws InternalErrorException
     * @throws \Throwable
     */
    public function test_create(): void
    {
        $manager = $this->getTestedInstance();

        // Test valid DTO
        $dto = $this->getSerializer()->denormalize(
            PricingStrategyFixtures::PRICING_STRATEGY_OK,
            PricingStrategyCreationContextDto::class
        );
        $id = $manager->create($dto);
        $this->assertEquals(15, $id);

        // Test DTO with wrong status
        $dto = $this->getSerializer()->denormalize(
            PricingStrategyFixtures::PRICING_STRATEGY_WRONG_STATUS,
            PricingStrategyCreationContextDto::class
        );

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessageMatches('/Invalid parameters/');
        $manager->create($dto);
    }

    /**
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws InternalErrorException
     * @throws \Throwable
     */
    public function test_create_with_no_start_date(): void
    {
        $manager = $this->getTestedInstance();

        // Test DTO with no start date
        $dto = $this->getSerializer()->denormalize(
            PricingStrategyFixtures::PRICING_STRATEGY_NO_START_DATE,
            PricingStrategyCreationContextDto::class
        );

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessageMatches('/Invalid parameters/');
        $manager->create($dto);
    }

    /**
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws InternalErrorException
     * @throws \Throwable
     */
    public function test_create_with_start_after_end(): void
    {
        $manager = $this->getTestedInstance();

        // Test DTO with start date after end date
        $dto = $this->getSerializer()->denormalize(
            PricingStrategyFixtures::PRICING_STRATEGY_START_AFTER_END,
            PricingStrategyCreationContextDto::class
        );

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessageMatches('/Invalid parameters/');
        $manager->create($dto);
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws ExceptionInterface
     */
    public function test_activate_nonexistent_strategy(): void
    {
        $manager = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Pricing strategy not found with id 78');
        $manager->activatePricingStrategy(78, $this->getUser());
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws ExceptionInterface
     */
    public function test_activate_already_activated_strategy(): void
    {
        $manager = $this->getTestedInstance();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Pricing strategy already activated');
        $manager->activatePricingStrategy(14, $this->getUser());
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws ExceptionInterface
     */
    public function test_activate_strategy(): void
    {
        $manager = $this->getTestedInstance();

        $manager->activatePricingStrategy(5, $this->getUser());

        $pricing_strategy_active = $this->fetchPricingStrategy(5);
        $this->assertEquals(
            [
                'pricing_strategy_id' => 5,
                'name' => 'strat de chokbar',
                'activation_status' => 'ACTIVATED',
            ],
            $pricing_strategy_active
        );

        $pricing_strategy_archive = $this->fetchLastPricingStrategyArchive(5);
        $this->assertEquals(
            [
                'pricing_strategy_id' => 5,
                'name' => 'strat de chokbar',
                'activation_status' => 'ACTIVATED',
            ],
            $pricing_strategy_archive
        );
    }

    public function test_deactivate_already_deactivated_strategy(): void
    {
        $manager = $this->getTestedInstance();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Pricing strategy already deactivated');
        $manager->closePricingStrategy(13, $this->getUser()->id_utilisateur, PricingStrategyStatus::DEACTIVATED);
    }

    public function test_deactivate_strategy(): void
    {
        $manager = $this->getTestedInstance();

        $manager->closePricingStrategy(5, $this->getUser()->id_utilisateur, PricingStrategyStatus::DEACTIVATED);

        $pricing_strategy_config = $this->fetchPricingStrategy(5);
        $this->assertEquals(
            [
                'pricing_strategy_id' => 5,
                'name' => 'strat de chokbar',
                'activation_status' => 'DEACTIVATED',
            ],
            $pricing_strategy_config
        );

        $pricing_strategy_archive = $this->fetchLastPricingStrategyArchive(5);
        $this->assertEquals(
            [
                'pricing_strategy_id' => 5,
                'name' => 'strat de chokbar',
                'activation_status' => 'DEACTIVATED',
            ],
            $pricing_strategy_archive
        );
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function test_expired(): void
    {
        $manager = $this->getTestedInstance();

        $pricing_strategies_config_expired_before = $this->fetchAllPricingStrategyByStatus(
            PricingStrategyStatus::EXPIRED
        );
        $this->assertCount(0, $pricing_strategies_config_expired_before);

        $manager->pricingStrategyExpired();

        $pricing_strategies_config_expired_after = $this->fetchAllPricingStrategyByStatus(
            PricingStrategyStatus::EXPIRED
        );
        $this->assertCount(2, $pricing_strategies_config_expired_after);
    }

    private function fetchPricingStrategy(int $pricing_strategy_id): array
    {
        $sql = <<<SQL
            SELECT pricing_strategy_id, name, activation_status
            FROM backOffice.pricing_strategy
            WHERE pricing_strategy_id = :pricing_strategy_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['pricing_strategy_id' => $pricing_strategy_id]);
    }

    /** @throws SqlErrorMessageException */
    private function fetchAllPricingStrategyByStatus(string $status): array
    {
        $sql = <<<SQL
            SELECT pricing_strategy_id, name, activation_status
            FROM backOffice.pricing_strategy
            WHERE activation_status = :activation_status
        SQL;

        return $this->getPdo()->fetchAll($sql, ['activation_status' => $status]);
    }

    /** @throws SqlErrorMessageException */
    private function fetchLastPricingStrategyArchive(int $pricing_strategy_id): array
    {
        $sql = <<<SQL
            SELECT pricing_strategy_id, name, activation_status
            FROM backOffice.pricing_strategy_archive
            WHERE pricing_strategy_id = :pricing_strategy_id
            ORDER BY pricing_strategy_archive_id DESC
            LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql, ['pricing_strategy_id' => $pricing_strategy_id]);
    }

    private function getUser(): UserEntity
    {
        return self::$container->get(AccountQueryRepository::class)->getUser('admin');
    }

    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
