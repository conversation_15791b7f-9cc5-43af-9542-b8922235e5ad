<?php

namespace PHPUnit\Unit\Erp\System\Mysql\Repository;

use App\Exception\SqlErrorMessageException;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Referential\Marketplace\EasyLounge;
use SonVideo\Erp\System\Mysql\Repository\SystemVariableRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SystemVariableRepositoryTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    private function getTestedClass(): SystemVariableRepository
    {
        return self::$container->get(SystemVariableRepository::class);
    }

    /**
     * Tests the variable datetime methods.
     *
     * @throws SqlErrorMessageException
     * @throws \Exception
     */
    public function test_variable_datetime(): void
    {
        // Try to retrieve non-existing variable
        $datetime = $this->getTestedClass()->getVariableDatetime(EasyLounge::SYSTEM_VARIABLE_START_AT);
        $this->assertNull($datetime);

        // Register and fetch start_datetime for a new variable
        $then = new \Datetime('1980-06-11 13:45:00');
        $testedClass = $this->getTestedClass()->setVariableDatetime(EasyLounge::SYSTEM_VARIABLE_START_AT, $then);

        $datetime = $testedClass->getVariableDatetime(EasyLounge::SYSTEM_VARIABLE_START_AT);
        $this->assertEquals($then->format('Y-m-d H:i:s'), $datetime);

        // Register and fetch start_datetime for an existing variable
        $now = new \DateTime('now');
        $testedClass = $this->getTestedClass()->setVariableDatetime(EasyLounge::SYSTEM_VARIABLE_START_AT, $now);

        $datetime = $testedClass->getVariableDatetime(EasyLounge::SYSTEM_VARIABLE_START_AT);
        $this->assertEquals($now->format('Y-m-d H:i:s'), $datetime);
    }
}
