<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\System\ValueObject;

use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class LoggableSystemEventTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the class instantiation. */
    public function test_class_instantiation(): void
    {
        // Instantiate class with no relationships
        $instance = new LoggableSystemEvent(LoggableSystemEvent::SYSTEM_ERP_PG, 'dummy', ['_rel' => []]);

        $payload = $instance->getPayload();
        $this->assertInstanceOf(\stdClass::class, $payload['_rel']);
    }
}
