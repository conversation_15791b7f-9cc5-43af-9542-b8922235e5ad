<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Quote\Manager\QuoteShippingEligibilityManager as TestedClass;
use SonVideo\Erp\Referential\Rpc\BridgeRpcMethodReferential;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class QuoteShippingEligibilityManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/get_quote_shipment_methods.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    public function test_retrieve_eligible_shipment_methods(): void
    {
        RpcClientServiceMock::savedResult(
            BridgeRpcMethodReferential::SERVER_NAME,
            BridgeRpcMethodReferential::CARRIER_ELIGIBILTY_ENDPOINT,
            '[]'
        );

        $this->getTestedInstance()->retrieveEligibleShipmentMethods(14);

        $formatted_articles_in_payload = RpcClientServiceMock::getLastCall()['args'][0]['items'];

        $this->assertEquals(
            [
                [
                    'article_id' => 81078,
                    'type' => 'article',
                    'sku' => 'ARCAMRBLINKNR',
                    'quantity' => 1,
                    'total_price' => 249,
                    'price_vat_excluded' => 207.5,
                    'price' => 249,
                    'order_line_no' => 1,
                    'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                ],
                [
                    'article_id' => 81079,
                    'type' => 'article',
                    'sku' => 'DESTOCK-ARCAMRBLINKNR',
                    'quantity' => 1,
                    'total_price' => 149,
                    'price_vat_excluded' => 207.5,
                    'price' => 249,
                    'order_line_no' => 2,
                    'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                ],
                [
                    'article_id' => 13895,
                    'type' => 'compose',
                    'sku' => 'NORSTCL25025M',
                    'quantity' => 1,
                    'total_price' => 239,
                    'price_vat_excluded' => 207.5,
                    'price' => 249,
                    'order_line_no' => 3,
                    'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                ],
            ],
            $formatted_articles_in_payload
        );
    }
}
