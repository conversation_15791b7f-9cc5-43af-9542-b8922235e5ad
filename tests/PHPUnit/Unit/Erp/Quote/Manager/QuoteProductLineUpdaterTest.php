<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Quote\Manager\QuoteProductLineUpdater as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteProductLineUpdaterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/put_quote_line_product.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /**
     * Most cases are handled in the e2e tests (quote/put_quote_line_product.feature)
     * Only tests requiring to read result in database afterward are done here.
     */
    public function test_update_one_with(): void
    {
        $this->getTestedInstance()->updateOneWith(
            12,
            3,
            ['quantity' => 3],
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        $quote_line_products = $this->fetchQuoteLineProduct(3);

        $this->assertEquals(81078, (int) $quote_line_products[0]->product_id);
        $this->assertEquals(3, (int) $quote_line_products[0]->quantity);
    }

    protected function fetchQuoteLineProduct(int $quote_line_product_id): array
    {
        $sql = <<<SQL
        SELECT
          qlp.quote_line_product_id,
          qlp.quote_line_id,
          qlp.product_id,
          qlp.quantity,
          qlp.unit_discount_amount,
          qlp.product,
          qlp.selected_warranties,
          ql.quote_id
          FROM backOffice.quote_line_product qlp
          INNER JOIN backOffice.quote_line ql ON qlp.quote_line_id = ql.quote_line_id
        WHERE quote_line_product_id = :quote_line_product_id
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['quote_line_product_id' => $quote_line_product_id]);
    }
}
