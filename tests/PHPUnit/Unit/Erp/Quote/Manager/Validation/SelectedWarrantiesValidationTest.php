<?php

namespace PHPUnit\Unit\Erp\Quote\Manager\Validation;

use SonVideo\Erp\Quote\Manager\Validation\SelectedWarrantiesValidation as TestedClass;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class SelectedWarrantiesValidationTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    private function getValidator(): ValidatorInterface
    {
        return self::$container->get(ValidatorInterface::class);
    }

    public function test_rules_when_all_keys_are_null(): void
    {
        $selected_warranties = <<<JSON
        [{
            "type": null,
            "label": null,
            "unit_selling_price_tax_included": null,
            "duration": null,
            "quantity": null
        }]
        JSON;

        $errors = $this->getValidator()->validate(
            ['selected_warranties' => json_decode($selected_warranties, true)],
            TestedClass::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertCount(5, $messages);
        $this->assertEquals(
            [
                '[selected_warranties][0][type]' => 'This value should not be blank.',
                '[selected_warranties][0][label]' => 'This value should not be blank.',
                '[selected_warranties][0][unit_selling_price_tax_included]' => 'This value should not be blank.',
                '[selected_warranties][0][duration]' => 'This value should not be blank.',
                '[selected_warranties][0][quantity]' => 'This field was not expected.',
            ],
            $messages
        );
    }

    public function test_rules_when_all_keys_are_blank(): void
    {
        $selected_warranties = <<<JSON
        [{
            "type": "",
            "label": "",
            "unit_selling_price_tax_included": "",
            "duration": ""
        }]
        JSON;

        $errors = $this->getValidator()->validate(
            ['selected_warranties' => json_decode($selected_warranties, true)],
            TestedClass::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertCount(4, $messages);
        $this->assertEquals(
            [
                '[selected_warranties][0][type]' => 'The value you selected is not a valid choice.',
                '[selected_warranties][0][label]' => 'This value should not be blank.',
                '[selected_warranties][0][unit_selling_price_tax_included]' => 'This value should be of type numeric.',
                '[selected_warranties][0][duration]' => 'This value should be of type numeric.',
            ],
            $messages
        );
    }

    public function test_miscellaneous_rules_with_invalid_types(): void
    {
        $selected_warranties = <<<JSON
        [{
            "type": "baz",
            "label": 123,
            "unit_selling_price_tax_included": "foo",
            "duration": "bar"
        }]
        JSON;

        $errors = $this->getValidator()->validate(
            ['selected_warranties' => json_decode($selected_warranties, true)],
            TestedClass::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertCount(4, $messages);
        $this->assertEquals(
            [
                '[selected_warranties][0][type]' => 'The value you selected is not a valid choice.',
                '[selected_warranties][0][label]' => 'This value should be of type string.',
                '[selected_warranties][0][unit_selling_price_tax_included]' => 'This value should be of type numeric.',
                '[selected_warranties][0][duration]' => 'This value should be of type numeric.',
            ],
            $messages
        );
    }

    public function test_miscellaneous_rules_with_negative_values(): void
    {
        $selected_warranties = <<<JSON
        [{
            "type": "extension",
            "label": "Valid",
            "unit_selling_price_tax_included": -1,
            "duration": -1
        }]
        JSON;

        $errors = $this->getValidator()->validate(
            ['selected_warranties' => json_decode($selected_warranties, true)],
            TestedClass::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertCount(2, $messages);
        $this->assertEquals(
            [
                '[selected_warranties][0][unit_selling_price_tax_included]' => 'This value should be either positive or zero.',
                '[selected_warranties][0][duration]' => 'This value should be either positive or zero.',
            ],
            $messages
        );
    }
}
