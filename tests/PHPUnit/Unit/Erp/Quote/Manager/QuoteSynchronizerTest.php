<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Quote\Manager\QuoteSynchronizer as TestedClass;
use SonVideo\Erp\Referential\Rpc\BoCmsRpcMethodReferential;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteSynchronizerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'quote/quote_sync.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    public function test_sync(): void
    {
        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "updated_quote_id": 12
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::QUOTE_UPSERT_METHOD,
            $expected_success_response
        );

        // Check last sync date before sync
        $variable = $this->fetchQuoteSyncDate();
        $this->assertEquals('1995-02-06 16:10:00', $variable['BO_SYS_datetime']);

        // Perform sync
        $this->getTestedInstance()->sync();

        // Check last sync date after sync
        $variable = $this->fetchQuoteSyncDate();
        $this->assertEquals('2022-02-03 16:10:00', $variable['BO_SYS_datetime']);
    }

    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    private function fetchQuoteSyncDate(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.BO_SYS_variable
        WHERE BO_SYS_variable = 'quote.synchronized_at'
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }
}
