<?php

namespace PHPUnit\Unit\Erp\Quote\Manager\Decorator;

use App\DataLoader\EntityDataLoader;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Quote\Entity\ProductLineInfoEntity;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Entity\QuoteLineEntity;
use SonVideo\Erp\Quote\Manager\Decorator\QuoteDecorator as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteDecoratorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['customer/get_customer_by_id.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** @throws \Exception */
    protected function getDataLoader()
    {
        return self::$container->get(EntityDataLoader::class);
    }

    protected function getStrictMinimalQuote(string $type = QuoteEntity::TYPE_QUOTATION): QuoteEntity
    {
        $quote = (new QuoteEntity())->setDataLoader($this->getDataLoader());
        $quote->type = $type;
        $quote->customer_id = 2;
        $quote->quote_id = 1;
        $quote->created_at = new \DateTime('2021-05-27 15:37:00');
        $quote->valid_until = 15;
        $quote->vat_rate = 0.2;

        return $quote;
    }

    /** @throws \Exception */
    public function test_decorate_with_strict_minimal_data(): void
    {
        $quote = $this->getStrictMinimalQuote();
        $decorated = $this->getTestedInstance()->decorate($quote);

        // has keys from the entity
        $this->assertArrayHasKey('quote_id', $decorated);
        $this->assertArrayHasKey('type', $decorated);
        $this->assertArrayHasKey('created_at', $decorated);
        $this->assertArrayHasKey('intra_community_vat', $decorated);
        $this->assertArrayHasKey('sent_at', $decorated);
        $this->assertArrayHasKey('expired_at', $decorated);
        $this->assertArrayHasKey('valid_until', $decorated);
        $this->assertArrayHasKey('message', $decorated);
        $this->assertArrayHasKey('billing_address', $decorated);
        $this->assertArrayHasKey('shipping_address', $decorated);
        $this->assertArrayHasKey('shipment_method', $decorated);
        $this->assertArrayHasKey('vat_rate', $decorated);
        $this->assertArrayHasKey('quote_line_aggregates', $decorated);
        $this->assertArrayHasKey('customer_order_aggregates', $decorated);
        $this->assertArrayHasKey('locked', $decorated);
        $this->assertArrayHasKey('remaining_steps_to_convert_to_quotation', $decorated);
        $this->assertArrayHasKey('remaining_steps_to_convert_to_offer', $decorated);
        $this->assertArrayHasKey('remaining_steps_to_convert_to_customer_order', $decorated);

        // some data are forced to a default value
        $this->assertEquals('', $decorated['message']);
        $this->assertCount(0, $decorated['quote_line_aggregates']);

        // it also has additional computed keys
        $this->assertEquals(0, $decorated['articles_count']);
        $this->assertEquals(0.0, $decorated['shipment_method']['cost']);
        $this->assertEquals('-', $decorated['shipment_method']['cost_formatted']);
        $this->assertEquals(0.0, $decorated['shipment_method']['cost_tax_excluded']);
        $this->assertEquals('-', $decorated['shipment_method']['cost_tax_excluded_formatted']);
        $this->assertEquals(0.0, $decorated['prices']['total_discount_tax_excluded']);
        $this->assertEquals(0.0, $decorated['prices']['total_discount_tax_included']);
        $this->assertEquals('0,00 €', $decorated['prices']['total_discount_tax_included_formatted']);
        $this->assertEquals(0.0, $decorated['prices']['total_price_tax_excluded']);
        $this->assertEquals(0.0, $decorated['prices']['total_vat']);
        $this->assertEquals('0,00 €', $decorated['prices']['total_vat_formatted']);
        $this->assertEquals(0.0, $decorated['prices']['total_price_tax_included']);
        $this->assertEquals('0,00 €', $decorated['prices']['total_price_tax_included_formatted']);
        $this->assertEquals('27/05/2021 15:37', $decorated['created_at_formatted']);
        $this->assertEquals('-', $decorated['expired_at_formatted']);
    }

    /** @throws \Exception */
    public function test_decorate_formats_expired_at(): void
    {
        $quote = $this->getStrictMinimalQuote();
        $quote->expired_at = new \DateTime('2021-06-15 23:59:59');

        $decorated = $this->getTestedInstance()->decorate($quote);

        $this->assertEquals('15/06/2021 23:59:59', $decorated['expired_at_formatted']);
    }

    /** @throws \Exception */
    public function test_decorate_shipment_method(): void
    {
        $quote = $this->getStrictMinimalQuote();
        $quote->shipment_method = [
            'cost' => 15.9,
            'shipment_method_id' => 1000,
        ];

        $decorated = $this->getTestedInstance()->decorate($quote);

        $this->assertEquals(15.9, $decorated['shipment_method']['cost']);
        $this->assertEquals('15,90 €', $decorated['shipment_method']['cost_formatted']);
        $this->assertEquals(13.25, $decorated['shipment_method']['cost_tax_excluded']);
        $this->assertEquals('13,25 €', $decorated['shipment_method']['cost_tax_excluded_formatted']);
    }

    public function test_disable_shipment_method_on_offer(): void
    {
        $quote = $this->getStrictMinimalQuote(QuoteEntity::TYPE_OFFER);
        $quote->shipment_method = [
            'cost' => 15.9,
            'shipment_method_id' => 1000,
        ];

        $decorated = $this->getTestedInstance()->decorate($quote);

        $this->assertEquals(0.0, $decorated['shipment_method']['cost']);
        $this->assertEquals('-', $decorated['shipment_method']['cost_formatted']);
        $this->assertEquals(0.0, $decorated['shipment_method']['cost_tax_excluded']);
        $this->assertEquals('-', $decorated['shipment_method']['cost_tax_excluded_formatted']);
    }

    /** @throws \Exception */
    public function test_decorate_products_and_prices(): void
    {
        $quote = $this->getStrictMinimalQuote();
        $quote->shipment_method = [
            'cost' => 15.9,
            'shipment_method_id' => 10000000,
        ];

        $product_info_1 = (new ProductLineInfoEntity())
            ->setDataLoader($this->getDataLoader())
            ->fromArray([
                'quantity' => 2,
                'unit_discount_amount' => 35.47,
                'product' => [
                    'product_id' => 1234,
                    'sku' => 'PRODUCT1234',
                    'description' => 'Some description',
                    'short_description' => 'Desc.',
                    'selling_price_tax_included' => 1590.5,
                    'ecotax_price' => 1.5,
                    'sorecop_price' => 7.3,
                    'vat' => 0.2,
                    'purchase_price' => 987.68,
                    'type' => 'article',
                ],
                'selected_warranties' => [
                    [
                        'type' => 'extension',
                        'label' => 'Extension de garantie 5 ans',
                        'unit_selling_price_tax_included' => 99.5,
                        'duration' => 5,
                    ],
                    [
                        'type' => 'theft_breakdown',
                        'label' => 'Garantie vol-casse 2 ans',
                        'unit_selling_price_tax_included' => 12.34,
                        'duration' => 2,
                    ],
                ],
            ])
            ->toArray();

        $product_line_1 = (new QuoteLineEntity())->setDataLoader($this->getDataLoader())->fromArray([
            'quote_line_id' => 1,
            'display_order' => 1,
        ]);

        $product_line_1->type = QuoteLineEntity::TYPE_PRODUCT;
        $product_line_1->data = $product_info_1;

        $quote->quote_line_aggregates = [$product_line_1];

        $decorated = $this->getTestedInstance()->decorate($quote);

        // Test product decoration
        $this->assertEqualsWithDelta(
            1325.41666666666,
            $decorated['quote_line_aggregates'][0]['data']['selling_price_tax_excluded'],
            0.001
        );
        $this->assertEqualsWithDelta(
            35.47,
            $decorated['quote_line_aggregates'][0]['data']['unit_discount_amount_abs_tax_included'],
            0.001
        );
        $this->assertEqualsWithDelta(
            29.5583333333333,
            $decorated['quote_line_aggregates'][0]['data']['unit_discount_amount_abs_tax_excluded'],
            0.001
        );
        $this->assertEqualsWithDelta(
            2591.71666666666,
            $decorated['quote_line_aggregates'][0]['data']['total_price_tax_excluded'],
            0.001
        );
        $this->assertEqualsWithDelta(
            3110.06,
            $decorated['quote_line_aggregates'][0]['data']['total_price_tax_included'],
            0.001
        );
        $this->assertEqualsWithDelta(
            1590.5,
            $decorated['quote_line_aggregates'][0]['data']['selling_price_tax_included'],
            0.001
        );
        $this->assertEquals(
            '1 590,50 €',
            $decorated['quote_line_aggregates'][0]['data']['selling_price_tax_included_formatted']
        );
        $this->assertEqualsWithDelta(
            70.94,
            $decorated['quote_line_aggregates'][0]['data']['total_discount_amount'],
            0.001
        );
        $this->assertEquals(
            '70,94 €',
            $decorated['quote_line_aggregates'][0]['data']['total_discount_amount_formatted']
        );
        $this->assertEqualsWithDelta(3110.06, $decorated['quote_line_aggregates'][0]['data']['total_price'], 0.001);
        $this->assertEquals('3 110,06 €', $decorated['quote_line_aggregates'][0]['data']['total_price_formatted']);
        $this->assertEquals('', $decorated['quote_line_aggregates'][0]['data']['url']);

        // Test first warranty
        $this->assertEquals('product_warranty', $decorated['quote_line_aggregates'][1]['type']);
        $this->assertEquals('Extension de garantie 5 ans', $decorated['quote_line_aggregates'][1]['data']['label']);
        $this->assertEqualsWithDelta(
            99.5,
            $decorated['quote_line_aggregates'][1]['data']['unit_selling_price_tax_included'],
            0.001
        );
        $this->assertEquals('99,50 €', $decorated['quote_line_aggregates'][1]['data']['price_tax_included_formatted']);
        $this->assertEqualsWithDelta(
            82.916666666667,
            $decorated['quote_line_aggregates'][1]['data']['price_tax_excluded'],
            0.001
        );
        $this->assertEquals('82,92 €', $decorated['quote_line_aggregates'][1]['data']['price_tax_excluded_formatted']);
        $this->assertEquals(5, $decorated['quote_line_aggregates'][1]['data']['duration']);
        $this->assertEquals(2, $decorated['quote_line_aggregates'][1]['data']['quantity']);
        $this->assertEquals('extension', $decorated['quote_line_aggregates'][1]['data']['type']);
        $this->assertEqualsWithDelta(
            199,
            $decorated['quote_line_aggregates'][1]['data']['total_price_tax_included'],
            0.001
        );
        $this->assertEquals(
            '199,00 €',
            $decorated['quote_line_aggregates'][1]['data']['total_price_tax_included_formatted']
        );
        $this->assertEqualsWithDelta(
            165.8333333333333,
            $decorated['quote_line_aggregates'][1]['data']['total_price_tax_excluded'],
            0.001
        );
        $this->assertEquals(
            '165,83 €',
            $decorated['quote_line_aggregates'][1]['data']['total_price_tax_excluded_formatted']
        );

        // Test second warranty
        $this->assertEquals(2, $decorated['quote_line_aggregates'][2]['data']['quantity']);
        $this->assertEqualsWithDelta(
            24.68,
            $decorated['quote_line_aggregates'][2]['data']['total_price_tax_included'],
            0.001
        );
        $this->assertEquals(
            '24,68 €',
            $decorated['quote_line_aggregates'][2]['data']['total_price_tax_included_formatted']
        );
        $this->assertEqualsWithDelta(
            20.566666666667,
            $decorated['quote_line_aggregates'][2]['data']['total_price_tax_excluded'],
            0.001
        );
        $this->assertEquals(
            '20,57 €',
            $decorated['quote_line_aggregates'][2]['data']['total_price_tax_excluded_formatted']
        );

        // Test prices decoration
        $this->assertEqualsWithDelta(59.116666666666, $decorated['prices']['total_discount_tax_excluded'], 0.001);
        $this->assertEqualsWithDelta(70.94, $decorated['prices']['total_discount_tax_included'], 0.001);
        $this->assertEquals('70,94 €', $decorated['prices']['total_discount_tax_included_formatted']);
        $this->assertEqualsWithDelta(2791.3666666667, $decorated['prices']['total_price_tax_excluded'], 0.001);
        $this->assertEqualsWithDelta(558.27333333333, $decorated['prices']['total_vat'], 0.001);
        $this->assertEquals('558,27 €', $decorated['prices']['total_vat_formatted']);
        $this->assertEqualsWithDelta(3349.64, $decorated['prices']['total_price_tax_included'], 0.001);
        $this->assertEquals('3 349,64 €', $decorated['prices']['total_price_tax_included_formatted']);
    }
}
