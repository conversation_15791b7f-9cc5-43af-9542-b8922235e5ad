<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Exception\InternalErrorException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Product\Entity\ProductV2Entity;
use SonVideo\Erp\Quote\Exception\ConvertQuoteException;
use SonVideo\Erp\Quote\Manager\QuoteToCustomerOrderConverter as TestedClass;
use SonVideo\Erp\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Product;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteToCustomerOrderConverterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/post_quote_to_customer_order.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    public function test_convert_expired_quote(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $converter = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Quote can not be converted');

        try {
            $converter->convert(100, $user);
        } catch (InternalErrorException $e) {
            $message = $e->getContext();
            $this->assertCount(2, $message);
            $this->assertEquals('expired', $message[0]['error']);
            $this->assertEquals('The quote has expired', $message[0]['message']);
            $this->assertEquals('quote_lines_empty', $message[1]['error']);
            $this->assertEquals('The quote has no line', $message[1]['message']);

            throw $e;
        }
    }

    public function test_convert_with_empty_billing_address(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $converter = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Quote can not be converted');

        try {
            $converter->convert(101, $user);
        } catch (InternalErrorException $e) {
            $message = $e->getContext();
            $this->assertCount(2, $message);
            $this->assertEquals('billing_address_empty', $message[0]['error']);
            $this->assertEquals('The billing address can\'t be empty', $message[0]['message']);
            $this->assertEquals('quote_lines_empty', $message[1]['error']);
            $this->assertEquals('The quote has no line', $message[1]['message']);

            throw $e;
        }
    }

    public function test_convert_with_empty_shipping_address(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $converter = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Quote can not be converted');

        try {
            $converter->convert(102, $user);
        } catch (InternalErrorException $e) {
            $message = $e->getContext();
            $this->assertCount(2, $message);
            $this->assertEquals('shipping_address_empty', $message[0]['error']);
            $this->assertEquals('The shipping address can\'t be empty', $message[0]['message']);
            $this->assertEquals('quote_lines_empty', $message[1]['error']);
            $this->assertEquals('The quote has no line', $message[1]['message']);

            throw $e;
        }
    }

    public function test_convert_with_empty_shipment_method(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $converter = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Quote can not be converted');

        try {
            $converter->convert(103, $user);
        } catch (InternalErrorException $e) {
            $message = $e->getContext();
            $this->assertCount(2, $message);
            $this->assertEquals('shipment_method_empty', $message[0]['error']);
            $this->assertEquals('The shipment method can\'t be empty', $message[0]['message']);
            $this->assertEquals('quote_lines_empty', $message[1]['error']);
            $this->assertEquals('The quote has no line', $message[1]['message']);

            throw $e;
        }
    }

    public function test_convert_with_all_possible_errors(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $converter = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Quote can not be converted');

        try {
            $converter->convert(105, $user);
        } catch (InternalErrorException $e) {
            $message = $e->getContext();
            $this->assertCount(5, $message);

            throw $e;
        }
    }

    public function test_convert_with_nonexistent_shipment_method(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $converter = $this->getTestedInstance();

        $this->expectException(ConvertQuoteException::class);
        $this->expectExceptionMessage('An error occurred while converting the quote');

        $converter->convert(200, $user);
    }

    public function test_convert_quotation_to_customer_order(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $quote_id = 13;
        $date = new \DateTime();

        $customer_order_id = $this->getTestedInstance()->convert($quote_id, $user);

        // Test customer order
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);

        $this->assertEquals(1235, $customer_order['id_commande']);
        $this->assertEquals(1235, $customer_order['no_commande_origine']);
        $this->assertEquals('backoffice.sonvideopro.com', $customer_order['creation_origine']);

        $customer_date = $customer_order['date_creation'];
        $this->assertLessThan(2, $date->diff(new \DateTime($customer_date))->s);

        $this->assertEquals('traitement', $customer_order['flux']);
        $this->assertEquals('************', $customer_order['ip']);
        $this->assertEquals('N', $customer_order['rappel_client']);
        $this->assertEquals(5, $customer_order['depot_emport']);
        $this->assertEquals('non', $customer_order['detaxe_export']);
        $this->assertEquals(1, $customer_order['cmd_intragroupe']);
        $this->assertEquals(1, $customer_order['id_prospect']);
        $this->assertNull($customer_order['id_devis']);
        $this->assertEquals(13, $customer_order['quote_id']);
        $this->assertEquals('', $customer_order['commentaire_facture']);
        $this->assertEquals(2, $customer_order['compteur_paiement']);
        $this->assertNull($customer_order['tradedoubler_id']);
        $this->assertEquals(0, $customer_order['nombre_visite']);
        $this->assertNull($customer_order['clef']);
        $this->assertNull($customer_order['promotion_id']);
        $this->assertEquals(0, $customer_order['rdv_socol']);
        $this->assertEquals(31, $customer_order['id_transporteur']);
        $this->assertEquals(37, $customer_order['id_pdt_transporteur']);
        $this->assertNull($customer_order['tpt_option_code']);
        $this->assertEquals(5, $customer_order['warehouse_id']);
        $this->assertEquals(1, $customer_order['sales_channel_id']);

        // Test billing address
        $this->assertEquals('particulier', $customer_order['cnt_fct_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_fct_email']);
        $this->assertEquals('', $customer_order['cnt_fct_societe']);
        $this->assertEquals('Mme', $customer_order['cnt_fct_civilite']);
        $this->assertEquals('Lorillard', $customer_order['cnt_fct_nom']);
        $this->assertEquals('Arthur', $customer_order['cnt_fct_prenom']);
        $this->assertEquals(
            '38 rue de la ville en bois, le
grand portail gris, tout au
fond de la cour sur la gauche',
            $customer_order['cnt_fct_adresse']
        );
        $this->assertEquals('44000', $customer_order['cnt_fct_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_fct_ville']);
        $this->assertEquals(67, $customer_order['cnt_fct_id_pays']);
        $this->assertEquals('', $customer_order['cnt_fct_telephone']);
        $this->assertEquals('', $customer_order['cnt_fct_telephone_bureau']);
        $this->assertEquals('06 97 97 97 97', $customer_order['cnt_fct_mobile']);
        $this->assertEquals('', $customer_order['cnt_fct_fax']);
        $this->assertNull($customer_order['cnt_fct_numero_tva']);

        // Test shipping address
        $this->assertEquals('particulier', $customer_order['cnt_lvr_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_lvr_email']);
        $this->assertEquals('', $customer_order['cnt_lvr_societe']);
        $this->assertEquals('M.', $customer_order['cnt_lvr_civilite']);
        $this->assertEquals('Lorillard', $customer_order['cnt_lvr_nom']);
        $this->assertEquals('Arthur', $customer_order['cnt_lvr_prenom']);
        $this->assertEquals(
            '38 rue de la ville en bois, le
grand portail gris, tout au
fond de la cour sur la gauche',
            $customer_order['cnt_lvr_adresse']
        );
        $this->assertEquals('44000', $customer_order['cnt_lvr_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_lvr_ville']);
        $this->assertEquals(67, $customer_order['cnt_lvr_id_pays']);
        $this->assertEquals('', $customer_order['cnt_lvr_telephone']);
        $this->assertEquals('', $customer_order['cnt_lvr_telephone_bureau']);
        $this->assertEquals('06 97 97 97 97', $customer_order['cnt_lvr_mobile']);
        $this->assertEquals('', $customer_order['cnt_lvr_fax']);
        $this->assertNull($customer_order['cnt_lvr_numero_tva']);

        // Test customer order products
        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);

        // First product
        $this->assertEquals($customer_order_id, $customer_order_products[0]->id_commande);
        $this->assertEquals(81078, $customer_order_products[0]->id_produit);
        $this->assertEquals(3, $customer_order_products[0]->quantite);
        $this->assertEquals(249.0, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(131.58, $customer_order_products[0]->prix_achat);
        $this->assertEquals('Récepteur Audio Bluetooth APTX Arcam rBlink', $customer_order_products[0]->description);
        $this->assertEquals('devis', $customer_order_products[0]->remise_type);
        $this->assertEquals(-20, $customer_order_products[0]->remise_montant);
        $this->assertEquals('Remise devis', $customer_order_products[0]->remise_description);
        $this->assertEquals(0.15, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertEquals('devis', $customer_order_products[0]->groupe_type);
        $this->assertEquals('Remise devis', $customer_order_products[0]->groupe_description);
        $this->assertNull($customer_order_products[0]->duree_garantie_ext);
        $this->assertEquals(0.0, $customer_order_products[0]->prix_garantie_ext);
        $this->assertNull($customer_order_products[0]->duree_garantie_vc);
        $this->assertEquals(0.0, $customer_order_products[0]->prix_garantie_vc);

        // Second product
        $this->assertEquals($customer_order_id, $customer_order_products[1]->id_commande);
        $this->assertEquals(13895, $customer_order_products[1]->id_produit);
        $this->assertEquals(1, $customer_order_products[1]->quantite);
        $this->assertEquals(249.0, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(16.75, $customer_order_products[1]->prix_achat);
        $this->assertEquals('Récepteur Audio Bluetooth APTX Arcam rBlink', $customer_order_products[1]->description);
        $this->assertNull($customer_order_products[1]->remise_type);
        $this->assertEquals(0, $customer_order_products[1]->remise_montant);
        $this->assertNull($customer_order_products[1]->remise_description);
        $this->assertEquals(0.15, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertNull($customer_order_products[1]->groupe_type);
        $this->assertNull($customer_order_products[1]->groupe_description);
        $this->assertEquals(5, $customer_order_products[1]->duree_garantie_ext);
        $this->assertEquals(99.0, $customer_order_products[1]->prix_garantie_ext);
        $this->assertNull($customer_order_products[1]->duree_garantie_vc);
        $this->assertEquals(0.0, $customer_order_products[1]->prix_garantie_vc);

        // Shipment cost
        $this->assertEquals($customer_order_id, $customer_order_products[2]->id_commande);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_products[2]->id_produit);
        $this->assertEquals(1, $customer_order_products[2]->quantite);
        $this->assertEquals(10.0, $customer_order_products[2]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[2]->tva);
        $this->assertEquals(0, $customer_order_products[2]->prix_achat);
        $this->assertEquals(
            ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            $customer_order_products[2]->description
        );
        $this->assertNull($customer_order_products[2]->remise_type);
        $this->assertEquals(0, $customer_order_products[2]->remise_montant);
        $this->assertNull($customer_order_products[2]->remise_description);
        $this->assertEquals(0, $customer_order_products[2]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[2]->prix_sorecop);
        $this->assertNull($customer_order_products[2]->groupe_type);
        $this->assertNull($customer_order_products[2]->groupe_description);

        // Test creation log on quote
        $log = $this->fetchOneLog($quote_id);
        $payload = json_decode($log['payload'], false);

        $this->assertEquals($quote_id, $payload->_rel->quote);
        $this->assertEquals($customer_order_id, $payload->data->customer_order_id);
        $this->assertEquals(1, $payload->meta->converted_by->user_id);

        // Test quote expiration and type
        $quote = $this->fetchOneQuote($quote_id);
        $this->assertEquals(
            (new \DateTime('+30 days'))->setTime(23, 59, 59)->format('Y-m-d H:i:s'),
            $quote['expired_at']
        );
        $this->assertEquals('quotation', $quote['type']);

        // Test source tag
        $tags = $this->fetchTags($customer_order_id);
        $this->assertCount(1, $tags);
        $this->assertEquals(CustomerOrderTag::SOURCE_INTRAGROUP, $tags[0]['tag_id']);
    }

    public function test_convert_draft_to_customer_order(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $quote_id = 14;

        // Check quote type before conversion
        $quote = $this->fetchOneQuote($quote_id);
        $this->assertEquals('draft', $quote['type']);

        // Convert
        $order_id = $this->getTestedInstance()->convert($quote_id, $user);

        // Check quote type after conversion
        $quote = $this->fetchOneQuote($quote_id);
        $this->assertEquals('offer', $quote['type']);

        // Check if order is B2B
        $this->assertTrue($this->isOrderB2B($order_id));

        // Check source tag
        $tags = $this->fetchTags($order_id);
        $this->assertCount(1, $tags);
        $this->assertEquals(CustomerOrderTag::SOURCE_SHOP_SV, $tags[0]['tag_id']);
    }

    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    protected function fetchOneCustomerOrder(int $customer_order_id)
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.commande
        WHERE id_commande = :customer_order_id
        MYSQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    protected function fetchCustomerOrderProducts(int $customer_order_id)
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.produit_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id_produit DESC
        MYSQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    protected function fetchOneLog(int $quote_id)
    {
        $sql = <<<MYSQL
        SELECT *
          FROM backOffice.system_event
        WHERE name = 'quote.customer_order'
        AND payload->"$._rel.quote" = :quote_id
        AND main_id = :quote_id
        ;
        MYSQL;

        return $this->getPdo()->fetchOne($sql, ['quote_id' => $quote_id]);
    }

    protected function fetchOneQuote(int $quote_id)
    {
        $sql = <<<MYSQL
        SELECT *
          FROM backOffice.quote
        WHERE quote_id = :quote_id
        ;
        MYSQL;

        return $this->getPdo()->fetchOne($sql, ['quote_id' => $quote_id]);
    }

    protected function isOrderB2B(int $order_id): bool
    {
        $sql = <<<MYSQL
        SELECT COUNT(id_commande)
          FROM backOffice.commande_btob
        WHERE id_commande = :order_id
        ;
        MYSQL;

        return 1 === count($this->getPdo()->fetchOne($sql, ['order_id' => $order_id]));
    }

    protected function fetchTags(int $order_id): array
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.commande_tag
        WHERE id_commande = :order_id
        ;
        MYSQL;

        return $this->getPdo()->fetchAll($sql, ['order_id' => $order_id]);
    }
}
