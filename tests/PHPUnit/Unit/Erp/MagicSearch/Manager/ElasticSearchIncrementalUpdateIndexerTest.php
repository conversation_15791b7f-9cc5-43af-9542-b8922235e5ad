<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\MagicSearch\Manager;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\MagicSearch\Manager\ElasticSearchIncrementalUpdateIndexer as TestedClass;
use SonVideo\Erp\MagicSearch\Manager\Index\DataProvider\IncrementalUpdate\ArticlesIncrementalUpdateIndexDataProvider;
use SonVideo\Erp\MagicSearch\Manager\Index\DataProvider\IncrementalUpdate\CustomersIncrementalUpdateIndexDataProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Output\StreamOutput;

class ElasticSearchIncrementalUpdateIndexerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'magic_search/data.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    protected function getTestedClass(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests the generate method with no data providers. */
    public function test_generate_nothing(): void
    {
        $output = new StreamOutput(fopen('php://memory', 'w', false));
        $result = $this->getTestedClass()->generate($output, []);

        // Nothing should be treated when no data providers is given
        $this->assertCount(0, $result);
    }

    /** Tests the generate method with a data provider. */
    public function test_generate_with_a_data_provider(): void
    {
        MySqlDatabase::loadSpecificFixtures(['magic_search/incremental_update_variables.sql']);

        $output = new StreamOutput(fopen('php://memory', 'w', false));
        $data_provider = self::$container->get(ArticlesIncrementalUpdateIndexDataProvider::class);
        $result = $this->getTestedClass()->generate($output, [$data_provider]);

        // Articles are retrieved successfully via the data provider process
        $this->assertCount(1, $result);
        $this->assertCount(5, $result['articles']);
        $this->assertEquals('articles', $result['articles']['name']);
        $this->assertEquals(3, $result['articles']['collected']);
        $this->assertEquals(3, $result['articles']['indexed']);
        $this->assertEquals(0, $result['articles']['errors']);
        $this->assertEquals('TRUE', $result['articles']['finished']);

        MySqlDatabase::loadSpecificFixtures(['magic_search/incremental_update_variables.sql']);

        $output = new StreamOutput(fopen('php://memory', 'w', false));
        $data_provider = self::$container->get(CustomersIncrementalUpdateIndexDataProvider::class);
        $result = $this->getTestedClass()->generate($output, [$data_provider]);

        // Customers are retrieved successfully via the data provider process
        $this->assertCount(1, $result);
        $this->assertCount(5, $result['customers']);
        $this->assertEquals('customers', $result['customers']['name']);
        $this->assertEquals(2, $result['customers']['collected']);
        $this->assertEquals(2, $result['customers']['indexed']);
        $this->assertEquals(0, $result['customers']['errors']);
        $this->assertEquals('TRUE', $result['customers']['finished']);
    }
}
