<?php

namespace PHPUnit\Unit\Erp\DataWarehouse\Observer;

use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\ArticleHistoryRepository;
use App\Tests\Utils\Database\PgDatabase;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\DataWarehouse\Observer\ArticleUpdateObserver;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleUpdateObserverTest extends KernelTestCase
{
    private ArticleHistoryRepository $article_history_repository;
    private LoggerInterface $logger;
    private ArticleUpdateObserver $observer;

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests that the observer successfully processes an event */
    public function test_run_process_success(): void
    {
        PgDatabase::reloadFixtures();

        $content = [
            'sku' => 'TEST321',
            0 => 'toto',
            'event_date' => '2023-01-01 12:00:00',
        ];

        $observer = self::$container->get(ArticleUpdateObserver::class);

        $reflection = new \ReflectionClass($observer);
        $method = $reflection->getMethod('runProcess');
        $method->setAccessible(true);

        $result = $method->invoke($observer, $content);

        $this->assertTrue($result);

        /** @var ArticleHistoryRepository $article_history_repository */
        $article_history_repository = self::$container->get(ArticleHistoryRepository::class);
        $article_history = $article_history_repository->findOne('sku = :sku', ['sku' => 'TEST321']);

        $this->assertNotNull($article_history);
        $this->assertEquals('UPDATE', $article_history->event_type);
        $this->assertEquals('toto', $article_history->event_author);
        $this->assertEquals('2023-01-01 12:00:00', $article_history->event_date->format('Y-m-d H:i:s'));
    }

    /** Tests that the observer handles exceptions properly */
    public function test_run_process_exception(): void
    {
        $this->article_history_repository = $this->createMock(ArticleHistoryRepository::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->observer = new ArticleUpdateObserver($this->article_history_repository);
        $this->observer->setLogger($this->logger);

        $content = [
            'sku' => 'TEST123',
            0 => 'backoffice',
            'event_date' => '2023-01-01 12:00:00',
        ];

        $exception = new \Exception('Test exception');

        $this->article_history_repository
            ->expects($this->once())
            ->method('insertEvent')
            ->with('UPDATE', $content)
            ->willThrowException($exception);

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with($exception->getMessage());

        $reflection = new \ReflectionClass($this->observer);
        $method = $reflection->getMethod('runProcess');
        $method->setAccessible(true);

        $result = $method->invoke($this->observer, $content);

        $this->assertFalse($result);
    }
}
