<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager;

use App\Adapter\Serializer\SerializerInterface;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicFeedback;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopicCommandFeedbackHandler;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\Console\Output\ConsoleSectionOutput;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class SynchronizableTopicCommandFeedbackHandlerTest extends KernelTestCase
{
    private InputInterface $input_mock;
    private ConsoleOutput $output_mock;
    private SynchronizableTopicCommandFeedbackHandler $feedback_handler;
    private SymfonyStyle $io_mock;
    private ConsoleSectionOutput $feedback_section_mock;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->input_mock = $this->createMock(InputInterface::class);
        $this->output_mock = $this->createMock(ConsoleOutput::class);
        $this->io_mock = $this->createMock(SymfonyStyle::class);
        $this->feedback_section_mock = $this->createMock(ConsoleSectionOutput::class);

        $this->output_mock->method('section')->willReturn($this->feedback_section_mock);

        $this->feedback_handler = new SynchronizableTopicCommandFeedbackHandler(
            $this->input_mock,
            $this->output_mock,
            $this->io_mock
        );
        $this->feedback_handler->setSerializer(self::$container->get(SerializerInterface::class));
        $this->feedback_handler->setLogger(new NullLogger());
    }

    /** Test that the console is not cleared when in verbose mode. */
    public function test_console_not_cleared_in_verbose_mode(): void
    {
        $this->feedback_section_mock
            ->expects($this->any())
            ->method('getVerbosity')
            ->willReturn(OutputInterface::VERBOSITY_VERBOSE);

        $this->feedback_section_mock->expects($this->never())->method('clear');

        $feedback = new SynchronizableTopicFeedback('test_topic', 10, 5, 1);
        $this->feedback_handler->progress($feedback);
    }

    /** Test that the console is cleared when not in verbose mode. */
    public function test_console_cleared_in_normal_mode(): void
    {
        $this->feedback_section_mock
            ->expects($this->any())
            ->method('getVerbosity')
            ->willReturn(OutputInterface::VERBOSITY_NORMAL);

        $this->feedback_section_mock->expects($this->once())->method('clear');

        $feedback = new SynchronizableTopicFeedback('test_topic', 10, 5, 1);
        $this->feedback_handler->progress($feedback);
    }

    /** Test that the end method shows success message in normal mode. */
    public function test_end_shows_success_message_in_normal_mode(): void
    {
        $this->feedback_section_mock
            ->expects($this->any())
            ->method('getVerbosity')
            ->willReturn(OutputInterface::VERBOSITY_NORMAL);

        $this->io_mock->expects($this->once())->method('success');

        $feedback = new SynchronizableTopicFeedback('test_topic', 6, 5, 1);
        $this->feedback_handler->progress($feedback);
        $this->feedback_handler->end();
    }

    /** Test that the end method doesn't show success message in verbose mode. */
    public function test_end_doesnt_show_success_message_in_verbose_mode(): void
    {
        $this->feedback_section_mock
            ->expects($this->any())
            ->method('getVerbosity')
            ->willReturn(OutputInterface::VERBOSITY_VERBOSE);

        $this->io_mock->expects($this->never())->method('success');

        $feedback = new SynchronizableTopicFeedback('test_topic', 6, 5, 1);
        $this->feedback_handler->progress($feedback);
        $this->feedback_handler->end();
    }
}
