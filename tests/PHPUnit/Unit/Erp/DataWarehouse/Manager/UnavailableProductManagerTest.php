<?php

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager;

use App\Database\PgDataWarehouse\DataSchema\AutoStructure\UnavailableProduct;
use App\Database\PgDataWarehouse\DataSchema\CustomerOrderModel;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\PhpUnitPommHelperTrait;
use PommProject\Foundation\Session\Connection;
use SonVideo\Erp\DataWarehouse\Manager\UnavailableProductManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class UnavailableProductManagerTest extends KernelTestCase
{
    use PhpUnitPommHelperTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'datawarehouse/unavailable_product.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();

        $updateArticles = <<<'SQL'
            UPDATE backOffice.article SET V_quantite_stock = 0 WHERE id_produit IN(81078, 81123, 128416);
        SQL;
        $this->getLegacyPdo()->fetchAffected($updateArticles);

        PgDatabase::reloadFixtures();
    }

    /** @throws \Exception */
    public function test_extract_today(): void
    {
        $this->getManager()->extractToday();

        $today = new \DateTime();

        $this->assertEquals(
            [
                [
                    'date' => $today->format('Y-m-d'),
                    'customer_order_line_id' => '11',
                    'quantity' => '1000',
                ],
                [
                    'date' => $today->format('Y-m-d'),
                    'customer_order_line_id' => '13',
                    'quantity' => '3000',
                ],
            ],
            $this->getPgResult()
        );
    }

    private function getPgConnexion(): Connection
    {
        return self::$container
            ->get(CustomerOrderModel::class)
            ->getSession()
            ->getConnection();
    }

    private function getManager(): UnavailableProductManager
    {
        return self::$container->get(UnavailableProductManager::class);
    }

    private function getLegacyPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** @return array<int, mixed[]> */
    private function getPgResult(): array
    {
        $sql = <<<'SQL'
        SELECT * FROM {table}
        SQL;

        $sql = strtr($sql, [
            '{table}' => (new UnavailableProduct())->getRelation(),
        ]);

        $stmt = $this->getPgConnexion()->executeAnonymousQuery($sql);

        $count = $stmt->countRows();
        $result = [];
        for ($i = 0; $i < $count; ++$i) {
            $result[] = $stmt->fetchRow($i);
        }

        return $result;
    }
}
