<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\MysqlErp\Repository\Entity\SynchronizableTopic;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicReadRepository;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicWriteRepository;
use SonVideo\Erp\DataWarehouse\Collection\TopicSynchronizerCollection;
use SonVideo\Erp\DataWarehouse\Contract\SynchronizableTopicFeedbackHandlerInterface;
use SonVideo\Erp\DataWarehouse\Contract\TopicSynchronizerInterface;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicDequeueParameters;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicFeedback;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopicSpoolerDequeuer;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SynchronizableTopicSpoolerDequeuerTest extends KernelTestCase
{
    private SynchronizableTopicReadRepository $read_repository_mock;
    private SynchronizableTopicWriteRepository $write_repository_mock;
    private TopicSynchronizerCollection $topic_synchronizer_collection_mock;
    private SynchronizableTopicSpoolerDequeuer $dequeuer;
    private SynchronizableTopicFeedbackHandlerInterface $feedback_handler_mock;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->read_repository_mock = $this->createMock(SynchronizableTopicReadRepository::class);
        $this->write_repository_mock = $this->createMock(SynchronizableTopicWriteRepository::class);
        $this->topic_synchronizer_collection_mock = $this->createMock(TopicSynchronizerCollection::class);

        $this->dequeuer = new SynchronizableTopicSpoolerDequeuer(
            $this->write_repository_mock,
            $this->read_repository_mock,
            $this->topic_synchronizer_collection_mock
        );
        $this->dequeuer->setSerializer(self::$container->get(SerializerInterface::class));

        $this->feedback_handler_mock = $this->createMock(SynchronizableTopicFeedbackHandlerInterface::class);
    }

    /** Test that the dequeue method works correctly with no topics to process. */
    public function test_dequeue_with_no_topics(): void
    {
        $this->read_repository_mock
            ->expects($this->once())
            ->method('findHowManyRowsAreLocked')
            ->willReturn(0);

        $this->write_repository_mock
            ->expects($this->once())
            ->method('lockForSynchronization')
            ->with(
                0,
                $this->callback(function ($parameters) {
                    return $parameters instanceof SynchronizableTopicDequeueParameters &&
                        10000 === $parameters->limit &&
                        null === $parameters->synchronizable_topic_id;
                })
            );

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllTopicsTopicForFeedback')
            ->willReturn([]);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllLockedToSynchronize')
            ->willReturn($this->createGenerator([]));

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('start')
            ->with([]);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('progress')
            ->with(null);

        $this->feedback_handler_mock->expects($this->once())->method('end');

        $this->dequeuer->dequeue($this->feedback_handler_mock);
    }

    /** Test that the dequeue method works correctly with topics to process. */
    public function test_dequeue_with_topics(): void
    {
        $topics = [];

        $topics[] = [
            'synchronizable_topic_id' => 1,
            'topic' => SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT,
            'content' => ['customer_order_id' => 123456],
        ];

        $topics[] = [
            'synchronizable_topic_id' => 2,
            'topic' => SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT,
            'content' => ['customer_order_id' => 123458],
        ];

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findHowManyRowsAreLocked')
            ->willReturn(0);

        $this->write_repository_mock
            ->expects($this->once())
            ->method('lockForSynchronization')
            ->with(
                0,
                $this->callback(function ($parameters) {
                    return $parameters instanceof SynchronizableTopicDequeueParameters &&
                        10000 === $parameters->limit &&
                        null === $parameters->synchronizable_topic_id;
                })
            );

        $feedback = new SynchronizableTopicFeedback(SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT, 2);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllTopicsTopicForFeedback')
            ->willReturn([$feedback]);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllLockedToSynchronize')
            ->willReturn($this->createGenerator($topics));

        $handler_mock = $this->createMock(TopicSynchronizerInterface::class);
        $handler_mock
            ->expects($this->exactly(2))
            ->method('synchronize')
            ->with(
                $this->callback(function ($topic) {
                    return $topic instanceof AbstractSynchronizableTopicContent &&
                        in_array($topic->synchronizable_topic_id, [1, 2]) &&
                        SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT === $topic->topic;
                })
            );

        $this->topic_synchronizer_collection_mock
            ->expects($this->exactly(2))
            ->method('getHandler')
            ->willReturn($handler_mock);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('start')
            ->with([$feedback]);

        $this->feedback_handler_mock
            ->expects($this->exactly(2))
            ->method('getByTopic')
            ->with(SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT)
            ->willReturn($feedback);

        $this->feedback_handler_mock->expects($this->exactly(3))->method('progress');

        $this->feedback_handler_mock->expects($this->once())->method('end');

        $this->write_repository_mock
            ->expects($this->exactly(2))
            ->method('removeSynchronizedTopic')
            ->with(
                $this->callback(function ($id) {
                    return in_array($id, [1, 2]);
                })
            );

        $this->dequeuer->dequeue($this->feedback_handler_mock);
    }

    /** Test that the dequeue method works correctly with a specific topic ID. */
    public function test_dequeue_with_specific_topic_id(): void
    {
        $topics = [
            [
                'synchronizable_topic_id' => 5,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT,
                'content' => ['customer_order_id' => 123456],
            ],
        ];

        $parameters = new SynchronizableTopicDequeueParameters(10000, 5);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findHowManyRowsAreLocked')
            ->willReturn(0);

        $this->write_repository_mock
            ->expects($this->once())
            ->method('lockForSynchronization')
            ->with(
                0,
                $this->callback(function ($param) {
                    return $param instanceof SynchronizableTopicDequeueParameters &&
                        10000 === $param->limit &&
                        5 === $param->synchronizable_topic_id;
                })
            );

        $feedback = new SynchronizableTopicFeedback(SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT, 1);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllTopicsTopicForFeedback')
            ->willReturn([$feedback]);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllLockedToSynchronize')
            ->willReturn($this->createGenerator($topics));

        $handler_mock = $this->createMock(TopicSynchronizerInterface::class);
        $handler_mock
            ->expects($this->once())
            ->method('synchronize')
            ->with(
                $this->callback(function ($topic) {
                    return $topic instanceof AbstractSynchronizableTopicContent &&
                        5 === $topic->synchronizable_topic_id &&
                        SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT === $topic->topic;
                })
            );

        $this->topic_synchronizer_collection_mock
            ->expects($this->once())
            ->method('getHandler')
            ->willReturn($handler_mock);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('start')
            ->with([$feedback]);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('getByTopic')
            ->with(SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT)
            ->willReturn($feedback);

        $this->feedback_handler_mock->expects($this->exactly(2))->method('progress');

        $this->feedback_handler_mock->expects($this->once())->method('end');

        $this->write_repository_mock
            ->expects($this->once())
            ->method('removeSynchronizedTopic')
            ->with(5);

        $this->dequeuer->dequeue($this->feedback_handler_mock, $parameters);
    }

    /** Helper method to create a generator from an array. */
    private function createGenerator(array $items): \Generator
    {
        $serializer = self::$container->get(SerializerInterface::class);

        foreach ($items as $item) {
            yield $serializer->denormalize($item, SynchronizableTopic::class);
        }
    }
}
