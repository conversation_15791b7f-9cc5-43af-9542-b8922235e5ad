<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SupplierContractRepository;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SupplierContractUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\SupplierContractUpsertTopicSynchronizer;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SupplierContractUpsertTopicSynchronizerTest extends KernelTestCase
{
    private SupplierContractUpsertTopicSynchronizer $synchronizer;
    private SupplierContractRepository $supplier_contract_repository;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->supplier_contract_repository = $this->createMock(SupplierContractRepository::class);
        $this->serializer = self::$container->get(SerializerInterface::class);

        $this->synchronizer = new SupplierContractUpsertTopicSynchronizer($this->supplier_contract_repository);
        $this->synchronizer->setSerializer($this->serializer);
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle(SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsert avec les données appropriées */
    public function test_synchronize_calls_upsert_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
                'supplier_contract_id' => 456,
                'supplier_id' => 789,
                'brand_id' => 101,
                'year' => 2023,
                'discount_description' => ['description' => 'Test discount'],
                'pam' => ['pam_data' => 'test'],
                'rfa' => ['rfa_data' => 'test'],
                'additional_rewards' => ['reward_data' => 'test'],
                'unconditional_discount' => 5.5,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call with SupplierContract instance
        $this->supplier_contract_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
                'supplier_contract_id' => 456,
                'supplier_id' => 789,
                'brand_id' => 101,
                'year' => 2023,
                'discount_description' => ['description' => 'Test discount'],
                'pam' => ['pam_data' => 'test'],
                'rfa' => ['rfa_data' => 'test'],
                'additional_rewards' => ['reward_data' => 'test'],
                'unconditional_discount' => 5.5,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to throw exception
        $this->supplier_contract_repository->method('upsert')->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les valeurs nullables */
    public function test_synchronize_handles_nullable_values(): void
    {
        // Create a real SynchronizableTopic instance with nullable values
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
                'supplier_contract_id' => 456,
                'supplier_id' => 789,
                'brand_id' => null, // nullable
                'year' => 2023,
                'discount_description' => null, // nullable
                'pam' => [],
                'rfa' => [],
                'additional_rewards' => [],
                'unconditional_discount' => 0.0,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call with SupplierContract instance
        $this->supplier_contract_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les tableaux complexes */
    public function test_synchronize_handles_complex_arrays(): void
    {
        // Create a real SynchronizableTopic instance with complex array data
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
                'supplier_contract_id' => 456,
                'supplier_id' => 789,
                'brand_id' => 101,
                'year' => 2023,
                'discount_description' => [
                    'type' => 'percentage',
                    'value' => 10,
                    'conditions' => ['minimum_order' => 1000]
                ],
                'pam' => [
                    ['type' => 'advertising', 'amount' => 500],
                    ['type' => 'promotion', 'amount' => 300]
                ],
                'rfa' => [
                    ['percentage' => 2.5, 'threshold' => 10000],
                    ['percentage' => 5.0, 'threshold' => 50000]
                ],
                'additional_rewards' => [
                    ['type' => 'volume_bonus', 'rate' => 1.5],
                    ['type' => 'loyalty_bonus', 'rate' => 0.5]
                ],
                'unconditional_discount' => 3.75,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call
        $this->supplier_contract_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les valeurs limites */
    public function test_synchronize_handles_boundary_values(): void
    {
        // Create a real SynchronizableTopic instance with boundary values
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 999999,
                'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
                'supplier_contract_id' => 1,
                'supplier_id' => 1,
                'brand_id' => 1,
                'year' => 2024,
                'discount_description' => [],
                'pam' => [],
                'rfa' => [],
                'additional_rewards' => [],
                'unconditional_discount' => 100.0, // Maximum percentage
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call
        $this->supplier_contract_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
