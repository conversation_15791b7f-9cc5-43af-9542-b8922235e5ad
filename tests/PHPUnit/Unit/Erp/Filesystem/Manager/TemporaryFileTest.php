<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Filesystem\Manager;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use SonVideo\Erp\Filesystem\Manager\TemporaryFile;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class TemporaryFileTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    protected function getTestedClass(): TemporaryFile
    {
        return self::$container->get(TemporaryFile::class);
    }

    /** Gets an uploaded file for testing. */
    protected function getUploadedFile(string $filename, string $mime_type): UploadedFile
    {
        $fixtures_dir = __DIR__ . '/../../../../../fixtures/files';
        $file_path = $fixtures_dir . '/temporary_file_upload.csv';

        // Create a test file if it doesn't exist
        if (!file_exists($file_path)) {
            file_put_contents($file_path, 'test,data,csv');
        }

        return new UploadedFile($file_path, $filename, $mime_type, null, true);
    }

    /**
     * Tests writing a temporary file.
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function test_write(): void
    {
        $uploaded_file = $this->getUploadedFile('temporary_file_upload.csv', 'text/csv');
        $target_path = $this->getTestedClass()->upload($uploaded_file);

        // Verify that the file was written successfully
        $this->assertStringContainsString(date('dmYH', strtotime('now')), $target_path);
        $this->assertStringContainsString('-temporary_file_upload.csv', $target_path);
    }
}
