<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Filesystem\Manager;

use SonVideo\Erp\Filesystem\Manager\CiloFtp;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CiloFtpTest extends KernelTestCase
{
    private const FIXTURES_DIR = __DIR__ . '/../../../../../fixtures';

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests retrieving order files to process. */
    public function test_retrieve_order_files_to_process(): void
    {
        $service = $this->getInstance();
        $this->reloadFilesFixtures();
        $list = $service->retrieveOrderFilesToProcess();

        // Verify that order files are retrieved correctly
        $this->assertCount(1, $list);
        $this->assertArrayHasKey('type', $list[0]);
        $this->assertArrayHasKey('path', $list[0]);
        $this->assertArrayHasKey('timestamp', $list[0]);
        $this->assertArrayHasKey('size', $list[0]);
        $this->assertArrayHasKey('dirname', $list[0]);
        $this->assertArrayHasKey('basename', $list[0]);
        $this->assertArrayHasKey('extension', $list[0]);
        $this->assertArrayHasKey('filename', $list[0]);
    }

    /**
     * Copy fixtures files into the Cilo FTP filesystem.
     *
     * @throws \Exception
     */
    public function reloadFilesFixtures(): void
    {
        $fixtures = [
            [
                'src' => '/files/cilo/orders/100000518.xml',
                'dest' => '/100000518.xml',
            ],
        ];
        $cilo_filesystem = self::$container->get('oneup_flysystem.mount_manager')->getFilesystem(CiloFtp::FILESYSTEM);
        foreach ($fixtures as $fixture) {
            $resource = fopen(self::FIXTURES_DIR . $fixture['src'], 'r');
            $cilo_filesystem->putStream(CiloFtp::ORDERS_DIR . $fixture['dest'], $resource);
            fclose($resource);
        }
    }

    /** Gets an instance of CiloFtp. */
    protected function getInstance(): CiloFtp
    {
        return new CiloFtp(self::$container->get('oneup_flysystem.mount_manager'));
    }
}
