<?php

namespace PHPUnit\Unit\Erp\Spooler\Manager\SpoolerEvent\CustomerOrder;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicReadRepository;
use SonVideo\Erp\Cms\Manager\CustomerOrderForCmsManager;
use SonVideo\Erp\Spooler\Entity\SpoolerEvent;
use SonVideo\Erp\Spooler\Manager\SpoolerEvent\CustomerOrder\CustomerOrderUpdateLastModifiedAtSpoolerEventHandler;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerReadRepository;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerWriteRepository;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderUpdateLastModifiedAtSpoolerEventHandlerTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets a test instance of CustomerOrderUpdateLastModifiedAtSpoolerEventHandler. */
    private function getTestedInstance(
        SpoolerReadRepository $spooler_read_repository,
        CustomerOrderForCmsManager $customer_order_for_cms_manager,
        SpoolerWriteRepository $spooler_write_repository,
        SynappsNotifier $synapps_notifier,
        SynchronizableTopicReadRepository $synchronizable_topic_repository
    ): CustomerOrderUpdateLastModifiedAtSpoolerEventHandler {
        $tested_class = new CustomerOrderUpdateLastModifiedAtSpoolerEventHandler(
            $spooler_read_repository,
            $customer_order_for_cms_manager,
            $spooler_write_repository,
            $synapps_notifier,
            $synchronizable_topic_repository
        );

        $tested_class->setLogger(self::$container->get('logger'));

        return $tested_class;
    }

    /** Tests the handle method when there is no event to dequeue. */
    public function test_handle_when_there_is_no_event_to_dequeue(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn([]);

        $customer_order_for_cms_manager = $this->createMock(CustomerOrderForCmsManager::class);
        $customer_order_for_cms_manager->expects($this->never())->method('touchAndPush');

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->never())->method('deleteEvent');

        $synapps_notifier = $this->createMock(SynappsNotifier::class);
        $synapps_notifier->expects($this->never())->method('notify');

        $synchronizable_topic_repository = $this->createMock(SynchronizableTopicReadRepository::class);
        $synchronizable_topic_repository->expects($this->never())->method('upsert');

        $this->getTestedInstance(
            $spooler_read_repository,
            $customer_order_for_cms_manager,
            $spooler_write_repository,
            $synapps_notifier,
            $synchronizable_topic_repository
        )->handle();
    }

    /** Tests the handle method when CMS RPC call fails. */
    public function test_handle_when_cms_rpc_call_fails(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn(
                self::$container->get(SerializerInterface::class)->denormalize(
                    [
                        [
                            'action' => '',
                            'target' => '44',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                    ],
                    SpoolerEvent::class . '[]'
                )
            );

        $customer_order_for_cms_manager = $this->createMock(CustomerOrderForCmsManager::class);
        $customer_order_for_cms_manager
            ->expects($this->once())
            ->method('touchAndPush')
            ->willThrowException(new \Exception("I'm not feeling it"));

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->never())->method('deleteEvent');

        $synapps_notifier = $this->createMock(SynappsNotifier::class);
        $synapps_notifier->expects($this->never())->method('notify');

        $synchronizable_topic_repository = $this->createMock(SynchronizableTopicReadRepository::class);
        $synchronizable_topic_repository->expects($this->never())->method('upsert');

        $this->getTestedInstance(
            $spooler_read_repository,
            $customer_order_for_cms_manager,
            $spooler_write_repository,
            $synapps_notifier,
            $synchronizable_topic_repository
        )->handle();
    }

    /** Tests the handle method when Synapps notification fails. */
    public function test_handle_when_synapps_notification_fails(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn(
                self::$container->get(SerializerInterface::class)->denormalize(
                    [
                        [
                            'action' => '',
                            'target' => '44',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                    ],
                    SpoolerEvent::class . '[]'
                )
            );

        $customer_order_for_cms_manager = $this->createMock(CustomerOrderForCmsManager::class);
        $customer_order_for_cms_manager->expects($this->once())->method('touchAndPush');

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->once())->method('deleteEvent');

        $synapps_notifier = $this->createMock(SynappsNotifier::class);
        $synapps_notifier
            ->expects($this->once())
            ->method('notify')
            ->willThrowException(new \Exception('Not available, but dequeue nonetheless'));

        $synchronizable_topic_repository = $this->createMock(SynchronizableTopicReadRepository::class);
        $synchronizable_topic_repository->expects($this->once())->method('upsert');

        $this->getTestedInstance(
            $spooler_read_repository,
            $customer_order_for_cms_manager,
            $spooler_write_repository,
            $synapps_notifier,
            $synchronizable_topic_repository
        )->handle();
    }

    /** Tests the handle method when all processes end successfully. */
    public function test_handle_when_all_processes_ends_successfully(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn(
                self::$container->get(SerializerInterface::class)->denormalize(
                    [
                        [
                            'action' => '',
                            'target' => '44',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                        [
                            'action' => '',
                            'target' => '666',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                    ],
                    SpoolerEvent::class . '[]'
                )
            );

        $customer_order_for_cms_manager = $this->createMock(CustomerOrderForCmsManager::class);
        $customer_order_for_cms_manager->expects($this->exactly(2))->method('touchAndPush');

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->exactly(2))->method('deleteEvent');

        $synapps_notifier = $this->createMock(SynappsNotifier::class);
        $synapps_notifier->expects($this->exactly(2))->method('notify');

        $synchronizable_topic_repository = $this->createMock(SynchronizableTopicReadRepository::class);
        $synchronizable_topic_repository->expects($this->exactly(2))->method('upsert');

        $this->getTestedInstance(
            $spooler_read_repository,
            $customer_order_for_cms_manager,
            $spooler_write_repository,
            $synapps_notifier,
            $synchronizable_topic_repository
        )->handle();
    }

    /** Tests the handle method when data warehouse synchronization fails. */
    public function test_handle_when_data_warehouse_synchronization_fails(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn(
                self::$container->get(SerializerInterface::class)->denormalize(
                    [
                        [
                            'action' => '',
                            'target' => '44',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                    ],
                    SpoolerEvent::class . '[]'
                )
            );

        $customer_order_for_cms_manager = $this->createMock(CustomerOrderForCmsManager::class);
        $customer_order_for_cms_manager->expects($this->once())->method('touchAndPush');

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->once())->method('deleteEvent');

        $synapps_notifier = $this->createMock(SynappsNotifier::class);
        $synapps_notifier->expects($this->once())->method('notify');

        $synchronizable_topic_repository = $this->createMock(SynchronizableTopicReadRepository::class);
        $synchronizable_topic_repository
            ->expects($this->once())
            ->method('upsert')
            ->willThrowException(new \Exception('Data warehouse synchronization failed, but dequeue nonetheless'));

        $this->getTestedInstance(
            $spooler_read_repository,
            $customer_order_for_cms_manager,
            $spooler_write_repository,
            $synapps_notifier,
            $synchronizable_topic_repository
        )->handle();
    }
}
