<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Client\GraphQLClient;
use App\Exception\NotFoundException;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\File\FilesystemHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use SonVideo\Erp\Article\Manager\ArticleMediaCloner;
use SonVideo\Erp\Article\Manager\ArticleMediaVariations;
use SonVideo\Erp\Article\Manager\Filesystem\ArticleMediaFile;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleMediaClonerTest extends KernelTestCase
{
    private const ARTICLE_ID_SOURCE = 81078;
    private const ARTICLE_ID_TARGET = 81123;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['sales_channel/sales_channels.sql', 'article/article_media.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();

        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'image_1200.jpg', 'image_1200.jpeg');
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'super.pdf', 'test.pdf');
    }

    /** Creates a test instance of ArticleMediaCloner. */
    protected function getTestedInstance(MockHandler $mock, array &$container): ArticleMediaCloner
    {
        $history = Middleware::history($container);
        $handler_stack = HandlerStack::create($mock);
        $handler_stack->push($history);

        return new ArticleMediaCloner(
            self::$container->get(ArticleMediaVariations::class),
            self::$container->get(ArticleMediaFile::class),
            self::$container->get(SingleArticleReadRepository::class),
            new GraphQLClient('', '', self::$container->get('logger'), new Client(['handler' => $handler_stack]))
        );
    }

    /** Tests cloning everything. */
    public function test_clone_everything(): void
    {
        // Should clone nothing
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getEmptyQueryResponse()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        $this->getTestedInstance($mock, $container)->clone(static::ARTICLE_ID_SOURCE, static::ARTICLE_ID_TARGET);
        $this->assertCount(1, $container);

        // Should clone everything in a published article
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getDataFromPublishedArticleSku()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        $this->getTestedInstance($mock, $container)->clone(static::ARTICLE_ID_SOURCE, static::ARTICLE_ID_TARGET);
        $this->assertCount(2, $container);

        $gql_request = self::extractVariablesFrom($container[1]['request']);
        // Insert in a published article
        $this->assertStringContainsString('insert_cms_article_article_media_i18n', $gql_request['query']);

        $gql_insert_request_variables = $gql_request['variables'];
        // Both media are inserted
        $this->assertCount(2, $gql_insert_request_variables['data']);
        // Each type uses their own display_order's basis
        $this->assertEquals(8, $gql_insert_request_variables['data'][0]['display_order']);
        $this->assertEquals(3, $gql_insert_request_variables['data'][1]['display_order']);

        // Should clone everything in a draft article
        $container = [];

        $query_response = str_replace(
            ['{ "article_id": 666 }', '"target_draft": []'],
            ['', '"target_draft": [{ "article_id": 666 }]'],
            self::getDataFromDraftArticleSku()
        );

        $mock = new MockHandler([
            new Response(200, [], $query_response),
            new Response(200, [], self::getMutationResponse()),
        ]);

        $this->getTestedInstance($mock, $container)->clone(static::ARTICLE_ID_SOURCE, static::ARTICLE_ID_TARGET);
        $this->assertCount(2, $container);

        $gql_request = self::extractVariablesFrom($container[1]['request']);
        // Insert in a published article
        $this->assertStringContainsString('insert_cms_draft_article_article_media_i18n', $gql_request['query']);

        $gql_insert_request_variables = $gql_request['variables'];
        // Both media are inserted
        $this->assertCount(2, $gql_insert_request_variables['data']);
        // Each type uses their own display_order's basis
        $this->assertEquals(1, $gql_insert_request_variables['data'][0]['display_order']);
        $this->assertEquals(2, $gql_insert_request_variables['data'][1]['display_order']);
    }

    /** Tests cloning images only. */
    public function test_clone_images_only(): void
    {
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getDataFromPublishedArticleSku()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        $this->getTestedInstance($mock, $container)->clone(
            static::ARTICLE_ID_SOURCE,
            static::ARTICLE_ID_TARGET,
            ArticleMediaCloner::CLONE_IMAGE,
            !ArticleMediaCloner::CLONE_DOCUMENT // Skip documents
        );
        $this->assertCount(2, $container);

        $gql_request = self::extractVariablesFrom($container[1]['request']);
        // Insert in a published article
        $this->assertStringContainsString('insert_cms_article_article_media_i18n', $gql_request['query']);

        $gql_insert_request_variables = $gql_request['variables'];
        // Only the image should be inserted
        $this->assertCount(1, $gql_insert_request_variables['data']);
        // Each type uses their own display_order's basis
        $this->assertEquals(8, $gql_insert_request_variables['data'][0]['display_order']);
    }

    /** Tests cloning documents only. */
    public function test_clone_document_only(): void
    {
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getDataFromPublishedArticleSku()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        $this->getTestedInstance($mock, $container)->clone(
            static::ARTICLE_ID_SOURCE,
            static::ARTICLE_ID_TARGET,
            !ArticleMediaCloner::CLONE_IMAGE, // Skip images
            ArticleMediaCloner::CLONE_DOCUMENT
        );
        $this->assertCount(2, $container);

        $gql_request = self::extractVariablesFrom($container[1]['request']);
        // Insert in a published article
        $this->assertStringContainsString('insert_cms_article_article_media_i18n', $gql_request['query']);

        $gql_insert_request_variables = $gql_request['variables'];
        // Only the document should be inserted
        $this->assertCount(1, $gql_insert_request_variables['data']);
        // Each type uses their own display_order's basis
        $this->assertEquals(3, $gql_insert_request_variables['data'][0]['display_order']);
    }

    /** Tests cloning with filtered media IDs. */
    public function test_clone_with_filtered_media_ids(): void
    {
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getDataFromPublishedArticleSku()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        $this->getTestedInstance($mock, $container)->clone(
            static::ARTICLE_ID_SOURCE,
            static::ARTICLE_ID_TARGET,
            // Skip both (irrelevant here)
            !ArticleMediaCloner::CLONE_IMAGE,
            !ArticleMediaCloner::CLONE_DOCUMENT,
            // Those should be in the where queries
            ['a14523f3-bdaf-48a7-ba03-7bff0ee41be5', 'b14523f3-bdaf-48a7-ba03-7bff0ee41be5']
        );
        $this->assertCount(1, $container);

        $gql_request = self::extractVariablesFrom($container[0]['request']);
        $gql_insert_request_variables = $gql_request['variables'];

        $this->assertCount(2, $gql_insert_request_variables['where']['media_id']['_in']);
        $this->assertCount(2, $gql_insert_request_variables['where_draft']['media_id']['_in']);
    }

    /** Tests clone failures. */
    public function test_clone_fails(): void
    {
        $source_article_id = static::ARTICLE_ID_SOURCE;
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getEmptyQueryResponse()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        // When source article does not exist
        $article_media_cloner = $this->getTestedInstance($mock, $container);

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessageMatches('/Article not found with id or sku "666"./');
        $article_media_cloner->clone(666, 999);
    }

    /** Tests clone failure when target article does not exist. */
    public function test_clone_fails_when_target_article_does_not_exist(): void
    {
        $source_article_id = static::ARTICLE_ID_SOURCE;
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getEmptyQueryResponse()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        // When target article does not exist
        $article_media_cloner = $this->getTestedInstance($mock, $container);

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessageMatches('/Article not found with id or sku "999"./');
        $article_media_cloner->clone($source_article_id, 999);
    }

    /** Tests clone failure when target and source articles are the same. */
    public function test_clone_fails_when_target_and_source_are_the_same(): void
    {
        $source_article_id = static::ARTICLE_ID_SOURCE;
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], self::getEmptyQueryResponse()),
            new Response(200, [], self::getMutationResponse()),
        ]);

        // When target article and source article are the same
        $article_media_cloner = $this->getTestedInstance($mock, $container);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessageMatches('/The source article and the target article cannot be identical/');
        $article_media_cloner->clone($source_article_id, 'ARCAMRBLINKNR');
    }

    /** Tests clone failure when no target article was found. */
    public function test_clone_fails_when_no_target_article_was_found(): void
    {
        $source_article_id = static::ARTICLE_ID_SOURCE;
        $target_article_id = static::ARTICLE_ID_TARGET;
        $container = [];
        $mock = new MockHandler([
            new Response(200, [], str_replace('{ "article_id": 666 }', '', self::getEmptyQueryResponse())),
            new Response(200, [], self::getMutationResponse()),
        ]);

        // When no target article was found
        $article_media_cloner = $this->getTestedInstance($mock, $container);

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessageMatches(
            '/Could not found either a published nor a draft target article with sku "LBCLD25BP"/'
        );
        $article_media_cloner->clone($source_article_id, $target_article_id);
    }

    /** Extracts variables from a request. */
    private static function extractVariablesFrom(Request $request)
    {
        return json_decode($request->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /** Gets a mutation response. */
    private static function getMutationResponse(): string
    {
        return json_encode(['data' => ['success' => 'true']], JSON_THROW_ON_ERROR);
    }

    /** Gets an empty query response. */
    private static function getEmptyQueryResponse(): string
    {
        return <<<json
        {
          "data": {
            "source_published": [],
            "source_draft": [],
            "target_published": [
              { "article_id": 666 }
            ],
            "target_published_image": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_published_document": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_draft": [],
            "target_draft_image": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_draft_document": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            }
          }
        }
        json;
    }

    /** Gets data from a published article SKU. */
    private static function getDataFromPublishedArticleSku(): string
    {
        return <<<json
        {
          "data": {
            "source_published": [
              {
                "media_id": "a64523f3-bdaf-48a7-ba03-7bff0ee41be6",
                "meta": {
                  "view": "Vue principale"
                },
                "type": "IMAGE",
                "supported_culture_id": "fr",
                "display_order": 1,
                "media": {
                  "media_variation": {
                    "image": {
                      "largest": 1200,
                      "referential": {
                        "95":  "/image_95.jpg",
                        "140": "/image_140.jpg",
                        "180": "/image_180.jpg",
                        "600": "/image_600.jpg",
                        "900": "/image_900.jpg",
                        "1200": "/image_1200.jpg",
                        "300_square": "/image_300_square.jpg",
                        "450_square": "/image_450_square.jpg"
                      }
                    }
                  }
                }
              },
              {
                "media_id": "cce65e83-6950-4a77-9fe6-511927d7576e",
                "meta": {
                  "view": " Guide de démarrage"
                },
                "type": "DOCUMENT",
                "supported_culture_id": "fr",
                "display_order": 1,
                "media": {
                  "media_variation": {
                    "document": {
                      "url": "/super.pdf",
                      "mime-type": "application/pdf"
                    }
                  }
                }
              }
            ],
            "source_draft": [],
            "target_published": [
              { "article_id": 666 }
            ],
            "target_published_image": {
              "aggregate": {
                "max": {
                  "display_order": 7
                }
              }
            },
            "target_published_document": {
              "aggregate": {
                "max": {
                  "display_order": 2
                }
              }
            },
            "target_draft": [],
            "target_draft_image": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_draft_document": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            }
          }
        }
        json;
    }

    /** Gets data from a draft article SKU. */
    private static function getDataFromDraftArticleSku(): string
    {
        return <<<json
        {
          "data": {
            "source_published": [],
            "source_draft": [
              {
                "media_id": "a14523f3-bdaf-48a7-ba03-7bff0ee41be5",
                "meta": {
                  "view": "Vue principale"
                },
                "type": "IMAGE",
                "supported_culture_id": "fr",
                "display_order": 1,
                "media": {
                  "media_variation": {
                    "image": {
                      "largest": 1200,
                      "referential": {
                        "95":  "/image_95.jpg",
                        "140": "/image_140.jpg",
                        "180": "/image_180.jpg",
                        "600": "/image_600.jpg",
                        "900": "/image_900.jpg",
                        "1200": "/image_1200.jpg",
                        "300_square": "/image_300_square.jpg",
                        "450_square": "/image_450_square.jpg"
                      }
                    }
                  }
                }
              },
              {
                "media_id": "cce65e83-6950-4a77-9fe6-511927d7576e",
                "meta": {
                  "view": " Guide de démarrage"
                },
                "type": "DOCUMENT",
                "supported_culture_id": "fr",
                "display_order": 2,
                "media": {
                  "media_variation": {
                    "document": {
                      "url": "/super.pdf",
                      "mime-type": "application/pdf"
                    }
                  }
                }
              }
            ],
            "target_published": [
              { "article_id": 666 }
            ],
            "target_published_image": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_published_document": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_draft": [],
            "target_draft_image": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            },
            "target_draft_document": {
              "aggregate": {
                "max": {
                  "display_order": null
                }
              }
            }
          }
        }
        json;
    }
}
