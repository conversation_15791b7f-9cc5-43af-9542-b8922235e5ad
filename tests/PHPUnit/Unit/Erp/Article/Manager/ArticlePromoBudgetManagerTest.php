<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Article\Dto\CreationContext\ArticlePromoBudgetCreationContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticlePromoBudgetUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticlePromoBudgetManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticlePromoBudgetManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'article/get_article_by_id_or_sku_v2.sql',
            'article/article_promo_budget.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticlePromoBudgetManager. */
    protected function getTestedInstance(): ArticlePromoBudgetManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return self::$container->get(ArticlePromoBudgetManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the create method. */
    public function test_create(): void
    {
        $dto = $this->getSerializer()->denormalize(
            [
                'article_id' => 72215,
                'amount' => 10,
                'start_at' => '2020-08-01 10:00:00',
                'end_at' => '2020-08-05 10:00:00',
            ],
            ArticlePromoBudgetCreationContextDto::class
        );

        // Valid articles promo budget system_events
        $manager = $this->getTestedInstance();
        $promo_id = $manager->create($dto);

        $this->assertEquals(5, $promo_id);

        $system_event = $this->fetchLastSystemEventsForArticle('72215');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.create.promo_budget', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 72215}, "data": {"amount": 10, "end_at": "2020-08-05 10:00:00", "start_at": "2020-08-01 10:00:00"}, "meta": {"created_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $dto = $this->getSerializer()->denormalize(
            [
                'id' => 3,
                'article_id' => 72216,
                'amount' => 13,
                'start_at' => '2024-08-01 12:00:00',
                'end_at' => '2024-08-05 10:00:00',
            ],
            ArticlePromoBudgetUpdateContextDto::class
        );

        // Valid articles promo budget system_events
        $manager = $this->getTestedInstance();
        $manager->update($dto);

        $system_event = $this->fetchLastSystemEventsForArticle('72216');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.promo_budget', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 72216}, "data": {"amount": {"new": 13, "old": 100}, "end_at": {"new": "2024-08-05 10:00:00", "old": "2024-08-08 12:00:00"}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Tests the delete method. */
    public function test_delete(): void
    {
        $dto = $this->getSerializer()->denormalize(
            [
                'id' => 4,
                'article_id' => 72219,
                'amount' => 13,
                'start_at' => '2024-08-01 12:00:00',
                'end_at' => '2024-08-05 10:00:00',
            ],
            ArticlePromoBudgetUpdateContextDto::class
        );

        // Valid articles promo budget system_events
        $manager = $this->getTestedInstance();
        $manager->delete(72219, 4);

        $system_event = $this->fetchLastSystemEventsForArticle('72219');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.delete.promo_budget', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 72219}, "data": {"amount": 100, "end_at": "2024-08-08 12:00:00", "start_at": "2024-08-01 12:00:00"}, "meta": {"deleted_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Fetches the last system events for a given article ID. */
    private function fetchLastSystemEventsForArticle(string $article_id): array
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.system_event
        WHERE JSON_CONTAINS(payload, :article_id, '$._rel.article');
        LIMIT 1
        MYSQL;

        return $this->getPdo()->fetchOne($sql, ['article_id' => $article_id]);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
