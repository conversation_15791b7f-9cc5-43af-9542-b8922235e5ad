<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Article\Dto\UpdateContext\PackagedArticleContextDto;
use SonVideo\Erp\Article\Manager\PackagedArticleManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PackagedArticleManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/packaged_articles.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of PackagedArticleManager. */
    protected function getTestedInstance(): PackagedArticleManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return self::$container->get(PackagedArticleManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $dto = $this->getSerializer()->denormalize(
            ['packaged_article_id' => 107583, 'package_id' => 78404, 'quantity' => 3],
            PackagedArticleContextDto::class
        );

        $packaged_article_manager = $this->getTestedInstance();
        $updated = $packaged_article_manager->update($dto);

        $this->assertEquals(1, $updated);

        $system_event = $this->fetchLastSystemEventsForArticle('78404');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.packaged_article', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 78404, "packaged_article": 78226}, "data": {"packaged_article_quantity": {"new": 3, "old": 100}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Tests the delete method. */
    public function test_delete(): void
    {
        $packaged_article_manager = $this->getTestedInstance();
        $deleted = $packaged_article_manager->delete(117993, 120087);

        $this->assertEquals(1, $deleted);

        $system_event = $this->fetchLastSystemEventsForArticle('120087');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.package', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 120087}, "data": {"packaged_article": {"added": [], "deleted": [{"quantity": 1, "article_id": 118230}]}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Fetches the last system events for a given article ID. */
    private function fetchLastSystemEventsForArticle(string $article_id): array
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.system_event
        WHERE JSON_CONTAINS(payload, :article_id, '$._rel.article');
        LIMIT 1
        MYSQL;

        return $this->getPdo()->fetchOne($sql, ['article_id' => $article_id]);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
