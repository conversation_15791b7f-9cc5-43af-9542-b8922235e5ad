<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Export\Cilo\Manager;

use App\DataLoader\EntityDataLoader;
use SonVideo\Erp\Entity\Marketplace\Cilo\CiloStockPriceEntity;
use SonVideo\Erp\Export\Cilo\Manager\StockPrice;
use SonVideo\Erp\Repository\Marketplace\Cilo\CiloMarketplaceReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class StockPriceTest extends KernelTestCase
{
    public const MOCKED_SQL_RESULTS = [
        [
            'sku' => 'AAATCRESCENDOINR',
            'prix_cilo_ht' => 0,
            'prix_svd_ttc' => '599.00',
            'quantite' => '6',
        ],
        [
            'sku' => 'AAATCRESCENDOISR',
            'prix_cilo_ht' => 0,
            'prix_svd_ttc' => '599.00',
            'quantite' => '4',
        ],
    ];

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the CiloMarketplaceReadRepository mock. */
    private function getCiloMarketplaceReadRepository(): CiloMarketplaceReadRepository
    {
        $data_loader = self::$container->get(EntityDataLoader::class);

        $repository_mocked = $this->createMock(CiloMarketplaceReadRepository::class);
        $mocked_result = static::MOCKED_SQL_RESULTS;

        $repository_mocked
            ->method('fetchAllForStockPrice')
            ->willReturnCallback(function () use ($mocked_result, $data_loader): array {
                $stock_price = [];

                foreach ($mocked_result as $product) {
                    $stock_price[] = $data_loader->hydrate($product, CiloStockPriceEntity::class);
                }

                return $stock_price;
            });

        return $repository_mocked;
    }

    /** Gets the tested class. */
    protected function getTestedClass(): StockPrice
    {
        return new StockPrice($this->getCiloMarketplaceReadRepository());
    }

    /** Tests the exportAsXml method. */
    public function test_export_as_xml(): void
    {
        // Check generated XML string
        $result = $this->getTestedClass()
            ->load()
            ->exportAsXml();

        $this->assertStringContainsString('<?xml version="1.0" encoding="ISO-8859-1"?>', $result);
        $this->assertStringContainsString(
            '<offer_reference type="SellerSku">AAATCRESCENDOINR</offer_reference>',
            $result
        );
        $this->assertStringContainsString(
            '<offer_reference type="SellerSku">AAATCRESCENDOISR</offer_reference>',
            $result
        );
    }
}
