<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\Quote;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\DataLoader\EntityDataLoader;
use App\Entity\OptionalColumnsLoader;
use App\Sql\Query\QueryBuilder;
use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use League\Flysystem\FileExistsException;
use League\Flysystem\MountManager;
use SonVideo\Erp\Document\Manager\HtmlToPdfGenerator;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\EmailSystemEventFactory;
use SonVideo\Erp\Mailing\Manager\Quote\QuoteEmailDispatcher;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Manager\Decorator\QuoteDecorator;
use SonVideo\Erp\Quote\Manager\Document\QuotePdfDocument;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use SonVideo\Synapps\Client\RpcClientService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteEmailDispatcherTest extends KernelTestCase
{
    private const FIXTURE_FILESYSTEM = 'mock_filesystem';
    private const MOCKED_QUOTE_PATH = 'sonvideopro.com/data/backoffice/quote/SONVIDEO_quotation_1234.pdf';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): QuoteEmailDispatcher
    {
        // Create a mock for the PDF generator
        $pdf_generator = $this->createMock(HtmlToPdfGenerator::class);
        $pdf_generator->method('generate')->willReturn('ok');

        $generator = new QuotePdfDocument(
            $pdf_generator,
            self::$container->get(QueryBuilder::class),
            self::$container->get(QuoteRepository::class),
            self::$container->get(OptionalColumnsLoader::class),
            self::$container->get(QuoteDecorator::class),
            self::$container->get(MountManager::class)
        );
        $generator->setDataLoader(self::$container->get(EntityDataLoader::class));

        $class = new QuoteEmailDispatcher(self::$container->get(EmailSystemEventFactory::class), $generator);

        $class->setLogger(self::$container->get('logger'));
        $class->setRpcClient(self::$container->get(RpcClientService::class));
        $class->setParameterModel(self::$container->get(ParameterModel::class));

        return $class;
    }

    public function test_dispatch_with_invalid_quotation_payload(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/post_send_quote_email.sql',
        ]);

        $invalid_quote_datas = [
            'to' => 'bad email',
            'from' => [
                'email' => null,
            ],
            'context' => [
                'type' => QuoteEntity::TYPE_QUOTATION,
            ],
            '_rel' => [
                'customer' => null,
                'quote' => null,
            ],
        ];

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch($invalid_quote_datas);
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[from][email]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from][reply_to]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString('[cc]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][quote_id]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][message]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][billing_address]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][prices]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][created_at]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][created_at_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][expired_at]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][expired_at_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][created_by]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][created_by_name]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_count]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][shipment_method]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][shipping_address]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][quote_line_aggregates]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][link_to_pay_quote]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][quote]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][quote]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_invalid_offer_payload(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/post_send_quote_email.sql',
        ]);

        $invalid_quote_datas = [
            'to' => 'bad email',
            'from' => [
                'email' => null,
            ],
            'context' => [
                'type' => QuoteEntity::TYPE_OFFER,
            ],
            '_rel' => [
                'customer' => null,
                'quote' => null,
            ],
        ];

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch($invalid_quote_datas);
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[from][email]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from][reply_to]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString('[cc]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][quote_id]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][message]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][prices]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][created_at]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][created_at_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][expired_at]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][expired_at_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][created_by]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][created_by_name]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_count]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][quote_line_aggregates]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][link_to_pay_quote]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][customer]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][quote]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][quote]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/post_send_quote_email.sql',
        ]);

        $valid_quote_datas = [
            'to' => '<EMAIL>',
            'from' => [
                'name' => 'Son-Vidéo',
                'email' => '<EMAIL>',
                'reply_to' => '<EMAIL>',
            ],
            'cc' => '<EMAIL>',
            'context' => [
                'quote_id' => 12,
                'message' => 'Suite à votre demande voici<br/>notre offre pour les produits suivants&nbsp;:',
                'billing_address' => [
                    'firstname' => 'Bruce',
                    'lastname' => 'Wayne',
                ],
                'prices' => [
                    'total_discount_tax_included' => 1200.86,
                    'total_discount_tax_included_formatted' => '1 200,86&nbsp;&euro;',
                    'total_price_tax_included' => 98500.86,
                    'total_price_tax_included_formatted' => '9 8500,86&nbsp;&euro;',
                    'total_vat' => 200.0,
                    'total_vat_formatted' => '200,00&nbsp;&euro;',
                ],
                'created_at' => '2019-10-25 15:32:54',
                'created_at_formatted' => '25/10/2019 15:32',
                'sent_at' => '2019-11-24 15:32:54',
                'expired_at' => '2019-11-24 15:32:54',
                'expired_at_formatted' => '24/11/2019',
                'created_by' => 1300,
                'created_by_name' => 'Joe Le Rigolo',
                'articles_count' => 2,
                'type' => 'quotation',
                'status' => 'inactive',
                'locked' => false,
                'shipment_method' => [
                    'cost' => 2.0,
                    'cost_formatted' => '2,00&nbsp;&euro;',
                    'carrier_name' => 'GLS',
                ],
                'shipping_address' => [
                    'address' => '36 rue de la ville en bois',
                    'postal_code' => '44000',
                    'city' => 'Nantes',
                ],
                'quote_line_aggregates' => [
                    [
                        'type' => 'product',
                        'data' => [
                            'product' => [
                                'sku' => 'jesuisunsku',
                                'short_description' => 'KEF LS50 Meta Bleu Special Edition',
                                'description' => 'Enceintes bibliothèque',
                                'image' => 'https://dfxqtqxztmxwe.cloudfront.net/images/article/kef/KEFLS50METABLMT/ls50-meta-bleu-royal-mat-special-edition_5f6862c8104e5_300_square.jpg',
                            ],
                            'selling_price_tax_excluded' => 99999.48,
                            'selling_price_tax_included' => 99999.48,
                            'selling_price_tax_included_formatted' => '99 999,48&nbsp;&euro;',
                            'quantity' => 1,
                            'unit_discount_amount_abs_tax_included' => 10.0,
                            'unit_discount_amount_abs_tax_excluded' => 10.0,
                            'total_discount_amount' => 10.0,
                            'total_discount_amount_formatted' => '10,00&nbsp;&euro;',
                            'total_price_tax_excluded' => 99999,
                            'total_price_tax_included' => 99999,
                            'total_price' => 99999,
                            'total_price_formatted' => '99 999,00&nbsp;&euro;',
                            'url' => 'https://son-video.com',
                        ],
                    ],
                    [
                        'type' => 'product_warranty',
                        'data' => [
                            'type' => 'extension',
                            'label' => 'Extension de garantie 5 ans',
                            'unit_selling_price_tax_included' => 99,
                            'duration' => 5,
                            'quantity' => 1,
                            'price_tax_included_formatted' => '99,00 €',
                            'price_tax_excluded' => 82.5,
                            'price_tax_excluded_formatted' => '82,50 €',
                            'total_price_tax_included' => 99,
                            'total_price_tax_included_formatted' => '99,00 €',
                            'total_price_tax_excluded' => 82.5,
                            'total_price_tax_excluded_formatted' => '82,50 €',
                        ],
                    ],
                    [
                        'type' => 'product',
                        'data' => [
                            'product' => [
                                'sku' => 'jesuisunsku2',
                                'short_description' => 'KEF LS50 Meta Bleu Special Edition',
                                'description' => 'Enceintes bibliothèque',
                                'image' => 'https://dfxqtqxztmxwe.cloudfront.net/images/article/kef/KEFLS50METABLMT/ls50-meta-bleu-royal-mat-special-edition_5f6862c8104e5_300_square.jpg',
                            ],
                            'selling_price_tax_excluded' => 99999,
                            'selling_price_tax_included' => 99999,
                            'selling_price_tax_included_formatted' => '99 999,48&nbsp;&euro;',
                            'quantity' => 1,
                            'unit_discount_amount_abs_tax_included' => 10.0,
                            'unit_discount_amount_abs_tax_excluded' => 10.0,
                            'total_discount_amount' => 10.0,
                            'total_discount_amount_formatted' => '10,00&nbsp;&euro;',
                            'total_price_tax_excluded' => 99999,
                            'total_price_tax_included' => 99999,
                            'total_price' => 99999,
                            'total_price_formatted' => '99 999,00nbsp;&euro;',
                            'url' => 'https://son-video.com',
                        ],
                    ],
                ],
                'link_to_pay_quote' => 'https://son-video.com/mon-compte/payer-mon-devis/12?autologin=auieauieauieauie',
            ],
            '_rel' => [
                'customer' => 970481,
                'quote' => 12,
            ],
            '_sent_by' => 1300,
        ];

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch($valid_quote_datas);

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }

    /** @throws FileExistsException */
    public function putMockedInvoiceFileInLegacyFilesystem(): void
    {
        /** @var MountManager $mount_manager */
        $mount_manager = self::$container->get('oneup_flysystem.mount_manager');

        $mocked_invoice = sprintf(
            '%s://%s/SONVIDEO_quotation_1234.pdf',
            'legacy_filesystem',
            static::MOCKED_QUOTE_PATH
        );

        if (!$mount_manager->has($mocked_invoice)) {
            $mount_manager->copy(
                sprintf('%s://quote/SONVIDEO_quotation_1234.pdf', static::FIXTURE_FILESYSTEM),
                $mocked_invoice
            );
        }
    }
}
