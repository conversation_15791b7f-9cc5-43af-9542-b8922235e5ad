<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Carrier\Manager\Eligibility;

use App\Adapter\Serializer\SerializerInterface;
use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeDto;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibleShipmentMethodDto;
use SonVideo\Erp\Carrier\Exception\ShipmentMethod\EligibilityInvalidEnvelopeException;
use SonVideo\Erp\Carrier\Manager\Eligibility\ShipmentMethodResolver;
use SonVideo\Erp\Referential\Rpc\BridgeRpcMethodReferential;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ShipmentMethodResolverTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'carrier/post_carrier_eligible_shipment_methods.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ShipmentMethodResolver. */
    protected function getTestedInstance(): ShipmentMethodResolver
    {
        return self::$container->get(ShipmentMethodResolver::class);
    }

    /** Tests the resolve method. */
    public function test_resolve(): void
    {
        $instance = $this->getTestedInstance();
        $serializer = self::$container->get(SerializerInterface::class);

        $item = [
            'sku' => 'ARCAMRBLINKNR',
            'quantity' => 1,
            'price_vat_excluded' => 100,
            'price' => 120.6,
            'total_price' => 120.6,
        ];

        $shipping_address = [
            'title' => 'M.',
            'firstname' => 'Harry',
            'lastname' => 'Potter',
            'cellphone' => '0123456789',
            'city' => 'Nantes',
            'postal_code' => '44100',
            'address' => '3/4 station something something',
            'country_code' => 'FR',
        ];

        // Test: Throw an exception if the envelope is empty
        try {
            $instance->resolve($serializer->denormalize([], EligibilityEnvelopeDto::class));
            $this->fail('Expected exception was not thrown');
        } catch (EligibilityInvalidEnvelopeException $e) {
            $this->assertStringContainsString('items: This value should not be blank.', $e->getMessage());
            $this->assertStringContainsString('shipping_address: This value should not be blank.', $e->getMessage());
        }

        // Test: Validate each item
        try {
            $instance->resolve(
                $serializer->denormalize(
                    [
                        'items' => [
                            [
                                'sku' => '',
                                'quantity' => 0,
                                'price_vat_excluded' => 0,
                                'price' => 0,
                                'total_price' => 0,
                            ],
                        ],
                        'shipping_address' => $shipping_address,
                    ],
                    EligibilityEnvelopeDto::class
                )
            );
            $this->fail('Expected exception was not thrown');
        } catch (\Exception $e) {
            $this->assertStringContainsString('items[0].sku: This value should not be blank.', $e->getMessage());
            $this->assertStringContainsString(
                'items[0].quantity: This value should be greater than 0.',
                $e->getMessage()
            );
            $this->assertStringContainsString(
                'items[0].price_vat_excluded: This value should not be equal to "".',
                $e->getMessage()
            );
            $this->assertStringContainsString(
                'items[0].price: This value should not be equal to "".',
                $e->getMessage()
            );
            $this->assertStringContainsString(
                'items[0].total_price: This value should not be equal to "".',
                $e->getMessage()
            );
        }

        // Test: Validate the shipping address
        try {
            $instance->resolve(
                $serializer->denormalize(
                    [
                        'items' => [$item],
                        'shipping_address' => [
                            'title' => '',
                            'firstname' => '',
                            'lastname' => '',
                            'cellphone' => '',
                            'city' => '',
                            'postal_code' => '',
                            'address' => '',
                            'country_code' => '',
                        ],
                    ],
                    EligibilityEnvelopeDto::class
                )
            );
            $this->fail('Expected exception was not thrown');
        } catch (EligibilityInvalidEnvelopeException $e) {
            $this->assertStringContainsString(
                'shipping_address.title: This value should not be blank.',
                $e->getMessage()
            );
        }

        try {
            $instance->resolve(
                $serializer->denormalize(
                    [
                        'items' => [$item],
                        'shipping_address' => array_merge($shipping_address, [
                            'title' => 'TOTO',
                        ]),
                    ],
                    EligibilityEnvelopeDto::class
                )
            );
            $this->fail('Expected exception was not thrown');
        } catch (EligibilityInvalidEnvelopeException $e) {
            $this->assertStringContainsString(
                'shipping_address.title: The value you selected is not a valid choice.',
                $e->getMessage()
            );
        }

        try {
            $instance->resolve(
                $serializer->denormalize(
                    [
                        'items' => [$item],
                        'shipping_address' => array_merge($shipping_address, [
                            'country_code' => 'ZZ',
                        ]),
                    ],
                    EligibilityEnvelopeDto::class
                )
            );
            $this->fail('Expected exception was not thrown');
        } catch (EligibilityInvalidEnvelopeException $e) {
            $this->assertStringContainsString(
                'shipping_address.country_code: The country code "ZZ" is invalid.',
                $e->getMessage()
            );
        }

        // Test: Throw an exception if an article does not exist
        try {
            $instance->resolve(
                $serializer->denormalize(
                    [
                        'items' => [
                            array_merge($item, [
                                'sku' => 'APPLE',
                            ]),
                        ],
                        'shipping_address' => $shipping_address,
                    ],
                    EligibilityEnvelopeDto::class
                )
            );
            $this->fail('Expected exception was not thrown');
        } catch (EligibilityInvalidEnvelopeException $e) {
            $this->assertStringContainsString('Article with sku "APPLE" does not exists', $e->getMessage());
        }

        // Test: Check decorated successful response
        $expected_success_response = <<<JSON
        [{"shipment_method_id": 13, "cost": 50}]
        JSON;

        RpcClientServiceMock::savedResult(
            BridgeRpcMethodReferential::SERVER_NAME,
            BridgeRpcMethodReferential::CARRIER_ELIGIBILTY_ENDPOINT,
            $expected_success_response
        );

        // Test: Returns a list of eligible shipment methods
        $result = $instance->resolve(
            $serializer->denormalize(
                ['items' => [$item], 'shipping_address' => $shipping_address],
                EligibilityEnvelopeDto::class
            )
        );

        $this->assertInstanceOf(EligibleShipmentMethodDto::class, $result[0]);
        $this->assertEquals(
            [
                'shipment_method_id' => 13,
                'label' => 'Express',
                'comment' => 'Livraison pour Ile de France',
                'carrier_name' => 'Novea',
                'is_retail_store' => false,
                'cost' => 50.0,
                'tags' => ['express'],
            ],
            $serializer->normalize($result[0])
        );
    }
}
