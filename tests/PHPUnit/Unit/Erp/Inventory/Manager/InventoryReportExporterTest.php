<?php

namespace PHPUnit\Unit\Erp\Inventory\Manager;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use League\Flysystem\FilesystemInterface;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;
use SonVideo\Erp\Inventory\Manager\InventoryReportExporter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class InventoryReportExporterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'inventory/inventories.sql',
            'inventory/inventory_report.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): InventoryReportExporter
    {
        return self::$container->get(InventoryReportExporter::class);
    }

    /** Tests the export method. */
    public function test_export(): void
    {
        $inventory_id = 1;
        $expected_file_path = 'Rapport_inventaire_1.csv';
        $expected_result = <<<EOF
        Sous-catégorie,Marque,Modèle,SKU,"Total pré-inventaire","Total inventaire",Différentiel,Zone,Emplacement,"Qte pre-inventaire","Qte inventaire","Différentiel empl.","Prix achat"
        "Récepteurs Bluetooth",Arcam,test,AAAAAAAAA,1,0,-1,,03.04.TEST$02.01.01,1,0,-1,131.58
        "Récepteurs Bluetooth",Arcam,rBlink,ARCAMRBLINKNR,8,7,-1,"M1 - Allée A",03.01.A$01.01.01,0,4,4,131.58
        "Récepteurs Bluetooth",Arcam,rBlink,ARCAMRBLINKNR,8,7,-1,"TV 1",03.01.TEST$01.01.01,4,3,-1,131.58
        "Récepteurs Bluetooth",Arcam,rBlink,ARCAMRBLINKNR,8,7,-1,,03.04.TEST$02.01.01,4,0,-4,131.58

        EOF;

        // Test: Can create csv file for a full inventory
        $this->assertEquals($expected_file_path, $this->getTestedInstance()->export($inventory_id));
        $this->assertTrue($this->getFilesystem()->has($expected_file_path));
        $this->assertEquals($expected_result, $this->getFilesystem()->read($expected_file_path));

        $inventory_id = 3;
        $expected_file_path = 'Rapport_inventaire_3.csv';
        $expected_result = <<<EOF
        Sous-catégorie,Marque,Modèle,SKU,"Total pré-inventaire","Total inventaire",Différentiel,Zone,Emplacement,"Qte pre-inventaire","Qte inventaire","Différentiel empl.","Prix achat"
        "Récepteurs Bluetooth",Arcam,rBlink,ARCAMRBLINKNR,0,2,2,"M1 - Allée A",03.01.A$01.01.01,0,2,2,131.58
        ,,,,,,,"M1 - Allée A",03.04.A$01.01.01,0,0,0,
        ,,,,,,,"M1 - Allée A",03.05.A$01.01.01,0,0,0,

        EOF;

        // Test: Can create csv file for a partial inventory
        $this->assertEquals($expected_file_path, $this->getTestedInstance()->export($inventory_id));
        $this->assertTrue($this->getFilesystem()->has($expected_file_path));
        $this->assertEquals($expected_result, $this->getFilesystem()->read($expected_file_path));
    }

    /** Gets the filesystem. */
    public function getFilesystem(): FilesystemInterface
    {
        return self::$container->get(ExportedFile::class)->getFilesystem();
    }
}
