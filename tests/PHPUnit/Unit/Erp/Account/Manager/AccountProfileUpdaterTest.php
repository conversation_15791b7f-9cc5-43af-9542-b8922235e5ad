<?php

namespace PHPUnit\Unit\Erp\Account\Manager;

use App\Adapter\Serializer\SerializerInterface;
use SonVideo\Erp\Account\Dto\AccountProfileUpdateRequestDto;
use SonVideo\Erp\Account\Manager\AccountProfileUpdater;
use SonVideo\Erp\Account\Mysql\Repository\AccountCommandRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class AccountProfileUpdaterTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of AccountProfileUpdater. */
    protected function getTestedInstance(AccountCommandRepository $repository = null): AccountProfileUpdater
    {
        return new AccountProfileUpdater($repository);
    }

    /** Tests that the update method fails if the repository throws an exception. */
    public function test_fails_if_repository_throw_an_exception(): void
    {
        $dto = self::$container->get(SerializerInterface::class)->denormalize(
            [
                'user_id' => 666,
                'warehouse_id' => 1234,
            ],
            AccountProfileUpdateRequestDto::class
        );

        $instance = $this->getTestedInstance(
            new class() extends AccountCommandRepository {
                public function updateProfileWith(AccountProfileUpdateRequestDto $dto): int
                {
                    throw new \Exception('exception from repository');
                }
            }
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('exception from repository');

        $instance->update($dto);
    }

    /** Tests that the update method succeeds. */
    public function test_succeeds(): void
    {
        $dto = self::$container->get(SerializerInterface::class)->denormalize(
            [
                'user_id' => 666,
                'warehouse_id' => 1234,
                'last_name' => 'toto',
                'first_name' => 'titi',
            ],
            AccountProfileUpdateRequestDto::class
        );

        $instance = $this->getTestedInstance(
            new class() extends AccountCommandRepository {
                public function updateProfileWith(AccountProfileUpdateRequestDto $dto): int
                {
                    return 666;
                }
            }
        );

        $this->assertEquals(['user_id' => 666], $instance->update($dto));
    }
}
