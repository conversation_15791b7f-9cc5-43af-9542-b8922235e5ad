<?php

namespace PHPUnit\Unit\Erp\Account\DataProvider;

use App\Adapter\Serializer\SerializerInterface;
use App\Client\GraphQLClient;
use GuzzleHttp\Client;
use Guz<PERSON><PERSON>ttp\Handler\MockHandler;
use <PERSON>uz<PERSON><PERSON>ttp\HandlerStack;
use Guz<PERSON>Http\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Ramsey\Uuid\Uuid;
use SonVideo\Erp\Account\DataProvider\AccountFromHalDataProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class AccountFromHalDataProviderTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of AccountFromHalDataProvider. */
    protected function getTestedInstance(MockHandler $mock, array &$container): AccountFromHalDataProvider
    {
        $history = Middleware::history($container);
        $handler_stack = HandlerStack::create($mock);
        $handler_stack->push($history);

        $instance = new AccountFromHalDataProvider(
            new GraphQLClient('', '', self::$container->get('logger'), new Client(['handler' => $handler_stack]))
        );

        $instance->setSerializer(self::$container->get(SerializerInterface::class));

        return $instance;
    }

    /** Tests what's sent to GraphQL. */
    public function test_whats_sent_to_graphql(): void
    {
        $container = [];
        $json = <<<'JSON'
        {"data":{"who_did_it":{"legacy_account_id":1},"for_who":{"legacy_account_id":2}}}
        JSON;

        $this->getTestedInstance(
            new MockHandler([new Response(200, [], $json)]),
            $container
        )->getLegacyAccountIdsForModifiedData(
            Uuid::fromString('4d09c38c-18d6-44ae-9bab-2322396eb124'),
            Uuid::fromString('4d09c38c-18d6-44ae-9bab-2322396eb125')
        );

        /** @var $request Request */
        $request = $container[0]['request'];
        $content = json_decode($request->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals(
            [
                'who_did_it' => '4d09c38c-18d6-44ae-9bab-2322396eb124',
                'for_who' => '4d09c38c-18d6-44ae-9bab-2322396eb125',
            ],
            $content['variables']
        );
    }
}
