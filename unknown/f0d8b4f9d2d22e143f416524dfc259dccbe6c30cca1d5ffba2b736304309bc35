<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgDataWarehouse\DataSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * CustomerOrderInitialLine.
 *
 * Structure class for relation data.customer_order_initial_line.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 *
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 * @see RowStructure
 */
class CustomerOrderInitialLine extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('data.customer_order_initial_line')
            ->setPrimaryKey(['customer_order_product_initial_id'])
            ->addField('customer_order_product_initial_id', 'int4')
            ->addField('reference', 'varchar')
            ->addField('model', 'varchar')
            ->addField('brand', 'varchar')
            ->addField('supplier', 'varchar')
            ->addField('quantity', 'int4')
            ->addField('total_purchase_cost', 'numeric')
            ->addField('total_gross_excl_tax', 'numeric')
            ->addField('total_discount_excl_tax', 'numeric')
            ->addField('total_net_excl_tax', 'numeric')
            ->addField('total_guarantee_excl_tax', 'numeric')
            ->addField('total_margin', 'numeric')
            ->addField('subcategory', 'varchar')
            ->addField('subcategory_id', 'int4')
            ->addField('category', 'varchar')
            ->addField('category_id', 'int4')
            ->addField('domain', 'varchar')
            ->addField('domain_id', 'int4')
            ->addField('customer_order_id', 'int4')
            ->addField('vat_rate', 'numeric')
            ->addField('promo_code', 'varchar')
            ->addField('product_type', 'varchar')
            ->addField('available_quantity', 'int4')
            ->addField('estimated_supplier_order_delivery_date', 'date');
    }
}
