INCLUDE_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))/.phcstack
FEATURE := "tests/Acceptance/features/"

include $(INCLUDE_DIR)/with-composer.mk
include $(INCLUDE_DIR)/with-deploy.mk
include $(INCLUDE_DIR)/with-synapps.mk
include $(INCLUDE_DIR)/with-registry.mk
include $(INCLUDE_DIR)/base.mk

.PHONY: install
install: before-install composer-install ## Installation du projet + setup de base pour la BDD. Les données du dernier dump ne sont pas chargée
	@bash $(BASE_DIR)/.phcstack/mysql_run_file.sh erp-server-mysql-test $(BASE_DIR)/sql/mysql/roles.sql 30
	@cd $(BASE_DIR) && make synapps-register

.PHONY: start-graphql
start-graphql: ##@ D<PERSON><PERSON>re <PERSON> (Graphql)
	@$(DOCKER_COMPOSE) up -d erp-graphql

.PHONY: stop-graphql
stop-graphql: ##@ Stoppe Hasura (Graphql)
	@$(DOCKER_COMPOSE) stop erp-graphql

required-dependencies:: ##@ Démarre les dépendances requises (mais ne les installent pas). Ex: BDD
	@bash $(BASE_DIR)/.phcstack/check_dependency.sh "ERP PG Schema" erp-postgresql erp-pg-schema

exec-help::
	@printf " $(INFO_COLOR)Setup de la BDD de tests (pour les tests PHPUnit)$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)make setup-test-databases$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)Doc des tests$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)Voir dans le README du projet$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)Unit tests$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)vendor/bin/atoum -c .atoum.php --no-code-coverage -d tests/Unit$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)E2E tests$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)vendor/bin/behat tests/Acceptance/features$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)PHPUnit$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)bin/phpunit --testdox --testsuite <unit|e2e>$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)Static analysis$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)vendor/bin/phpstan$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)Refactoring$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)vendor/bin/rector --dry-run$(NO_COLOR)\n"
	@printf " $(INFO_COLOR)Pomm schema$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR)bin/console pomm:generate:schema-all --prefix-dir src/App/Database --prefix-ns App\\\\\\Database <pg_erp_server|pg_data_warehouse> <eav|system|carrier> --psr4$(NO_COLOR)\n"

.PHONY: setup-test-databases
setup-test-databases: ##@ Reload la base de données de test
	@rm -f var/cache/schema_stamp
	@docker exec -e SKIP_DB_INIT=false -it $(EXEC_CONTAINER_NAME)  php .atoum.php

.PHONY: unit-tests
unit-tests: setup-test-databases atoum-tests phpunit-unit-tests ##@ Joue les tests unitaires.

.PHONY: atoum-tests
atoum-tests: ##@ Joue les test unitaires Atoum.
	@docker exec -it $(EXEC_CONTAINER_NAME) vendor/bin/atoum -c .atoum.php -d tests/Unit --no-code-coverage --debug

.PHONY: phpunit-unit-tests
phpunit-unit-tests: setup-test-databases ##@ Joue les test unitaires phpunit
	@docker exec -e XDEBUG_MODE="coverage" -it $(EXEC_CONTAINER_NAME) bin/phpunit --testdox --testsuite unit

.PHONY: phpunit-e2e-tests
phpunit-e2e-tests: setup-test-databases ##@ Joue les test phpunit e2e
	@docker exec -e XDEBUG_MODE="coverage" -it $(EXEC_CONTAINER_NAME) bin/phpunit --testdox --testsuite e2e

.PHONY: behat-tests
behat-tests: ##@ Joue les tests E2E behat
	@docker exec -it $(EXEC_CONTAINER_NAME) vendor/bin/behat $(FEATURE) -vvv

.PHONY: e2e-tests
e2e-tests: setup-test-databases behat-tests phpunit-e2e-tests ##@ Joue les tests end-to-end

.PHONY: php-cs-fixer
php-cs-fixer: ##@ Joue php cs fixer sur le projet (en dry run)
	@docker exec -it $(EXEC_CONTAINER_NAME) vendor/bin/php-cs-fixer fix --diff --dry-run

.PHONY: rector
rector: ##@ Joue rector sur le projet (en dry run)
	@docker exec -it $(EXEC_CONTAINER_NAME) vendor/bin/rector --dry-run

.PHONY: composer-update-phcdb
composer-update-phcdb: ##@ mise à jour de la dependence composer phcdb
	@docker exec -it $(EXEC_CONTAINER_NAME) composer update son-video/phcdb
