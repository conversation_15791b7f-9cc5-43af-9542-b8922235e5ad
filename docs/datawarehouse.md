# Documentation DataWarehouse

## Introduction

Cette documentation explique comment ajouter un nouveau topic à synchroniser dans le système de DataWarehouse. Le
système de synchronisation utilise une architecture basée sur des topics pour transférer des données entre différentes
bases de données.

## Architecture du système

Le système de synchronisation DataWarehouse est composé des éléments suivants :

1. **SynchronizableTopic** : Entité représentant un topic à synchroniser, stockée dans la table
   `backOffice.synchronizable_topic`.
2. **TopicContent** : Classes qui définissent la structure des données à synchroniser pour chaque type de topic.
3. **TopicSynchronizer** : Classes qui implémentent la logique de synchronisation pour chaque type de topic.
4. **SpoolerDataWarehouseDequeueCommand** : Commande Symfony qui traite les topics en attente de synchronisation.

## Processus de synchronisation

Le processus de synchronisation se déroule comme suit :

1. Un topic est créé et enregistré dans la table `synchronizable_topic` avec le statut `CREATED`.
2. La commande `spooler:data-warehouse:dequeue` est exécutée (généralement via un cron).
3. La commande verrouille les topics à traiter en changeant leur statut à `LOCKED`.
4. Pour chaque topic verrouillé, le système identifie le handler approprié via la `TopicSynchronizerCollection`.
5. Le handler traite le topic et effectue la synchronisation des données.
6. Une fois la synchronisation réussie, le topic est supprimé de la table.

## Ajouter un nouveau topic à synchroniser

Pour ajouter un nouveau topic à synchroniser, vous devez suivre les étapes suivantes :

### 1. Définir le nom du topic

Ajoutez d'abord une constante pour votre nouveau topic dans la classe `SynchronizableTopicName` :

```php
// Dans src/SonVideo/Erp/Referential/DataWarehouse/SynchronizableTopicName.php
class SynchronizableTopicName
{
    // ... autres constantes existantes
    public const MY_SUBJECT_ACTION = 'my_subject.action';
}
```

### 2. Créer une classe TopicContent

Créez une nouvelle classe qui étend `AbstractSynchronizableTopicContent` dans le répertoire
`src/SonVideo/Erp/DataWarehouse/Entity/SynchronizableTopic/`.

**Convention de nommage** : Le nom de la classe doit se terminer par `TopicContent`.

```php
<?php

namespace SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;

class MySubjectActionTopicContent extends AbstractSynchronizableTopicContent
{
    // Propriétés spécifiques à votre topic
    public int $my_subject_id;
    public string $name;
    public \DateTimeImmutable $created_at;
    // Ajoutez d'autres propriétés selon vos besoins
}
```

### 3. Enregistrer la classe dans le DiscriminatorMap

Modifiez la classe `AbstractSynchronizableTopicContent` pour ajouter votre nouveau topic dans l'annotation
`@DiscriminatorMap` en utilisant la constante définie :

```php
/**
 * @DiscriminatorMap(typeProperty="topic", mapping={
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT=CustomerOrderInitialUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT=CustomerOrderPaymentStatusUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_UPSERT=CustomerOrderPaymentUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::CUSTOMER_ORDER_UPSERT=CustomerOrderUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::SAFETY_STOCK_UPSERT=SafetyStockUpsertTopicContent::class,
 *    \SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName::MY_SUBJECT_ACTION=MySubjectActionTopicContent::class,
 * })
 */
```

### 4. Créer un TopicSynchronizer

Créez une classe qui étend `AbstractTopicSynchronizer` dans le répertoire
`src/SonVideo/Erp/DataWarehouse/Manager/SynchronizableTopic/` :

**Convention de nommage** : Le nom de la classe doit se terminer par `TopicSynchronizer`.

```php
<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\MySubjectRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\MySubjectActionTopicContent;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class MySubjectActionSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::MY_SUBJECT_ACTION;
    private MySubjectRepository $subject_repository;

    public function __construct(MySubjectRepository $subject_repository)
    {
        $this->subject_repository = $subject_repository;
    }

    /** @param MySubjectActionTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            $this->subject_repository->upsert($this->serializer->normalize($synchronizable_topic));
        } catch (\Exception $exception) {
            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
```

### 5. Enregistrer le TopicSynchronizer dans le conteneur de services

> Les explications de cette section sont informationnelles, il n'y a d'action à effectuer dans le projet.

Le service sera automatiquement enregistré grâce à l'auto-configuration de Symfony. Cependant, vous devez vous assurer
qu'il est correctement tagué pour être ajouté à la collection de synchroniseurs.

Dans le fichier `config/services.yaml`, les services qui implémentent `TopicSynchronizerInterface` sont automatiquement
tagués avec `app.data_warehouse_spooler.topic_handler` :

```yaml
_instanceof:
    SonVideo\Erp\DataWarehouse\Contract\TopicSynchronizerInterface:
        tags: [ 'app.data_warehouse_spooler.topic_handler' ]
```

### 6. Créer un topic à synchroniser

> Les explications de cette section sont informationnelles, il n'y a d'action à effectuer dans le projet.

Pour créer un topic à synchroniser, utilisez le repository `SynchronizableTopicWriteRepository` :

```php
// Dans votre service ou manager
$this->synchronizable_topic_repository->upsert([
    'target' => 'conflict_resolution_key',
    'topic' => SynchronizableTopicName::MY_SUBJECT_ACTION,
    'content' => [
        'my_subject_id' => 123,
        'name' => 'Exemple',
        'created_at' => new \DateTimeImmutable(),
        // Autres propriétés selon votre TopicContent
    ],
]);
```

Le `conflict_resolution_key` sert à éviter de traiter plusieurs fois le même topic. Si un topic avec la même
`conflict_resolution_key` existe déjà, il sera mis à jour.

> Il est également possible de créer un topic directement en BDD, par exemple via les triggers. Rechercher des exemples
> dans le
> projet `PhcDb` (en cherchant les interactions avec la table `backOffice.synchronizable_topic`)

## Exécution de la commande de synchronisation

La commande `spooler:data-warehouse:dequeue` est responsable de la synchronisation des topics. Elle peut être exécutée
manuellement ou via un cron :

> Cette cron est programmée pour tourner toutes les minutes sur les environnements AWS (Voir dans le projet Ansible :
`roles/erp-server/files/crontab:83`)

```bash
# Exécution manuelle
php bin/console spooler:data-warehouse:dequeue

# Avec une limite de topics à traiter
php bin/console spooler:data-warehouse:dequeue --limit=100

# Pour un topic spécifique
php bin/console spooler:data-warehouse:dequeue --topic-id=123
```

## Exemple complet d'implémentation

Voici un exemple complet d'implémentation pour synchroniser des données de stock de sécurité :

### 1. Classe TopicContent

```php
<?php

namespace SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;

class SafetyStockUpsertTopicContent extends AbstractSynchronizableTopicContent
{
    public int $product_id;
    public int $supplier_id;
    public int $brand_id;
    public \DateTimeImmutable $snapshotted_at;
    public int $subcategory_id;
    public ?float $safety_stock = null;
    public ?float $shipping_delay = null;
}
```

### 2. Classe TopicSynchronizer

```php
<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SafetyStockRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SafetyStockUpsertTopicContent;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class SafetyStockTopicUpsertSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::SAFETY_STOCK_UPSERT;
    private SafetyStockRepository $safety_stock_repository;

    public function __construct(SafetyStockRepository $safety_stock_repository)
    {
        $this->safety_stock_repository = $safety_stock_repository;
    }

    /** @param SafetyStockUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            $this->safety_stock_repository->upsert($this->serializer->normalize($synchronizable_topic));
        } catch (\Exception $exception) {
            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
```

### 3. Création d'un topic à synchroniser

```php
// Dans un service ou manager
$this->synchronizable_topic_repository->upsert([
    'target' => (string)$product_id,
    'topic' => SynchronizableTopicName::SAFETY_STOCK_UPSERT,
    'content' => [
        'product_id' => $product_id,
        'supplier_id' => $supplier_id,
        'brand_id' => $brand_id,
        'snapshotted_at' => new \DateTimeImmutable(),
        'subcategory_id' => $subcategory_id,
        'safety_stock' => $safety_stock,
        'shipping_delay' => $shipping_delay,
    ],
]);
```

## Conclusion

Le système de synchronisation DataWarehouse permet de transférer des données entre différentes bases de données de
manière asynchrone et fiable. En suivant les étapes décrites dans cette documentation, vous pouvez facilement ajouter de
nouveaux types de données à synchroniser.
