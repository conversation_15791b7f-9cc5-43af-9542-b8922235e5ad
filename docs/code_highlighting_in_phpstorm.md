### Colorations syntaxiques personnalisées dans PHPStorm

Afin de séparer les dialectes SQL dans que PHPStorm ne sache si c'est du PG ou du MySQL,    
ou la reconnaissance et la complétion de requête GraphQL,   
vous pouvez utiliser les HEREDOC/NOWDOC personalisés.

#### 1. <PERSON><PERSON><PERSON> "Language Injection" dans les Settings

![code_highlighting_in_phpstorm-1.png](code_highlighting_in_phpstorm-1.png)

#### 2. Copier la reconnaissance de base `<<<SQL`

![code_highlighting_in_phpstorm-2.png](code_highlighting_in_phpstorm-2.png)

#### 3. <PERSON><PERSON><PERSON> le tag `MYSQL`

![code_highlighting_in_phpstorm-3.png](code_highlighting_in_phpstorm-3.png)

#### 3. C<PERSON><PERSON> le tag `PSQL`

![code_highlighting_in_phpstorm-4.png](code_highlighting_in_phpstorm-4.png)

#### 3. <PERSON><PERSON><PERSON> le tag `GQL`

![code_highlighting_in_phpstorm-5.png](code_highlighting_in_phpstorm-5.png)



