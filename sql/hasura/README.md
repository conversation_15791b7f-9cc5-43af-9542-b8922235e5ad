## Utilisation des namespaces

Hasura utilise à la fois des tables de la BDD PG de l'ERP et du CMS.

Afin d'éviter des collisions de noms et faciliter la maintenance à long terme, il est fortement recommandé
d'ajouter un prefix sur les connections supplémentaires.

On considère que la connection par défaut est ERP et toutes les autres BDD devront utiliser un espace de nom dédié.
Ici toutes les tables CMS **devront** donc être préfixées par "cms\_"
(concerne toutes les tables hormis celles présentes dans le schéma cms).

## Modification via l'interface (Didacticiel vidéo)

![Custom_graphql_name.webm](./Custom_graphql_name.webm)

## Ajout via l'API d'Hasura

- Ajouter la table via l'API Explorer (https://erp-graphql.lxc/console/data/manage)
- Une fois ajoutée, utiliser une console REST de PHPStorm pour ajouter un namespace à la table
    - Exemple avec la table `carrier.shipment_method`
    - Ajouter le nom custom avec le masque `<namespace>_<schema>_<table>` :

```
POST https://erp-graphql.lxc/v1/metadata
Content-Type: application/json
X-Hasura-Role: admin
x-hasura-admin-secret: SuperSecret

{
  "type": "pg_set_table_customization",
  "args": {
    "table": {
      "name": "shipment_method",
      "schema":  "carrier"
    },
    "source": "cms",
    "configuration": {
      "custom_name":  "cms_carrier_shipment_method"
    }
  }
}
```

- Vérifier dans l'API explorer que la table est référencée sous `cms_carrier_shipment_method`
- Exporter les metadata

Plus d'info dans la doc d'
Hasura (https://hasura.io/docs/latest/graphql/core/api-reference/metadata-api/table-view.html#pg-set-table-customization)

## Ajout manuel (non recommandé)

- Rechercher la table exportée dans les metadata

```json
{
  "table": {
    "schema": "carrier",
    "name": "shipment_method"
  }
}
```

- Ajouter la configuration manuellement (avant les permissions s'il y en a) :

```json
{
  "table": {
    "schema": "carrier",
    "name": "shipment_method"
  },
  "configuration": {
    "custom_root_fields": {},
    "custom_name": "cms_carrier_shipment_method",
    "custom_column_names": {}
  }
}
```

- Vérfier dans l'API explorer que la table est référencée sous `cms_carrier_shipment_method`
- Exporter les metadata

bank_deposit site_id => account.site
