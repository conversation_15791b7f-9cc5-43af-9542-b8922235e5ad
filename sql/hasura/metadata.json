{"resource_version": 55, "metadata": {"version": 3, "sources": [{"name": "cms", "kind": "postgres", "tables": [{"table": {"name": "article", "schema": "article"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_article_article", "custom_root_fields": {}}, "array_relationships": [{"name": "medias", "using": {"foreign_key_constraint_on": {"column": "article_id", "table": {"name": "article_media", "schema": "article"}}}}, {"name": "medias_i18n", "using": {"manual_configuration": {"column_mapping": {"article_id": "article_id"}, "insertion_order": null, "remote_table": {"name": "article_media_i18n", "schema": "article"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["article_id", "sku", "name", "weight_g", "brand_id", "batch_sale", "common_content_id", "internal_unbasketable_comment", "embargo_date", "created_at", "unbasketable_reason", "price", "packaged_articles", "article_url", "manufacturer_warranty_yr", "svd_warranty_yr", "replaced_by", "default_stand_id", "eav_attributes", "eav_facets"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "article_media", "schema": "article"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_article_article_media", "custom_root_fields": {}}, "object_relationships": [{"name": "article", "using": {"foreign_key_constraint_on": "article_id"}}], "array_relationships": [{"name": "i18n", "using": {"foreign_key_constraint_on": {"column": "media_id", "table": {"name": "article_media_i18n", "schema": "article"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["article_id", "media_variation"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["article_id", "media_id", "media_variation"], "filter": {}, "allow_aggregations": true}}], "delete_permissions": [{"role": "user", "permission": {"filter": {}}}]}, {"table": {"name": "article_media_i18n", "schema": "article"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_article_article_media_i18n", "custom_root_fields": {}}, "object_relationships": [{"name": "media", "using": {"foreign_key_constraint_on": "media_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["media_id", "article_id", "supported_culture_id", "display_order", "meta", "type"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["article_id", "display_order", "media_id", "meta", "supported_culture_id", "type"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["display_order", "media_id", "meta"], "filter": {}, "check": null}}], "delete_permissions": [{"role": "user", "permission": {"filter": {}}}]}, {"table": {"name": "shipment_method", "schema": "carrier"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_carrier_shipment_method", "custom_root_fields": {}}, "select_permissions": [{"role": "user", "permission": {"columns": ["carrier_id", "shipment_method_id", "logo", "description_i18n", "is_online", "title_i18n"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["description_i18n", "is_online", "logo", "title_i18n"], "filter": {}, "check": null}}]}, {"table": {"name": "warehouse", "schema": "cms"}, "select_permissions": [{"role": "user", "permission": {"columns": ["data", "delivery_open_day", "shipment_method_id", "warehouse_id"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["data", "delivery_open_day", "shipment_method_id"], "filter": {}, "check": null}}]}, {"table": {"name": "account", "schema": "customer"}, "select_permissions": [{"role": "user", "permission": {"columns": ["created_at", "customer_id", "email", "fail_logon_bucket", "is_active", "last_connection"], "filter": {}}}]}, {"table": {"name": "account_information", "schema": "customer"}, "select_permissions": [{"role": "user", "permission": {"columns": ["preferences", "title", "addresses", "birthday", "cellphone", "firstname", "customer_id", "lastname", "phone", "pseudonym"], "filter": {}}}]}, {"table": {"name": "article", "schema": "draft_article"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_draft_article_article", "custom_root_fields": {}}, "array_relationships": [{"name": "medias", "using": {"manual_configuration": {"column_mapping": {"article_id": "article_id"}, "insertion_order": null, "remote_table": {"name": "article_media", "schema": "draft_article"}}}}, {"name": "medias_i18n", "using": {"manual_configuration": {"column_mapping": {"article_id": "article_id"}, "insertion_order": null, "remote_table": {"name": "article_media_i18n", "schema": "draft_article"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["article_id", "batch_sale", "brand_id", "common_content_id", "default_stand_id", "eav_attributes", "eav_facets", "embargo_date", "final_common_content_id", "manufacturer_warranty_yr", "name", "packaged_articles", "price", "sku", "svd_warranty_yr", "unbasketable_reason", "weight_g"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "article_media", "schema": "draft_article"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_draft_article_article_media", "custom_root_fields": {}}, "object_relationships": [{"name": "article", "using": {"manual_configuration": {"column_mapping": {"article_id": "article_id"}, "insertion_order": null, "remote_table": {"name": "article", "schema": "draft_article"}}}}], "array_relationships": [{"name": "i18n", "using": {"manual_configuration": {"column_mapping": {"media_id": "media_id"}, "insertion_order": null, "remote_table": {"name": "article_media_i18n", "schema": "draft_article"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["article_id", "media_variation"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["media_id", "article_id", "media_variation"], "filter": {}, "allow_aggregations": true}}], "delete_permissions": [{"role": "user", "permission": {"filter": {}}}]}, {"table": {"name": "article_media_i18n", "schema": "draft_article"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_draft_article_article_media_i18n", "custom_root_fields": {}}, "object_relationships": [{"name": "media", "using": {"manual_configuration": {"column_mapping": {"media_id": "media_id"}, "insertion_order": null, "remote_table": {"name": "article_media", "schema": "draft_article"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["media_id", "article_id", "supported_culture_id", "display_order", "meta", "type"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["media_id", "article_id", "supported_culture_id", "display_order", "meta", "type"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["media_id", "display_order", "meta"], "filter": {}, "check": {}}}], "delete_permissions": [{"role": "user", "permission": {"filter": {}}}]}, {"table": {"name": "owner", "schema": "permission_refactored"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_owner", "custom_root_fields": {}}}, {"table": {"name": "owner_permission", "schema": "permission_refactored"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_owner_permission", "custom_root_fields": {}}}, {"table": {"name": "permission", "schema": "permission_refactored"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_permission", "custom_root_fields": {}}}, {"table": {"name": "promo_offer", "schema": "promo_offer"}, "computed_fields": [{"name": "end_at", "definition": {"function": {"name": "promo_offer_end_at", "schema": "promo_offer"}}}, {"name": "start_at", "definition": {"function": {"name": "promo_offer_start_at", "schema": "promo_offer"}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["promo_offer_id", "stand_id", "image_banner", "article_editorial_content_i18n", "article_short_description_i18n", "banner_i18n", "clickbait_id", "selection_id", "slug", "definition", "computed_article_ids", "name", "updated_at", "deactivated_reason", "lifetime"], "computed_fields": ["end_at", "start_at"], "filter": {}}}]}, {"table": {"name": "parameter", "schema": "system"}, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "cms_system_parameter", "custom_root_fields": {}}, "select_permissions": [{"role": "user", "permission": {"columns": ["description", "value", "name"], "filter": {}}}], "update_permissions": [{"role": "user", "permission": {"columns": ["value"], "filter": {}, "check": null}}]}], "configuration": {"connection_info": {"database_url": {"from_env": "CMS_DATABASE_URL"}, "isolation_level": "read-committed", "pool_settings": {"connection_lifetime": 600, "idle_timeout": 60, "max_connections": 10}, "use_prepared_statements": true}}}, {"name": "erp", "kind": "postgres", "tables": [{"table": {"name": "account", "schema": "account"}, "object_relationships": [{"name": "information", "using": {"manual_configuration": {"column_mapping": {"account_id": "account_id"}, "insertion_order": null, "remote_table": {"name": "account_information", "schema": "account"}}}}, {"name": "site", "using": {"foreign_key_constraint_on": "site_id"}}], "array_relationships": [{"name": "account_roles", "using": {"foreign_key_constraint_on": {"column": "account_id", "table": {"name": "account_role", "schema": "account"}}}}, {"name": "permissions", "using": {"manual_configuration": {"column_mapping": {"account_id": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner_permission", "schema": "permission"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["account_id", "email", "is_active", "is_sales_consultant", "legacy_account_id", "site_id", "username"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["account_id", "created_at", "email", "is_active", "is_sales_consultant", "is_synced", "legacy_account_id", "site_id", "username"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["email", "is_active", "is_sales_consultant", "legacy_account_id", "site_id", "username"], "filter": {}, "check": null}}], "event_triggers": [{"name": "account_account", "definition": {"enable_manual": false, "insert": {"columns": "*"}, "update": {"columns": ["is_active", "is_sales_consultant", "username", "legacy_account_id", "account_id"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 60}, "webhook": "{{HAL_WEBHOOK_URL}}", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "HAL_WEBHOOK_SECRET"}]}]}, {"table": {"name": "account_information", "schema": "account"}, "object_relationships": [{"name": "account", "using": {"foreign_key_constraint_on": "account_id"}}], "computed_fields": [{"name": "has_avatar", "definition": {"function": {"name": "account_information_has_avatar", "schema": "account"}}}, {"name": "name", "definition": {"function": {"name": "account_information_computed_name", "schema": "account"}}}, {"name": "phone_for_search", "definition": {"function": {"name": "account_information_phones_for_search", "schema": "account"}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["account_id", "addresses", "firstname", "job", "lastname", "phones", "preferences", "title"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["addresses", "phones", "preferences", "firstname", "lastname", "title", "account_id", "avatar", "job"], "computed_fields": ["has_avatar", "name", "phone_for_search"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["addresses", "firstname", "job", "lastname", "phones", "preferences", "title"], "filter": {}, "check": null}}], "event_triggers": [{"name": "account_account_information", "definition": {"enable_manual": false, "insert": {"columns": "*"}, "update": {"columns": ["preferences", "firstname", "lastname"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 60}, "webhook": "{{HAL_WEBHOOK_URL}}", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "HAL_WEBHOOK_SECRET"}]}]}, {"table": {"name": "account_role", "schema": "account"}, "object_relationships": [{"name": "role", "using": {"foreign_key_constraint_on": "role_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["account_id", "role_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["account_id", "role_id"], "filter": {}, "allow_aggregations": true}}], "delete_permissions": [{"role": "user", "permission": {"filter": {}}}], "event_triggers": [{"name": "account_account_role", "definition": {"delete": {"columns": "*"}, "enable_manual": false, "insert": {"columns": "*"}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 60}, "webhook": "{{HAL_WEBHOOK_URL}}", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "HAL_WEBHOOK_SECRET"}]}]}, {"table": {"name": "app_authorization", "schema": "account"}, "event_triggers": [{"name": "account_app_authorization", "definition": {"enable_manual": false, "insert": {"columns": "*"}, "update": {"columns": ["app_id", "profile", "name", "erp_application_name", "client_id"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 60}, "webhook": "{{HAL_WEBHOOK_URL}}", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "HAL_WEBHOOK_SECRET"}]}]}, {"table": {"name": "role", "schema": "account"}, "select_permissions": [{"role": "user", "permission": {"columns": ["is_synced", "description", "name", "role_id"], "filter": {}, "allow_aggregations": true}}], "event_triggers": [{"name": "account_role", "definition": {"enable_manual": false, "insert": {"columns": "*"}, "update": {"columns": ["role_id", "name", "description"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 60}, "webhook": "{{HAL_WEBHOOK_URL}}", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "HAL_WEBHOOK_SECRET"}]}]}, {"table": {"name": "site", "schema": "account"}, "array_relationships": [{"name": "accounts", "using": {"foreign_key_constraint_on": {"column": "site_id", "table": {"name": "account", "schema": "account"}}}}], "computed_fields": [{"name": "information", "definition": {"function": {"name": "account_site_information", "schema": "account"}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["phones", "address", "city", "name", "postal_code", "company_id", "site_id", "misc", "is_store", "legacy_site_id"], "computed_fields": ["information"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "balance", "schema": "checkout"}, "object_relationships": [{"name": "closed_by_user", "using": {"manual_configuration": {"column_mapping": {"closed_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}, {"name": "opened_by_user", "using": {"manual_configuration": {"column_mapping": {"opened_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}, {"name": "store", "using": {"manual_configuration": {"column_mapping": {"store_id": "site_id"}, "insertion_order": null, "remote_table": {"name": "site", "schema": "account"}}}}], "array_relationships": [{"name": "balance_amounts", "using": {"foreign_key_constraint_on": {"column": "balance_id", "table": {"name": "balance_amount", "schema": "checkout"}}}}, {"name": "balance_comments", "using": {"foreign_key_constraint_on": {"column": "balance_id", "table": {"name": "balance_comment", "schema": "checkout"}}}}, {"name": "bank_deposits", "using": {"foreign_key_constraint_on": {"column": "balance_id", "table": {"name": "bank_deposit", "schema": "checkout"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["opened_by", "store_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["balance_id", "store_id", "opened_by", "opened_at", "closed_by", "closed_at"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["closed_at", "closed_by"], "filter": {}, "check": null}}]}, {"table": {"name": "balance_amount", "schema": "checkout"}, "object_relationships": [{"name": "balance", "using": {"foreign_key_constraint_on": "balance_id"}}, {"name": "created_by_user", "using": {"manual_configuration": {"column_mapping": {"created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}, {"name": "payment_mean", "using": {"foreign_key_constraint_on": "payment_mean_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["balance_amount_id", "balance_id", "beginning_quantity", "beginning_unit_amount", "closing_quantity", "closing_unit_amount", "created_by", "payment_mean_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["balance_amount_id", "balance_id", "payment_mean_id", "created_by", "beginning_unit_amount", "beginning_quantity", "closing_unit_amount", "closing_quantity"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["closing_quantity", "closing_unit_amount"], "filter": {}, "check": null}}]}, {"table": {"name": "balance_comment", "schema": "checkout"}, "object_relationships": [{"name": "balance", "using": {"foreign_key_constraint_on": "balance_id"}}, {"name": "created_by_user", "using": {"manual_configuration": {"column_mapping": {"created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["balance_id", "body", "created_by", "meta"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["body", "balance_id", "meta", "created_at", "balance_comment_id", "created_by"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "bank_deposit", "schema": "checkout"}, "object_relationships": [{"name": "balance", "using": {"foreign_key_constraint_on": "balance_id"}}, {"name": "created_by_user", "using": {"manual_configuration": {"column_mapping": {"created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}, {"name": "store", "using": {"manual_configuration": {"column_mapping": {"store_id": "site_id"}, "insertion_order": null, "remote_table": {"name": "site", "schema": "account"}}}}], "array_relationships": [{"name": "bank_deposit_amounts", "using": {"foreign_key_constraint_on": {"column": "bank_deposit_id", "table": {"name": "bank_deposit_amount", "schema": "checkout"}}}}, {"name": "bank_deposit_comments", "using": {"foreign_key_constraint_on": {"column": "bank_deposit_id", "table": {"name": "bank_deposit_comment", "schema": "checkout"}}}}], "computed_fields": [{"name": "declared_amount", "definition": {"function": {"name": "bank_deposit_declared_amount", "schema": "checkout"}}, "comment": ""}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["balance_id", "created_by", "store_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["balance_id", "bank_deposit_id", "created_at", "created_by", "store_id"], "computed_fields": ["declared_amount"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "bank_deposit_amount", "schema": "checkout"}, "object_relationships": [{"name": "bank_deposit", "using": {"foreign_key_constraint_on": "bank_deposit_id"}}, {"name": "payment_mean", "using": {"foreign_key_constraint_on": "payment_mean_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["bank_deposit_id", "payment_mean_id", "quantity"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["bank_deposit_amount_id", "bank_deposit_id", "payment_mean_id", "quantity"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "bank_deposit_comment", "schema": "checkout"}, "object_relationships": [{"name": "bank_deposit", "using": {"foreign_key_constraint_on": "bank_deposit_id"}}, {"name": "created_by_user", "using": {"manual_configuration": {"column_mapping": {"created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["bank_deposit_id", "body", "created_by"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["bank_deposit_comment_id", "bank_deposit_id", "created_by", "created_at", "body"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "payment_mean", "schema": "checkout"}, "array_relationships": [{"name": "balance_amounts", "using": {"foreign_key_constraint_on": {"column": "payment_mean_id", "table": {"name": "balance_amount", "schema": "checkout"}}}}, {"name": "bank_deposit_amounts", "using": {"foreign_key_constraint_on": {"column": "payment_mean_id", "table": {"name": "bank_deposit_amount", "schema": "checkout"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["label", "meta", "payment_mean_id", "unit_amount"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "release_note", "schema": "communication"}, "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["release_note_id", "type", "status", "released_at", "title", "description", "notes", "extracted_notes", "tags"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["tags", "description", "release_note_id", "status", "title", "type", "released_at", "extracted_notes", "notes"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["description", "extracted_notes", "notes", "released_at", "status", "tags", "title", "type"], "filter": {}, "check": null}}]}, {"table": {"name": "attribute", "schema": "eav"}, "array_relationships": [{"name": "attribute_values", "using": {"foreign_key_constraint_on": {"column": "attribute_id", "table": {"name": "attribute_value", "schema": "eav"}}}}, {"name": "subcategory_attributes", "using": {"foreign_key_constraint_on": {"column": "attribute_id", "table": {"name": "subcategory_attribute", "schema": "eav"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_CREATE"}}]}}}, "columns": ["attribute_id", "name", "meta", "definition", "i18n"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["attribute_id", "name", "meta", "definition", "i18n"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_READ"}}]}}}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["definition", "i18n", "meta", "name"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_UPDATE"}}]}}}, "check": null}}], "event_triggers": [{"name": "eav_attribute", "definition": {"enable_manual": false, "update": {"columns": ["name", "definition", "meta", "i18n"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 90}, "webhook_from_env": "ERP_WEBHOOK_URL", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "ERP_WEBHOOK_SECRET"}]}]}, {"table": {"name": "attribute_value", "schema": "eav"}, "object_relationships": [{"name": "attribute", "using": {"foreign_key_constraint_on": "attribute_id"}}], "array_relationships": [{"name": "product_values", "using": {"foreign_key_constraint_on": {"column": "attribute_value_id", "table": {"name": "product_value", "schema": "eav"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_CREATE"}}]}}}, "columns": ["attribute_id", "display_order", "i18n", "meta", "value"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["attribute_id", "attribute_value_id", "display_order", "i18n", "meta", "value"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_READ"}}]}}}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["display_order", "value"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_CREATE"}}]}}}, "check": null}}], "delete_permissions": [{"role": "user", "permission": {"filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_DELETE"}}]}}}}}], "event_triggers": [{"name": "eav_attribute_value", "definition": {"delete": {"columns": "*"}, "enable_manual": false, "insert": {"columns": "*"}, "update": {"columns": ["display_order", "meta", "value", "i18n"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 90}, "webhook_from_env": "ERP_WEBHOOK_URL", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "ERP_WEBHOOK_SECRET"}]}]}, {"table": {"name": "product_subcategory", "schema": "eav"}, "insert_permissions": [{"role": "user", "permission": {"check": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_CREATE"}}]}}}, "columns": ["sku", "subcategory_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["sku", "subcategory_id"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_READ"}}]}}}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["subcategory_id"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_CREATE"}}]}}}, "check": {}}}]}, {"table": {"name": "product_value", "schema": "eav"}, "object_relationships": [{"name": "attribute_value", "using": {"foreign_key_constraint_on": "attribute_value_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_CREATE"}}]}}}, "columns": ["sku", "attribute_value_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["attribute_value_id", "sku"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_READ"}}]}}}, "allow_aggregations": true}}], "delete_permissions": [{"role": "user", "permission": {"filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_VALUE_DELETE"}}]}}}}}], "event_triggers": [{"name": "eav_product_value", "definition": {"delete": {"columns": "*"}, "enable_manual": true, "insert": {"columns": "*"}, "update": {"columns": ["attribute_value_id"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 90}, "webhook_from_env": "ERP_WEBHOOK_URL", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "ERP_WEBHOOK_SECRET"}]}]}, {"table": {"name": "subcategory", "schema": "eav"}, "insert_permissions": [{"role": "user", "permission": {"check": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_FILTER_UPDATE"}}]}}}, "columns": ["subcategory_id", "use_filters"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["subcategory_id", "use_filters"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_READ"}}]}}}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["use_filters"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_FILTER_UPDATE"}}]}}}, "check": null}}], "event_triggers": [{"name": "eav_subcategory", "definition": {"enable_manual": true, "insert": {"columns": "*"}, "update": {"columns": ["use_filters"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 90}, "webhook_from_env": "ERP_WEBHOOK_URL", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "ERP_WEBHOOK_SECRET"}]}]}, {"table": {"name": "subcategory_attribute", "schema": "eav"}, "object_relationships": [{"name": "attribute", "using": {"foreign_key_constraint_on": "attribute_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_CATEGORY_ASSOCIATION_CREATE"}}]}}}, "columns": ["attribute_id", "display_order", "filter_status", "subcategory_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["filter_status", "display_order", "attribute_id", "subcategory_id"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_READ"}}]}}}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["display_order", "filter_status"], "filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_FILTER_UPDATE"}}]}}}, "check": null}}], "delete_permissions": [{"role": "user", "permission": {"filter": {"_exists": {"_table": {"name": "vw_account_permission", "schema": "permission"}, "_where": {"_and": [{"account_id": {"_eq": "X-Hasura-User-Id"}}, {"scoped_permission": {"_eq": "erp.ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE"}}]}}}}}], "event_triggers": [{"name": "eav_subcategory_attribute", "definition": {"delete": {"columns": "*"}, "enable_manual": false, "update": {"columns": ["filter_status", "display_order"]}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 90}, "webhook_from_env": "ERP_WEBHOOK_URL", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "ERP_WEBHOOK_SECRET"}]}]}, {"table": {"name": "content", "schema": "knowledge_base"}, "object_relationships": [{"name": "entry", "using": {"foreign_key_constraint_on": "entry_id"}}, {"name": "owner", "using": {"manual_configuration": {"column_mapping": {"created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["body", "created_by", "entry_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["entry_id"], "filter": {}}}]}, {"table": {"name": "entry", "schema": "knowledge_base"}, "object_relationships": [{"name": "created_by_user", "using": {"manual_configuration": {"column_mapping": {"created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}], "array_relationships": [{"name": "contents", "using": {"foreign_key_constraint_on": {"column": "entry_id", "table": {"name": "content", "schema": "knowledge_base"}}}}, {"name": "pages", "using": {"foreign_key_constraint_on": {"column": "entry_id", "table": {"name": "page", "schema": "knowledge_base"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["created_by", "name"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["entry_id", "created_at", "name", "created_by"], "filter": {}, "allow_aggregations": true}}], "update_permissions": [{"role": "user", "permission": {"columns": ["name"], "filter": {}, "check": {}}}]}, {"table": {"name": "page", "schema": "knowledge_base"}, "object_relationships": [{"name": "entry", "using": {"foreign_key_constraint_on": "entry_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["entry_id", "url"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["entry_id", "url"], "filter": {}}}]}, {"table": {"name": "vw_active_content", "schema": "knowledge_base"}, "object_relationships": [{"name": "content_created_by_user", "using": {"manual_configuration": {"column_mapping": {"content_created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}, {"name": "entry_created_by_user", "using": {"manual_configuration": {"column_mapping": {"entry_created_by": "owner_id"}, "insertion_order": null, "remote_table": {"name": "owner", "schema": "permission"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["entry_id", "content_created_at", "entry_created_at", "body", "name", "content_created_by", "entry_created_by"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "owner", "schema": "permission"}, "object_relationships": [{"name": "account", "using": {"manual_configuration": {"column_mapping": {"owner_id": "account_id"}, "insertion_order": null, "remote_table": {"name": "account", "schema": "account"}}}}, {"name": "application", "using": {"manual_configuration": {"column_mapping": {"owner_id": "app_id"}, "insertion_order": null, "remote_table": {"name": "app_authorization", "schema": "account"}}}}, {"name": "role", "using": {"manual_configuration": {"column_mapping": {"owner_id": "role_id"}, "insertion_order": null, "remote_table": {"name": "role", "schema": "account"}}}}], "array_relationships": [{"name": "permissions", "using": {"manual_configuration": {"column_mapping": {"owner_id": "owner_id"}, "insertion_order": null, "remote_table": {"name": "vw_permission", "schema": "permission"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["is_active", "meta", "type", "inherited_roles", "owner_id"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "owner_permission", "schema": "permission"}, "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": ["owner_id", "permission_id", "scope"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["owner_id", "permission_id", "scope"], "filter": {}, "allow_aggregations": true}}], "delete_permissions": [{"role": "user", "permission": {"filter": {}}}], "event_triggers": [{"name": "permission_owner_permission", "definition": {"delete": {"columns": "*"}, "enable_manual": false, "insert": {"columns": "*"}, "update": {"columns": "*"}}, "retry_conf": {"interval_sec": 10, "num_retries": 0, "timeout_sec": 60}, "webhook": "{{HAL_WEBHOOK_URL}}", "headers": [{"name": "X-Webhook-Auth-Secret", "value_from_env": "HAL_WEBHOOK_SECRET"}]}]}, {"table": {"name": "vw_account_permission", "schema": "permission"}}, {"table": {"name": "vw_permission", "schema": "permission"}, "select_permissions": [{"role": "user", "permission": {"columns": ["description", "inherited_from", "is_active", "is_assigned", "owner_id", "permission_id", "scope", "scoped_permission", "type"], "filter": {}, "allow_aggregations": true}}]}, {"table": {"name": "parameter", "schema": "system"}, "select_permissions": [{"role": "user", "permission": {"columns": ["description", "value", "name"], "filter": {}}}], "update_permissions": [{"role": "user", "permission": {"columns": ["value"], "filter": {}, "check": null}}]}, {"table": {"name": "vw_system_parameter_email", "schema": "system"}, "select_permissions": [{"role": "user", "permission": {"columns": ["description", "value", "max_created_at", "name"], "filter": {}}}]}], "functions": [{"function": {"name": "search_attributes", "schema": "eav"}}], "configuration": {"connection_info": {"database_url": {"from_env": "ERP_DATABASE_URL"}, "isolation_level": "read-committed", "pool_settings": {"connection_lifetime": 600, "idle_timeout": 60, "max_connections": 10, "retries": 1}, "use_prepared_statements": true}}}]}}